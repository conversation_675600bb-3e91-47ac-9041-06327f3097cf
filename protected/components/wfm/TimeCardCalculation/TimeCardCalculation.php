<?php

use Components\Core\Calculator\IntervalIntersectionCalculator;
use Components\Core\Descriptor\FromToDescriptor;
use Components\Core\Descriptor\IntervalIntersectionDescriptor;
use Components\Core\Enum\AppConfigEnum;
use Components\Core\Enum\CommonFieldEnum;
use Components\TimeCardCalculationCore\Builder\EmployeesRegistrationsDescriptorFromDatabaseBuilder;
use Components\TimeCardCalculationCore\Descriptor\EmployeesRegistrationsDescriptor;
use Components\TimeCardCalculationCore\Descriptor\ErrorDefinitionsDescriptor;
use Components\TimeCardCalculationCore\Enum\EmployeeCalcFieldEnum;
use Components\TimeCardCalculationCore\Enum\ErrorDefinitionEnum;
use Components\TimeCardCalculationCore\Enum\ErrorDefinitionKeyEnum;
use Components\TimeCardCalculationCore\Enum\TimeCardCalculationEnum;
use Components\TimeCardCalculationCore\Enum\WorktimeRoundTypeEnum;
use Components\TimeCardCalculationCore\Guesser\RegsFilterOnlyFirstInLastOutRuleGuesser;
use Components\TimeCardCalculationCore\Provider\PreviousEmployeesRegistrationsProvider;
use Components\TimeCardCalculationCore\Service\TimeCardCalculationDataService;
use Components\WorkScheduleCore\Builder\EmployeeExtraHourFilterDescriptorBuilder;
use Components\WorkScheduleCore\Builder\EmployeeExtraHoursCollectionDescriptorBuilder;
use Components\WorkScheduleCore\Descriptor\EmployeeExtraHoursCollectionDescriptor;
use Components\WorkScheduleCore\Enum\EmployeeExtraHourInsideGroupEnum;
use Components\WorkScheduleCore\Enum\EmployeeExtraHourSignEnum;
use Components\WorkScheduleCore\Enum\WorkScheduleUsedTypeOfDayTypeSourceEnum;
use Components\WorkScheduleCore\Provider\EmployeeExtraHourConfigProvider;

Yang::loadComponentNamespaces('Core');
Yang::loadComponentNamespaces('WorkScheduleCore');
Yang::loadComponentNamespaces('TimeCardCalculationCore');

class TimeCardCalculation
{
    public const RUN_TYPE_ONLY_MEMORY = 'onlyMemory';
    public const RUN_TYPE_WORK_SCHEDULE_BY_WORK_TIME = 'workScheduleByWorkTime';


    public const NMB_EVENT_TYPE_ID = 'NMB';
    public const NMK_EVENT_TYPE_ID = 'NMK';

    public const CALC_ERROR = 'errorDescriptor';

    public const SIGN_WORKTIME = 'wt';
    public const SIGN_OVERTIME = 'ot';

    public const DEBUG_LEVEL_NONE = 'DebugLevelNone';
    public const DEBUG_LEVEL_TIME_MEASURE = 'DebugLevelTimeMeasure';
    public const DEBUG_LEVEL_FULL = 'DebugLevelFull';
    public const DEBUG_INFO_TO_APPLICATION_LOG = self::DEBUG_LEVEL_NONE; // self::DEBUG_LEVEL_TIME_MEASURE;

    public const MEASURE_WIDTH_DAILY = '';
    public const DEBUG_INFO_TO_APPLICATION_MEASURE = self::DEBUG_LEVEL_NONE;

    public const DEFAULT_DATE_TIMEZONE = 'UTC';

    public const REGISTRATION_IS_USED_IN_CALCULATION = 0;
    public const REGISTRATION_IS_NOT_USED_IN_CALCULATION = 4;

    public const WORKGROUP_SHOW_ROUND_TYPES_ROUND = 1;

    public const SIGN_MODIFIER_CHAR = '#';
    public const SIGN_MODIFIER_ACCEPTED_OVERTIME = self::SIGN_MODIFIER_CHAR . 'AcceptedOvertime';

    public const CALC_STATUS_OK = 0;

    public const MARK = 'mark';
    public const COST_CHANGE = 'costChange';
    public const LOCKED = 'locked';
    public const WORKTIME = 'worktime';
    public const STATES = 'states';
    public const STATES_STATE = 'state';
    public const STATES_STATUS = 'status';
    public const STATES_WORK_IS_ALL_DAY_NAME = 'workisalldayname';
    public const STATES_WORK_IS_ALL_DAY_SHORT_NAME = 'workisalldayshort';
    public const STATES_VALUE = 'value';
    public const STATES_COST_ID = 'costid';
    public const STATES_COST_CENTER_ID = 'costcenterid';

    private int $MIN_SECONDS_IN_INSIDE_TYPE = 3600;

    private array $ORDINARY_TIME_NOT_VIOLATION_EVENT_TYPE_ID = [
        'EBK',
        'HTK',
        'HTV',
        'CSK',
        'CSB',
        'TCK',
        'TCB',
        'CigiB',
        'CigiK',
        'TSK',
        'TSB',
        'TSZKK',
        'TSZKB',
        'HOB',
        'HOK',
        'SZUK',
        '2UK',
        'KMK',
        'KK'
    ];
    private array $EVENT_TYPE_EXCEPTION_BY_WORKGROUP = [
        'SHIFT' => [
            'CigiB' => true,
            'CigiK' => true,
        ],
    ];

    private array $SHIFT_TYPE = [
        'de' => 'used_work_time_day_shift',
        'du1' => 'used_work_time_evening_shift_1',
        'du2' => 'used_work_time_evening_shift_2',
        'ej' => 'used_work_time_night_shift',
    ];

    private string $startDate;
    private string $endDate;
    private array $employeeContractIds;

    private string $employeeContractId;
    private string $dayToProcess;
    private string $yesterday;
    private bool $isPreCalculated;
    private int $dayToProcessTs;
    private array $dayTypeRows;
    private array $regRow;
    private array $break_notes;
    private string $createdBy;

    public string $messages;

    /** @var ErrorDefinitionsDescriptor[] $errorDescriptors */
    private array $errorDescriptors;

    private StateMachineGet $stateMachine;
    private GStateType $stateType;
    private GetInsideType $insideType;
    private EmployeesRegistrationsDescriptor $registrations;
    private GetPublicHoliday $publicHolidays;
    private array $publicHolidaysThisCountry;
    private GetEmployeeWorkSchedule $employeeWorkSchedule;
    private array $downtimeWorkstartWorkend;
    private EmployeesWithMainData $employeesWithMainData;
    private array $employeeMainData;
    private GetActiveCards $employeeCards;
    private array $employeeAbsences;
    private GetCostCenter $costCenter;
    private GetRegCosts $getRegCosts;
    private GetEmployeeExtraHours $oldEmployeeExtraHours;
    private EmployeeExtraHoursCollectionDescriptor $employeeExtraHours;
    private GetLinkGroupToTerminal $regFilterByLinkGroupToTerminal;
    private string $LinkGroupToTerminalRelatedId;
    private int $backTimeToNewInsideType;
    private array $allEmployeeCalcData = [];
    private string $fromRunType;

    private array $employeeCalcResults;
    private array $dayTypeBreaksTime;

    private array $calc;
    private array $dayProperties;
    private bool $isAbsenceExists;
    private bool $isAbsenceDraft;
    private bool $isAbsenceFullDay;

    private bool $ABSENCE_HOUR_MODE;
    private string $COST_MODE;
    private bool $COST_END_CHANGED;
    private array $PAID_PUBLIC_HOLIDAY_TYPES;
    private string $WORK_STATE_TYPE_ID;
    private string $UNDISCLOSED_STATE_TYPE_ID;
    private string $GONEHOME_STATE_TYPE_ID;
    private string $BREAKTIME_STATE_TYPE_ID;
    private int $ENTRY_MOVE_IN_SECONDS;
    private int $EXIT_MOVE_IN_SECONDS;
    private int $CALCULATION_TIME_BETWEEN_DAY_TYPES;
    private bool $CAME_BACK;
    private bool $OVERTIME_AFTER_WORKTIME;
    private int $MIN_OVERTIME_BEFORE_WORK;
    private bool $CAME_IN;
    private int $NIGHT_SHIFT_COMPENSATORY_TIME;
    private string $OVERTIME_MODE;
    private bool $OT_MGMT_REST_DAY_USE_INTERVAL;
    private bool $STANDBY_MODE;
    private string $APP_SETTINGS_CALC_DAILY_WORKTIME_PLACE;
    private bool $SPLIT_DAYTYPE_TOLERANCE;
    private bool $WORKGROUP_SHOW_ROUND_TYPES;
    private bool $FLEXIBLE_USE_STANDBY_IN_OVERTIME_CALC;
    private bool $SHIFT_START_IN_WORKGROUP;
    private bool $SHIFT_START_IN_DAYTYPE;
    private bool $USING_LOST_TIME_TYPE;
    private bool $USING_LOST_TIME_TYPE_IN_REST_DAY;
    private bool $SAVING_UNDISCLOSED_TO_EMPLOYEE_CALC;
    private bool $SAVING_OTHER_STATES;
    private bool $BOT_AUTOMATIC_ACCEPT;
    private bool $BOT_ACCEPT_BY_OT_MANAGEMENT;
    private bool $BOT_ACCEPT_BY_OT_MANAGEMENT_TO_OT;
    private bool $STANDBY_BOT_ACCEPT_AND_CHANGE_TO_OT;

    private array $RULE_INDICATION;
    private int $RULE_REGS_FILTER_MIN_TIME_BETWEEN_REGS;
    private string $RULE_REGS_FILTER_ONLY_FIRST_IN_LAST_OUT;
    private array $RULE_REGS_FILTER_ONLY_FIRST_IN_LAST_OUT_EXCEPT_EVENT_TYPE_ID;
    private bool $WG_FILO_FILTER;
    private int $RULE_REGS_FILTER_TERMINAL_BEFORE_OUT_TIME;
    private int $RULE_REGS_FILTER_TERMINAL_AFTER_OUT_TIME;
    private bool $RULE_REGS_FILTER_FLEXIBLE_BETWEEN_EAT_LDT;
    private bool $RULE_REGS_FILTER_FRAMEWORK_BALANCE_BETWEEN_EAT_LDT;
    private string $RULE_REGS_FILTER_EXCEPTION;
    private string $RULE_REGS_NO_CAUSE;
    private string $RULE_REGS_NO_CAUSE_FLEXIBLE;
    private bool $RULE_DAILY_BREAK_TIME;
    private bool $RULE_DAILY_BREAK_TIME_WITH_GONE_HOME;
    private bool $RETURN_A_BREAK_AFTER_WORKTIME_ONLY_FROM_BEING_OUT_AFTER_THE_END_OF_WORKTIME;
    private bool $RULE_WORKTIME_IN_POST;
    private string $RULE_WORK_TYPE_WITHOUT_ACS_PAYROLL;
    private bool $DEDUCTION_OF_BREAKS_OUTSIDE_WORKING_HOURS;
    private bool $WORK_SCHEDULE_OUT_OF_TIME_CHANGE_IN_HOUR;
    private bool $HOLIDAY_AS_WORKDAY;
    private bool $BALANCE_ROUND_TOLERANCE;
    private bool $USED_WORKTIME_FROM;
    private bool $ADD_ABSENCE_TO_BALANCE;
    private bool $OVERTIME_BEFORE_UNSET;
    private bool $BALANCE_TIME_BACK;
    private bool $STANDBY_DAYTYPE_BREAKTIMES_OVERLAP;
    private bool $REST_DAY_BALANCE_TO_OVERTIME;
    private string $SETTING_4_REG_TURNED_ON;
    private string $SETTING_4_REG_WORKGROUP;
    private string $SETTING_4_REG_DAY_TYPES;
    private string $AUTOMATIC_REGS_MODIFY_ABSENCE_CASE;
    private string $CALC_INTERVAL_LOST_TIME_BACK_INSIDE_TYPE_LIST;
    private bool $STANDBY_SIGN;
    private bool $RULE_DAILY_BREAK_TIME_SUB_BACKWARDS;
    private bool $OVERTIME_BEFORE_WORKTIME_FLEX;
    private bool $INFORMAL_GOT_ABSENCE_AND_REGS_ERROR;


    private array $measures;
    private float $measuresStartTime;
    private float $measuresFilterTime;

    private int $DE_START;
    private int $DU_START1;
    private int $DU_START2;
    private int $EJ_START;
    private int $EJ_STOP;

    private int $numberOfEmployee;
    private bool $showStand;
    private array $payrollCalcOption;

    private string $WfmDateTimeFormat;
    private string $DATE_TIMEZONE;
    private bool $AUTOMATIC_REGS_OTHER_REGS_RULE;

    private bool $useExternalRegistrationSource;

    private IntervalIntersectionCalculator $intervalIntersectionCalculator;
    private EmployeeExtraHourConfigProvider $employeeExtraHourConfigProvider;
    private EmployeesRegistrationsDescriptorFromDatabaseBuilder $employeesRegistrationsDescriptorBuilder;
    private PreviousEmployeesRegistrationsProvider $previousEmployeesRegistrationsProvider;

    /** @var EmployeeExtraHourConfig[] $standbyTypes */
    private array $standbyTypes;
    private array $employeeCalcHash;
    private array $writeCalculateDataToDb = [];

    public function __construct(
        array $employeeContractIds,
        string $startDate,
        string $endDate,
        bool $showStand = false,
        string $runType = 'normal',
        bool $init = true
    ) {
        $this->createdBy = userID() ?? 'calculator';
        $this->intervalIntersectionCalculator = new IntervalIntersectionCalculator();
        $this->employeeExtraHourConfigProvider = new EmployeeExtraHourConfigProvider();
        $this->employeesRegistrationsDescriptorBuilder = new EmployeesRegistrationsDescriptorFromDatabaseBuilder();
        $this->previousEmployeesRegistrationsProvider = new PreviousEmployeesRegistrationsProvider();

        $this->useExternalRegistrationSource = false;

        date_default_timezone_set(self::DEFAULT_DATE_TIMEZONE);
        if ($init) {
            $this->init($employeeContractIds, $startDate, $endDate, $showStand, $runType);
        }
    }

    public function init(
        array $employeeContractIds,
        string $startDate,
        string $endDate,
        bool $showStand = false,
        string $runType = 'normal'
    ): self {
        $this->employeeContractIds = $employeeContractIds;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->fromRunType = $runType;

        $dayBeforeStartDay = date(AppConfigEnum::DATE_FORMAT, strtotime($this->startDate . ' -1 day'));
        $nextDayAfterEnd = date(AppConfigEnum::DATE_FORMAT, strtotime($this->endDate . ' +1 day'));

        $this->numberOfEmployee = count($this->employeeContractIds);
        $this->showStand = $showStand;

        $this->checkInputs();

        $this->measureInit();

        $this->getAppSettings();

        $this->measure('AppSettings', 'init');

        $this->stateMachine = new StateMachineGet($this->startDate, $this->endDate);
        if (!$this->stateMachine->isStateMachineResults()) {
            $errorDescriptor = new ErrorDefinitionsDescriptor(
                ErrorDefinitionEnum::STATE_MACHINE_NOT_FOUND,
                ['StartDate' => $this->startDate, 'EndDate' => $this->endDate]
            );
            $message = 'Error: Init TimeCardCalculation - ' . $errorDescriptor->formatMessage();
            Yang::log($message, Yang::LOGLEVEL_ERROR, 'system.Calculation');
            $this->errorDescriptors[] = $errorDescriptor;
            return $this;
        }
        $this->measure('stateMachine', 'init');
        $this->stateType = new GStateType($this->startDate, $this->endDate);

        if (!$this->stateType->isStateTypes()) {
            $errorDescriptor = new ErrorDefinitionsDescriptor(
                ErrorDefinitionEnum::STATE_TYPE_NOT_FOUND,
                ['StartDate' => $this->startDate, 'EndDate' => $this->endDate]
            );
            $message = 'Error: Init TimeCardCalculation - ' . $errorDescriptor->formatMessage();
            Yang::log($message, Yang::LOGLEVEL_ERROR, 'system.Calculation');
            $this->errorDescriptors[] = $errorDescriptor;
            return $this;
        }
        $this->measure('stateType', 'init');
        $this->insideType = new GetInsideType($this->startDate, $this->endDate);

        $insideTypes = $this->insideType->getinsideTypes();

        if (empty($insideTypes)) {
            $errorDescriptor = new ErrorDefinitionsDescriptor(
                ErrorDefinitionEnum::INSIDE_TYPE_NOT_FOUND,
                ['StartDate' => $this->startDate, 'EndDate' => $this->endDate]
            );
            $message = 'Error: Init TimeCardCalculation ' . $errorDescriptor->formatMessage();
            Yang::log($message, Yang::LOGLEVEL_ERROR, 'system.Calculation');
            $this->errorDescriptors[] = $errorDescriptor;
            return $this;
        }
        $this->measure('insideType', 'init');
        $this->publicHolidays = new GetPublicHoliday($dayBeforeStartDay, $nextDayAfterEnd);

        $publicHolidays = $this->publicHolidays->getPublicHolidays();

        if (empty($publicHolidays)) {
            if (self::DEBUG_INFO_TO_APPLICATION_LOG === self::DEBUG_LEVEL_FULL) {
                $message = 'Log: Init TimeCardCalculation publicHoliday table query results is empty. ' .
                    '(startDate: ' . $this->startDate . '; endDate: ' . $this->endDate . ').';
                Yang::log($message, Yang::LOGLEVEL_LOG, 'system.Calculation');
            }
        }
        $this->measure('publicHolidays', 'init');
        $this->costCenter = new GetCostCenter($this->startDate, $this->endDate);

        $costCenters = $this->costCenter->getCostCenters();

        if (empty($costCenters)) {
            $errorDescriptor = new ErrorDefinitionsDescriptor(
                ErrorDefinitionEnum::COST_CENTER_NOT_FOUND,
                ['StartDate' => $this->startDate, 'EndDate' => $this->endDate]
            );
            $message = 'Error: Init TimeCardCalculation - ' . $errorDescriptor->formatMessage();
            Yang::log($message, Yang::LOGLEVEL_ERROR, 'system.Calculation');
            $this->errorDescriptors[] = $errorDescriptor;
            return $this;
        }
        $this->measure('costCenter', 'init');
        $this->employeeWorkSchedule = new GetEmployeeWorkSchedule(
            $dayBeforeStartDay,
            $nextDayAfterEnd,
            $this->employeeContractIds,
            true/*$skipCheckApprovers*/
        );
        $this->measure('employeeWorkSchedule', 'init');

        if ($this->WORK_SCHEDULE_OUT_OF_TIME_CHANGE_IN_HOUR) {
            $employeeWorkSchedule96hours = new GetEmployeeWorkSchedule(
                $dayBeforeStartDay,
                $nextDayAfterEnd,
                $this->employeeContractIds,
                true
                /*$skipCheckApprovers*/,
                'workSchedule',
                '96'
            );
            $this->downtimeWorkstartWorkend = $this->employeeWorkSchedule->set96hour(
                $employeeWorkSchedule96hours->get()
            );
        }
        $this->employeeExtraHours = (new EmployeeExtraHoursCollectionDescriptorBuilder())
            ->setFilterDescriptor(
                (new EmployeeExtraHourFilterDescriptorBuilder())
                    ->reset()
                    ->setEmployeeContractIds($this->employeeContractIds)
                    ->setFrom(DateTime::createFromFormat(AppConfigEnum::DATE_FORMAT, $dayBeforeStartDay))
                    ->setTo(DateTime::createFromFormat(AppConfigEnum::DATE_FORMAT, $nextDayAfterEnd))
                    ->build()
            )
            ->build();

        $this->oldEmployeeExtraHours = new GetEmployeeExtraHours(
            $dayBeforeStartDay,
            $nextDayAfterEnd,
            $this->employeeContractIds,
            true/*$skipCheckApprovers*/
        );
        $this->measure('employeesExtraHours', 'init');

        $filter = ['employee_contract_id' => $this->employeeContractIds];
        $this->employeesWithMainData = new EmployeesWithMainData(
            $dayBeforeStartDay, $nextDayAfterEnd, $filter
        );
        $this->measure('employeesWithMainData', 'init');

        $this->employeeCards = new GetActiveCards(
            $this->employeeContractIds,
            $dayBeforeStartDay,
            $nextDayAfterEnd
        );

        $employeeCalc = new GetEmployeeCalc();
        $employeeCalcStatus = '5,6'; //Saved, Locked
        $this->employeeCalcResults = $employeeCalc->get(
            $this->startDate,
            $this->endDate,
            $this->employeeContractIds,
            $employeeCalcStatus,
            true
        );
        $this->measure('employeeCalcResults', 'init');

        $employeeCalcStatus = '1,2';
        $this->employeeCalcHash[TimeCardCalculationEnum::EMPLOYEE_CALC] = $employeeCalc->getResultOnlyHash(
            $this->startDate,
            $this->endDate,
            $this->employeeContractIds,
            $employeeCalcStatus
        );

        $this->employeeCalcHash[TimeCardCalculationEnum::EMPLOYEE_CALC_USED_DAY_TYPE] =
            GetEmployeeCalcUsedDaytype::getResultOnlyHash(
                $this->startDate,
                $this->endDate,
                $this->employeeContractIds,
                $employeeCalcStatus
            );

        $employeeCalcMessage = new GetEmployeeCalcMessage();
        $this->employeeCalcHash[TimeCardCalculationEnum::EMPLOYEE_CALC_MESSAGE] =
            $employeeCalcMessage->getResultOnlyHash(
                $this->startDate,
                $this->endDate,
                $this->employeeContractIds,
                $employeeCalcStatus
            );

        $employeeCalcOtherState = new GetEmployeeCalcOtherStates();
        $this->employeeCalcHash[TimeCardCalculationEnum::EMPLOYEE_CALC_OTHER_STATES] =
            $employeeCalcOtherState->getResultOnlyHash(
                $this->startDate,
                $this->endDate,
                $this->employeeContractIds,
                $employeeCalcStatus
            );

        $this->regFilterByLinkGroupToTerminal = new GetLinkGroupToTerminal();
        $this->LinkGroupToTerminalRelatedId = $this->regFilterByLinkGroupToTerminal->getRelatedGroupId();
        $this->measure('regFilterByLinkGroupToTerminal', 'init');

        $gea = new GetEmployeeAbsences();
        $this->employeeAbsences = $gea->get($this->startDate, $this->endDate, $this->employeeContractIds);
        if (weHaveModule('ttwa-pcs')) {
            $this->getRegCosts = new GetRegCosts($this->startDate, $this->endDate);
        }
        if (!$this->isUseExternalRegistrationSource()) {
            $this->measure('EmployeesRegistrations', 'init');
            $this->registrations = $this->employeesRegistrationsDescriptorBuilder
                ->setEmployeeContractIds($this->employeeContractIds)
                ->setExitedEmployees($this->employeesWithMainData->getQuitEmployees())
                ->setStartDate($dayBeforeStartDay)
                ->setEndDate($nextDayAfterEnd)
                ->build();
        }
        $this->measure('employeeExtraHourConfigProvider', 'init');
        $this->standbyTypes = $this->employeeExtraHourConfigProvider->provide(
            DateTime::createFromFormat(AppConfigEnum::DATE_FORMAT, $dayBeforeStartDay),
            DateTime::createFromFormat(AppConfigEnum::DATE_FORMAT, $nextDayAfterEnd),
            [EmployeeExtraHourInsideGroupEnum::STAND_BY]
        );

        $this->measure('activities', 'init');
        return $this;
    }

//--------------------------------------------------------------------------------------------
    public function payrollcalculation($payrollCalcOptionString = 'NO_OPTION'): void
    {
        $this->payrollCalcOption = explode('_AND_', $payrollCalcOptionString);

        $readyNumberOfEmployee = 0;
        $this->messages = '';
        $messageId = 1;
        foreach ($this->employeeContractIds as $this->employeeContractId) {
            $this->payrollCalculationEmployee();

            $this->setMessages($messageId);

            if ($this->showStand) {
                ++$readyNumberOfEmployee;
                echo "$readyNumberOfEmployee/$this->numberOfEmployee " .
                    Employee::getEmployeeFullnameEmpIdEcNumberByEcID(
                        $this->employeeContractId,
                        Dict::getLang()
                    ) . '<br/>';
                flush();
            }
        }
        $payrollSave = new TimeCardCalculationDataService();
        $payrollSave->processEmployeeCalcData($this->writeCalculateDataToDb, $this->employeeCalcHash);

        if (self::DEBUG_INFO_TO_APPLICATION_LOG === self::DEBUG_LEVEL_TIME_MEASURE ||
            self::DEBUG_INFO_TO_APPLICATION_LOG === self::DEBUG_LEVEL_FULL) {
            $message = "Info: Payroll calculation time measure: \n " . json_encode($this->measures);
            Yang::log($message, 'info', 'system.Calculation');
        }
    }

//--------------------------------------------------------------------------------------------
    public function payrollCalculationEmployee(): void
    {
        $this->measureInit();

        unset($this->calc);
        unset($this->dayProperties);
        unset($this->dayTypeRows);

        $this->isPreCalculated = false;

        if (isset($this->yesterday)) {
            unset($this->yesterday);
        }
        $this->dayToProcess = $this->startDate;
        $this->dayToProcessTs = strtotime($this->dayToProcess);
        $toWorkInterval = 0;
        $employeeState = $this->GONEHOME_STATE_TYPE_ID;
        $decadeStopTs = strtotime($this->endDate);
        $this->measure('Employee init:', 'employeeContractId:' . $this->employeeContractId);

        while ($this->dayToProcessTs <= $decadeStopTs) {
            if (self::DEBUG_INFO_TO_APPLICATION_MEASURE === self::MEASURE_WIDTH_DAILY) {
                $this->measure('Daily (' . $this->dayToProcess . '): Start', 'Day:' . $this->dayToProcess);
            }
            $employeeMainData = $this->employeesWithMainData->get($this->employeeContractId, $this->dayToProcess);
            if (!$this->employeeMainDataCheck($employeeMainData)) {
                $this->setNexDayToProcess();
                continue;
            }
            $this->employeeMainData = $employeeMainData;

            if ($this->calcAutoLock()) {
                $this->setNexDayToProcess();
                continue;
            }
            $this->calc[$this->dayToProcess][self::LOCKED] = false;
            $this->isAbsenceExists = false;
            $this->isAbsenceDraft = false;
            $this->isAbsenceFullDay = true;

            $absence = &$this->employeeAbsences[$this->employeeContractId][$this->dayToProcess];

            if (isset($absence['state_type_id'])) {
                $this->isAbsenceFullDay = ((int)$absence[GetEmployeeAbsences::FULL_DAY_ABSENCE]) > 0;

                switch ($absence['status']) {
                    case Status::PUBLISHED:
                    case Status::DRAFT_DELETE:
                        $this->isAbsenceExists = true;
                        break;
                    case Status::DRAFT:
                        $this->isAbsenceDraft = true;
                        break;
                }
            }

            $this->publicHolidaysThisCountry = $this->publicHolidays->getPublicHolidaysByCountry(
                $this->employeeMainData['country']
            );
            $this->loadDayTypeRowsIfNotExists();

            if (self::DEBUG_INFO_TO_APPLICATION_MEASURE === self::MEASURE_WIDTH_DAILY) {
                $this->measure('Daily (' . $this->dayToProcess . '): daytype_var', 'Day:' . $this->dayToProcess);
            }
            if (isset($this->dayTypeRows[$this->dayToProcess]) and is_array($this->dayTypeRows[$this->dayToProcess])) {
                foreach ($this->dayTypeRows[$this->dayToProcess] as $key => $value) {
                    $$key = $value;
                }
            } else {
                $this->setNexDayToProcess();
                continue;
            }
            if (self::DEBUG_INFO_TO_APPLICATION_MEASURE === self::MEASURE_WIDTH_DAILY) {
                $this->measure('Daily (' . $this->dayToProcess . '): daytype_var to var', 'Day:' . $this->dayToProcess);
            }

            if (isset($this->dayTypeRows['used_pre_day']) and $this->dayTypeRows['used_pre_day']) {
                $pre_day = date(AppConfigEnum::DATE_FORMAT, strtotime($this->dayToProcess . ' -1 day'));
                $this->setDailyConst($pre_day);
            } else {
                $this->setDailyConst($this->dayToProcess);
            }

            if (self::DEBUG_INFO_TO_APPLICATION_MEASURE === self::MEASURE_WIDTH_DAILY) {
                $this->measure('Daily (' . $this->dayToProcess . '): setDailyConst', 'Day:' . $this->dayToProcess);
            }

            $costid = $this->getDefaultCostId();
            $firstregofday = 0;
            $totalWorkTime = 0;

            if ($this->dayTypeRows[$this->dayToProcess]['worktype'] == 'ONLYWORKSCHEDULE'
                and $founddaytype === true
                and $this->isAbsenceExists === false) {
                $workstart = $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
                $workstop = $this->dayTypeRows[$this->dayToProcess]['used_work_end'];
                $work = ($workstop - $workstart);
                $this->setWorktime($costid, $work, $workstart, $workstop);

                $this->calc[$this->dayToProcess]['seldt']['min'] = date(AppConfigEnum::DATE_TIME_FORMAT, $workstart);
                $this->calc[$this->dayToProcess]['seldt']['max'] = date(AppConfigEnum::DATE_TIME_FORMAT, $workstop);

                $this->calc[$this->dayToProcess]['seldt']['min_origi'] = date(
                    AppConfigEnum::DATE_TIME_FORMAT,
                    $workstart
                );
                $this->calc[$this->dayToProcess]['seldt']['max_origi'] = date(
                    AppConfigEnum::DATE_TIME_FORMAT,
                    $workstop
                );
                $this->setRegistrationSelectValues();
                $founddaytype = false;
            } elseif ($towork > (-1) and $founddaytype === true) {
                if ($this->setEmployeeStateTypeIdFirstRunFromPreviuosRegs($employeeState)) {
                    $this->isPreCalculated = true;
                }
                $this->clearEmployeeStateWhenStateStatusIsDaily($employeeState);
                if (self::DEBUG_INFO_TO_APPLICATION_MEASURE === self::MEASURE_WIDTH_DAILY) {
                    $this->measure('Daily (' . $this->dayToProcess . '): preStateTypeId', 'Day:' . $this->dayToProcess);
                }

                $this->setRegistrationSelectValues();

                if (self::DEBUG_INFO_TO_APPLICATION_MEASURE === self::MEASURE_WIDTH_DAILY) {
                    $this->measure('Daily (' . $this->dayToProcess . '): select values', 'Day:' . $this->dayToProcess);
                }

                $tmpRows = $this->registrations->getEmployeeRegistrations(
                    $this->employeeContractId,
                    $this->calc[$this->dayToProcess]['seldt']['min'],
                    $this->calc[$this->dayToProcess]['seldt']['max']
                );
                if (self::DEBUG_INFO_TO_APPLICATION_MEASURE === self::MEASURE_WIDTH_DAILY) {
                    $this->measure(
                        'Daily (' . $this->dayToProcess . '): registrations->get',
                        'Day:' . $this->dayToProcess
                    );
                }
                foreach ($tmpRows as $rowcount => $this->regRow) //töröljük a számolásban nem kellő regiszrációkat, mert bizonyos esetekben a számolásnak szüksége van a következő regisztrációra.
                {
                    if (
                        $this->regRow['calc_status'] === '1'
                        && $this->regRow['status'] != Status::PUBLISHED
                        && $this->regRow['status'] != Status::DRAFT_DELETE
                    ) {
                        unset($tmpRows[$rowcount]);
                        continue;
                    }
                    if ($this->LinkGroupToTerminalRelatedId !== 'NONE') {
                        $groupId = $this->employeeMainData[$this->LinkGroupToTerminalRelatedId];
                        $terminalId = $this->regRow['terminal_id'];
                        $readerId = $this->regRow['reader_id'];
                        if ($this->regFilterByLinkGroupToTerminal->isNotUsed($groupId, $terminalId, $readerId)) {
                            unset($tmpRows[$rowcount]);
                            continue;
                        }
                    }
                }

                $this->workTypeWithoutACS($tmpRows);
                $this->addRegsAbsenceToBalance($tmpRows);
                unset($this->break_notes);
                $stateStartFlag = 0;
                $firstregofday = 0;
                $workisallday = 0;
                $dailyregscount = 0;
                $coststarted['start'] = 0;
                $coststarted['end'] = 0;
                $this->calc[$this->dayToProcess][$this->GONEHOME_STATE_TYPE_ID] = 0;
                $this->calc[$this->dayToProcess]['claimid'] = 0;

                if ($this->optionCheck('COSTCOSTENDREPAIR') === true) {
                    $tmpRows = $this->CostCostEndRepair($tmpRows);
                }
                $tmpRows = array_values($tmpRows);

                $tmpRows = $this->InsertNMBIfNMBPreviousExists($tmpRows);
                $tmpRows = $this->InsertNMKIfNMKLaterExists($tmpRows);
                $tmpRows = $this->addLastNMKRegIfNone($tmpRows);
                $tmpRows = $this->addRegsIfNone($tmpRows);
                $usedRegistrationCounter = 0;

                foreach ($tmpRows as $rowcount => &$this->regRow) {
                    $tmpRowsCount = count($tmpRows);
                    $this->regRow['lastregofday'] = (($rowcount + 1) === $tmpRowsCount);

                    $tmpDate = new DateTime($this->regRow['reg_time'], new DateTimeZone($this->DATE_TIMEZONE));
                    $this->regRow['reg_time_orig_ts'] = $tmpDate->format('U');
                    $this->regRow['reg_time_ts'] = $tmpDate->format('U');
                    $dailyregscount++;
                    $this->calc[$this->dayToProcess]['regscount'] = $dailyregscount;

                    if ($this->WG_FILO_FILTER) {
                        $this->RULE_REGS_FILTER_ONLY_FIRST_IN_LAST_OUT = 'DISABLED';
                        if ($this->dayTypeRows[$this->dayToProcess]['filter_only_filo_regs']) {
                            $this->RULE_REGS_FILTER_ONLY_FIRST_IN_LAST_OUT = 'ENABLEWHERISCALCTRUEINTERMINAL';
                        }
                    }

                    if ($this->ruleCalcModifyRegFilter($rowcount, $tmpRows)) {
                        if ($this->regRow['calc_status'] !== TimeCardCalculation::REGISTRATION_IS_NOT_USED_IN_CALCULATION) {
                            $this->calc[$this->dayToProcess]['regs'][$this->regRow['reg_row_id']]['calc_status'] = TimeCardCalculation::REGISTRATION_IS_NOT_USED_IN_CALCULATION;
                        }
                        continue;
                    }
                    $this->ruleCalcModifyNoCause($usedRegistrationCounter, $tmpRowsCount);
                    $usedRegistrationCounter++;

                    if ($this->regRow['calc_status'] != TimeCardCalculation::REGISTRATION_IS_USED_IN_CALCULATION) {
                        $this->calc[$this->dayToProcess]['regs'][$this->regRow['reg_row_id']]['calc_status'] = TimeCardCalculation::REGISTRATION_IS_USED_IN_CALCULATION;
                    }
                    $this->ruleIndicationMaxLate($dailyregscount);

                    if ($this->dayTypeRows[$this->dayToProcess]['worktype'] === 'INFORMAL') {
                        $this->calc[$this->dayToProcess][self::MARK] = self::CALC_STATUS_OK;
                        if ($firstregofday == 0) {
                            $firstregofday = $this->regRow['reg_time_ts'];
                            $this->dayTypeRows[$this->dayToProcess]['firstreg'] = $this->regRow['reg_time'];
                            $this->setClaimtoCalc($costid, 'wtde', $towork);
                        }
                        continue;
                    }

                    $this->regRow['firstregofday'] = false;
                    if ($firstregofday === 0) {
                        $this->regRow['reg_time_ts'] = $this->setFirstRegModUsedWorkStart(
                            $this->regRow['reg_time_ts'],
                            $this->dayTypeRows[$this->dayToProcess]['used_work_start']
                        );
                        $firstregofday = $this->regRow['reg_time_ts'];
                        $this->dayTypeRows[$this->dayToProcess]['firstreg'] = $this->regRow['reg_time'];
                        $this->regRow['firstregofday'] = true;
                    }

                    if (!isset($preEmployeeStateTypeId)) {
                        $preEmployeeStateTypeId = null;
                    }
                    if (!isset($newemployeestate)) {
                        $newemployeestate = null;
                    }
                    if (isset($this->EVENT_TYPE_EXCEPTION_BY_WORKGROUP[$this->dayTypeRows[$this->dayToProcess]['worktype']][$this->regRow['event_type_id']])) {
                        continue;
                    }
                    [$newemployeestate, $preEmployeeStateTypeId] = $this->stateMachine->getNewStateTypeId(
                        $employeeState,
                        $this->regRow['event_type_id'],
                        $this->dayToProcess
                    );
                    $this->ruleIndicationFirstStateStart(
                        $firstregofday,
                        $employeeState,
                        $newemployeestate,
                        $this->regRow['event_type_id']
                    );

                    if (($newemployeestate !== null)
                        and (!$workisallday or $this->stateType->getStateType(
                                $this->dayToProcess,
                                $newemployeestate,
                                'state_type_status'
                            ) == 2)
                        and (!$workisallday or !$stateStartFlag)
                    ) {
                        if (
                            ($newemployeestate != $employeeState
                                or $this->regRow['event_type_id'] == 'COST'
                                or $this->regRow['event_type_id'] == 'COSTEND'
                            )
                        ) {
                            if ($this->regRow['event_type_id'] === 'COST') {
                                $this->calc[$this->dayToProcess][self::COST_CHANGE] = true;
                            }
                            if ($this->COST_END_CHANGED
                                && $this->regRow['event_type_id'] === 'COSTEND') {
                                $costid = $this->getCostIdFromRegs();
                            }

                            $this->ruleIndicationCameBack($employeeState);

                            $this->regRow['reg_time_ts'] += $this->ruleCalcModifyEntryExitMoveInSeconds();
                            $this->regRow['reg_time_ts'] = $this->ruleCalcModifyEntyExitTolerance();

                            $changeRegToWorkStart = ((int)$this->regRow['from_work_start'] === 1) &&
                                $this->regRow['firstregofday'] &&
                                $this->dayTypeRows[$this->dayToProcess]['used_work_start'] < $this->regRow['reg_time_ts'];
                            $this->regRow['reg_time_ts'] =
                                $changeRegToWorkStart ?
                                    $this->dayTypeRows[$this->dayToProcess]['used_work_start'] :
                                    $this->regRow['reg_time_ts'];

                            $changeRegTimeToWorkEnd =
                                ((int)$this->regRow['to_work_end'] === 1) &&
                                $this->regRow['lastregofday'] &&
                                $this->dayTypeRows[$this->dayToProcess]['used_work_end'] > $this->regRow['reg_time_ts'];
                            $this->regRow['reg_time_ts'] =
                                $changeRegTimeToWorkEnd ?
                                    $this->dayTypeRows[$this->dayToProcess]['used_work_end'] :
                                    $this->regRow['reg_time_ts'];

                            $this->ruleCalcModifyPaidLunchtimeAndWorkStart($employeeState);
                            $FlexibleBetweenEatLdt = ($this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FLEXIBLE'
                                and $this->RULE_REGS_FILTER_FLEXIBLE_BETWEEN_EAT_LDT
                            );
                            $FrameWorkBalanceBetweenEatLdt = ($this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FRAMEWORK_BALANCE'
                                and $this->RULE_REGS_FILTER_FRAMEWORK_BALANCE_BETWEEN_EAT_LDT
                            );
                            $restDayWithoutOvertime = ($this->workScheduleIsRestDay() &&
                                $this->dayTypeRows[$this->dayToProcess]['overtimeType'] !== WorkScheduleUsedTypeOfDayTypeSourceEnum::RESTDAY
                            );

                            if ($FlexibleBetweenEatLdt || $FrameWorkBalanceBetweenEatLdt && !$restDayWithoutOvertime) {
                                $this->regRow['reg_time_ts'] = $this->ruleCalcModifyRegTimeToEatOrLdt();
                            } elseif ($this->dayTypeRows[$this->dayToProcess]['worktype'] !== 'FLEXIBLE') {
                                $this->regRow['reg_time_ts'] = $this->ruleCalcModifyRegTimeToWorkStartStop($tmpRows);
                            }

                            $this->regRow['reg_time_ts'] = $this->overtimeAfterWorktimeFLEX(
                                $this->regRow['reg_time_ts']
                            );

                            $this->ruleIndicationOrdinaryTimeViolation($newemployeestate, $firstregofday);

                            if ($preEmployeeStateTypeId and $firstregofday == 0) { // TODO: Nincs ellenőrizve, hogy az előző státusz munkaidő-e, mert ált nem az
                                $workStart = (isset($this->dayTypeRows[$this->dayToProcess]['used_ordinary_time_start']) and $this->dayTypeRows[$this->dayToProcess]['used_ordinary_time_start'] > 0) ?
                                    $this->dayTypeRows[$this->dayToProcess]['used_ordinary_time_start'] :
                                    $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
                                $this->setClaimtoCalc(
                                    $costid,
                                    $preEmployeeStateTypeId,
                                    abs($this->regRow['reg_time_ts'] - $workStart)
                                );
                            }

                            if ($employeeState <> $this->GONEHOME_STATE_TYPE_ID) {
                                if ($this->stateType->getCalculationStart($employeeState)) {
                                    $workstart = $this->stateType->getCalculationStart($employeeState);
                                    $workstop = $this->regRow['reg_time_ts'];
                                    $work = ((int)$workstop - (int)$workstart);
                                    switch ($this->stateType->getStateType(
                                        $this->dayToProcess,
                                        $employeeState,
                                        'state_type_status'
                                    )) {
                                        case 0:
                                            if (!isset($this->calc[$this->dayToProcess][$employeeState])) {
                                                $this->calc[$this->dayToProcess][$employeeState] = 0;
                                            }
                                            $this->calc[$this->dayToProcess][$employeeState] += $work;
                                            if ($this->stateType->getStateType(
                                                $this->dayToProcess,
                                                $employeeState,
                                                GStateType::WORKTIME
                                            )) {
                                                $totalWorkTime += $work;

                                                $this->increaseToWorkByLunchtime($totalWorkTime, $workstop, $towork);

                                                $this->increaseWorktimeByRules($totalWorkTime, $workstop, $work);

                                                if (!isset($this->calc[$this->dayToProcess]['firstregdt'])) {
                                                    $this->calc[$this->dayToProcess]['firstregdt'] = $this->stateType->getCalculationStart(
                                                        $employeeState
                                                    );
                                                }
                                                $this->calc[$this->dayToProcess]['lastregdt'] = $this->regRow['reg_time_ts'];

                                                if ($work > 0) {
                                                    if ($employeeState !== $this->WORK_STATE_TYPE_ID
                                                        && $employeeState !== 'overtime________________20161018'
                                                        && $this->stateType->getStateType(
                                                            $this->dayToProcess,
                                                            $employeeState,
                                                            'save'
                                                        )
                                                    ) {
                                                        @($this->calc[$this->dayToProcess]['other_state'][$employeeState] += $work);
                                                    }

                                                    if ($employeeState === 'overtime________________20161018') {
                                                        $this->setOvertime($costid, $work, $workstart, $workstop);
                                                    } else {
                                                        if ($this->dayTypeRows[$this->dayToProcess]['worktype'] == 'SHIFT'
                                                            or $this->dayTypeRows[$this->dayToProcess]['worktype'] == 'FRAMEWORK'
                                                            or $this->dayTypeRows[$this->dayToProcess]['worktype'] == 'FRAMEWORK_BALANCE'
                                                            or $this->dayTypeRows[$this->dayToProcess]['worktype'] == 'STANDBYJOB') {
                                                            $this->setWorktimeOvertime(
                                                                $costid,
                                                                $work,
                                                                $workstart,
                                                                $workstop
                                                            );
                                                        } else {
                                                            if (!($this->workScheduleIsRestDay() &&
                                                                    $this->REST_DAY_BALANCE_TO_OVERTIME) &&
                                                                $this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FLEXIBLE'
                                                            ) {
                                                                $remainingWork = $this->SetOvertimeStandByOverlap(
                                                                    $costid,
                                                                    $work,
                                                                    $workstart,
                                                                    $workstop
                                                                );
                                                                foreach ($remainingWork as $value) {
                                                                    if ($this->workScheduleIsRestDay()
                                                                    ) {
                                                                        $this->calc[$this->dayToProcess]['balance'] = $value['work'];
                                                                        $this->setClaimtoCalc(
                                                                            $costid,
                                                                            'balance',
                                                                            $value['work']
                                                                        );
                                                                    } else {
                                                                        $this->setWorktime(
                                                                            $costid,
                                                                            $value['work'],
                                                                            $value['workstart'],
                                                                            $value['workstop']
                                                                        );
                                                                    }
                                                                }
                                                            } else {
                                                                $this->setWorktime(
                                                                    $costid,
                                                                    $work,
                                                                    $workstart,
                                                                    $workstop
                                                                );
                                                            }
                                                        }
                                                    }
                                                }
                                            } elseif ($this->stateType->getStateType(
                                                $this->dayToProcess,
                                                $employeeState,
                                                'save'
                                            )) {
                                                if ($employeeState == $this->BREAKTIME_STATE_TYPE_ID)    // Ebédel
                                                {
                                                    $this->calc[$this->dayToProcess]['lunchtime']['real_lunch_time'] = $work;
                                                } elseif ($this->stateType->getStateType(
                                                    $this->dayToProcess,
                                                    $employeeState,
                                                    'reduce_break_time'
                                                )) {
                                                    $sub = true; //mindig csökkenteni kell a breaktime értékét ha már levontuk
                                                    $tmp = ($this->RULE_DAILY_BREAK_TIME === false) ? $this->getBreakTime(
                                                        $workstart,
                                                        $workstop,
                                                        $sub
                                                    ) : 0;
                                                }

                                                @($this->calc[$this->dayToProcess]['other_state'][$employeeState] += $work);
                                            }
                                            $this->stateType->setCalculationStart($employeeState, 0);
                                            if (!isset($this->calc[$this->dayToProcess][self::MARK]) or $this->calc[$this->dayToProcess][self::MARK] > (0)) {
                                                $this->calc[$this->dayToProcess][self::MARK] = self::CALC_STATUS_OK;
                                            }
                                            break;
                                        case 1:
                                        case 11:
                                            if ($this->stateType->getCalculationState(
                                                    $employeeState
                                                ) == 2) //elozo napon mar
                                            {
                                                $workstart = $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
                                                $this->stateType->setCalculationStart(
                                                    $employeeState,
                                                    $this->dayTypeRows[$this->dayToProcess]['used_work_start']
                                                );
                                                $this->stateType->setCalculationState($employeeState, 0);
                                                if ($work < 0) {
                                                    break;
                                                }
                                            }
                                            if (!isset($this->calc[$this->dayToProcess][$employeeState])) {
                                                $this->calc[$this->dayToProcess][$employeeState] = 0;
                                            }
                                            $this->calc[$this->dayToProcess][$employeeState] += $work;
                                            if ($this->stateType->getStateType(
                                                $this->dayToProcess,
                                                $employeeState,
                                                GStateType::WORKTIME
                                            )) {
                                                $totalWorkTime += $work;
                                                if (!isset($this->calc[$this->dayToProcess]['firstregdt'])) {
                                                    $this->calc[$this->dayToProcess]['firstregdt'] = $this->stateType->getCalculationStart(
                                                        $employeeState
                                                    );
                                                }
                                                $this->calc[$this->dayToProcess]['lastregdt'] = $this->regRow['reg_time_ts'];

                                                if ($this->workScheduleIsRestDay() &&
                                                    $this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FLEXIBLE'
                                                ) {
                                                    $this->calc[$this->dayToProcess]['balance'] = $work;
                                                    $this->setClaimtoCalc($costid, 'balance', $work);
                                                } else {
                                                    if ($this->dayTypeRows[$this->dayToProcess]['worktype'] == 'SHIFT'
                                                        or $this->dayTypeRows[$this->dayToProcess]['worktype'] == 'FRAMEWORK'
                                                        or $this->dayTypeRows[$this->dayToProcess]['worktype'] == 'FRAMEWORK_BALANCE'
                                                        or $this->dayTypeRows[$this->dayToProcess]['worktype'] == 'STANDBYJOB') {
                                                        $this->setWorktimeOvertime(
                                                            $costid,
                                                            $work,
                                                            $workstart,
                                                            $workstop
                                                        );
                                                    } else {
                                                        $this->setWorktime($costid, $work, $workstart, $workstop);
                                                    }
                                                }
                                            } elseif ($this->stateType->getStateType(
                                                $this->dayToProcess,
                                                $employeeState,
                                                'save'
                                            )) {
                                                @($this->calc[$this->dayToProcess]['other_state'][$employeeState] += $work);
                                            }
                                            $this->stateType->setCalculationStart($employeeState, 0);
                                            if (!isset($this->calc[$this->dayToProcess][self::MARK]) or $this->calc[$this->dayToProcess][self::MARK] > (0)) {
                                                $this->calc[$this->dayToProcess][self::MARK] = 1;
                                            }
                                            break;
                                        case 2:
                                            if ($this->stateType->getCalculationState($employeeState) == 0) {
                                                $this->stateType->setCalculationState($employeeState, 1);
                                                $this->stateType->setCalculationStart($employeeState, 1);
                                            } else {
                                                if ($this->stateType->getCalculationState($employeeState) == 1) {
                                                    $this->stateType->setCalculationState($employeeState, 1);
                                                    $this->stateType->setCalculationStart($employeeState, 1);
                                                } else {
                                                    if ($this->stateType->getCalculationState($employeeState) == 2) {
                                                        if ($newemployeestate == $this->GONEHOME_STATE_TYPE_ID
                                                            and $this->dayTypeRows[$this->dayToProcess]['used_work_end'] <= $this->regRow['reg_time_ts']) {//kintre kerul es a munkanap vege utan
                                                            $this->calc[$this->dayToProcess][$employeeState] = 1;//$towork;
                                                            if ($this->stateType->getStateType(
                                                                $this->dayToProcess,
                                                                $employeeState,
                                                                GStateType::WORKTIME
                                                            )) {
                                                                if (!isset($this->calc[$this->dayToProcess]['firstregdt'])) {
                                                                    $this->calc[$this->dayToProcess]['firstregdt'] = $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
                                                                }
                                                                $this->calc[$this->dayToProcess]['lastregdt'] = $this->regRow['reg_time_ts'];
                                                                $this->calc[$this->dayToProcess][self::WORKTIME] = $towork;
                                                            } else {
                                                                $toWorkInterval -= $towork;
                                                                if ($this->stateType->getStateType(
                                                                    $this->dayToProcess,
                                                                    $employeeState,
                                                                    'save'
                                                                )) {
                                                                    @($this->calc[$this->dayToProcess]['other_state'][$employeeState] += $towork);
                                                                }
                                                            }
                                                            $workisallday = 1;
                                                            if (!isset($this->calc[$this->dayToProcess][self::MARK]) or $this->calc[$this->dayToProcess][self::MARK] > 0) {
                                                                $this->calc[$this->dayToProcess][self::MARK] = 2;
                                                            }
                                                            $this->calc[$this->dayToProcess]['workisalldayname'] = $this->stateType->getStateType(
                                                                $this->dayToProcess,
                                                                $employeeState,
                                                                'name_dict_id'
                                                            );
                                                            $this->calc[$this->dayToProcess]['workisalldayshort'] = $this->stateType->getStateType(
                                                                $this->dayToProcess,
                                                                $employeeState,
                                                                'short_name_dict_id'
                                                            );
                                                            $this->calc[$this->dayToProcess]['workisalldayfirst'] = $this->getFirstAllDayAbsence(
                                                                $employeeState,
                                                                $newemployeestate
                                                            );
                                                        }
                                                        $this->stateType->setCalculationState($employeeState, 0);
                                                        $this->stateType->setCalculationStart($employeeState, 0);
                                                    }
                                                }
                                            }
                                            break;
                                    }
                                }
                            } elseif ($this->stateType->getStateType(
                                    $this->dayToProcess,
                                    $newemployeestate,
                                    'state_type_status'
                                ) == 2
                                and $this->dayTypeRows[$this->dayToProcess]['used_work_start'] >= $this->regRow['reg_time_ts']) {
                                $this->stateType->setCalculationState($newemployeestate, 2);
                                $this->calc[$this->dayToProcess]['workisalldayfirst'] = $this->getFirstAllDayAbsence(
                                    $employeeState,
                                    $newemployeestate
                                );
                            } elseif ($this->stateType->getCalculationStart($employeeState) !== 0) {
                                $this->breakTimeFromOutSide($costid, $employeeState);
                                $this->collectDailyGoneHomeState($employeeState);
                            }
                            $stateStartFlag = ($newemployeestate === $this->GONEHOME_STATE_TYPE_ID) ? 0 : 1;
                            if ($this->regRow['event_type_id'] == 'COST') {
                                $coststarted['start'] = 1;
                                $coststarted['end'] = 0;
                                $costid = $this->getCostIdFromRegs();
                            } elseif ($this->regRow['event_type_id'] == 'COSTEND') {
                                $coststarted['end'] = 1;
                                $costid = $this->getDefaultCostId();
                            }
                            $employeeState = $newemployeestate;
                            $this->stateType->setCalculationStart($employeeState, $this->regRow['reg_time_ts']);
                        }//if($newemployeestate != $employeeState)
                        else {
                            if ($firstregofday == 0) {
                                $firstregofday = $this->regRow['reg_time_ts'];
                                $this->dayTypeRows[$this->dayToProcess]['firstreg'] = $this->regRow['reg_time'];
                            }
                        }
                    } else {//state change not found
                        if ($firstregofday == 0) {
                            $firstregofday = $this->regRow['reg_time_ts'];
                            $this->dayTypeRows[$this->dayToProcess]['firstreg'] = $this->regRow['reg_time'];
                        }
                        $this->calc[$this->dayToProcess][self::MARK] =
                            ErrorDefinitionEnum::CALC_STATUS_STATE_CHANGE_MISSING[ErrorDefinitionKeyEnum::CODE];
                        $this->calc[$this->dayToProcess]['errorString'] = sprintf(
                            '&Eacute;rv&eacute;nytelen &aacute;tmenet, &aacute;llapot=%d, esem&eacute;ny=%d',
                            $employeeState,
                            $this->regRow['event_type_id']
                        );
                    }
                }//end of registartion fetch

                $this->calc[$this->dayToProcess]['preeventid'] = isset($this->regRow['event_type_id']) ? $this->regRow['event_type_id'] : 0;
                $this->dayTypeRows[$this->dayToProcess]['lastreg'] = $this->regRow['reg_time'] ?? null;
                if (self::DEBUG_INFO_TO_APPLICATION_MEASURE === self::MEASURE_WIDTH_DAILY) {
                    $this->measure(
                        'Daily (' . $this->dayToProcess . '): registrations fetch',
                        'Day:' . $this->dayToProcess
                    );
                }

                $this->ruleIndicationCostEndMissing($coststarted);
                $this->ruleIndicationCostChangeMissing();

                $staterowid = 0;

                $phGet = $this->publicHolidays->get(
                    $this->dayToProcess,
                    $this->PAID_PUBLIC_HOLIDAY_TYPES,
                    $this->employeeMainData['country']
                );

                if ($firstregofday) {
                    if ($this->dayTypeRows[$this->dayToProcess]['worktype'] === 'INFORMAL') {
                        $this->calc[$this->dayToProcess][self::MARK] = self::CALC_STATUS_OK;
                    } else {
                        $this->ruleInicatorLatestDepartTimeViolation();
                        $state_type_status = $this->stateType->getStateType(
                            $this->dayToProcess,
                            $employeeState,
                            'state_type_status'
                        );
                        if ($this->stateType->getCalculationStart($employeeState) !== 0
                            and $state_type_status > 0
                        ) {
                            if ($state_type_status == 1 || $state_type_status == 11) {
                                if (($this->dayTypeRows[$this->dayToProcess]['used_work_end'] - $this->stateType->getCalculationStart(
                                            $employeeState
                                        )) > 0) {
                                    $workstart = ($this->dayTypeRows[$this->dayToProcess]['used_work_end'] - $this->stateType->getCalculationStart(
                                            $employeeState
                                        )) > (12 * 3600) ?
                                        $this->dayTypeRows[$this->dayToProcess]['used_work_start'] : $this->stateType->getCalculationStart(
                                            $employeeState
                                        );
                                    $workstop = $this->dayTypeRows[$this->dayToProcess]['used_work_end'];
                                    $work = ($workstop - $workstart);
                                    $this->calc[$this->dayToProcess][$employeeState] = $work;
                                    $this->regRow['reg_time_ts'] = $this->dayTypeRows[$this->dayToProcess]['used_work_end'];
                                    if ($this->stateType->getStateType(
                                        $this->dayToProcess,
                                        $employeeState,
                                        GStateType::WORKTIME
                                    )) {
                                        $this->calc[$this->dayToProcess]['lastregdt'] = $this->dayTypeRows[$this->dayToProcess]['used_work_end'];

                                        if ($this->workScheduleIsRestDay() &&
                                            $this->dayTypeRows[$this->dayToProcess]['worktype'] == 'FLEXIBLE'
                                        ) {
                                            $this->calc[$this->dayToProcess]['balance'] = $work;
                                            $this->setClaimtoCalc($costid, 'balance', $work);
                                        } else {
                                            if ($this->dayTypeRows[$this->dayToProcess]['worktype'] == 'SHIFT'
                                                or $this->dayTypeRows[$this->dayToProcess]['worktype'] == 'FRAMEWORK'
                                                or $this->dayTypeRows[$this->dayToProcess]['worktype'] == 'FRAMEWORK_BALANCE'
                                                or $this->dayTypeRows[$this->dayToProcess]['worktype'] == 'STANDBYJOB') {
                                                $this->setWorktimeOvertime($costid, $work, $workstart, $workstop);
                                            } else {
                                                $this->setWorktime($costid, $work, $workstart, $workstop);
                                            }
                                        }
                                    } else {
                                        $this->setClaimtoCalc($costid, $employeeState, $work);
                                    }
                                    if (!isset($this->calc[$this->dayToProcess][self::MARK]) or $this->calc[$this->dayToProcess][self::MARK] > 0) {
                                        $this->calc[$this->dayToProcess][self::MARK] = self::CALC_STATUS_OK;
                                    }
                                }
                            } else {
                                if ($state_type_status == 2) {
                                    if ($dailyregscount == 1
                                        or ($this->dayTypeRows[$this->dayToProcess]['used_work_start'] >= $this->stateType->getCalculationStart(
                                                $employeeState
                                            ) and $stateStartFlag == 1)
                                        or ($this->dayTypeRows[$this->dayToProcess]['used_work_end'] <= $this->stateType->getCalculationStart(
                                                $employeeState
                                            ) and $stateStartFlag == 0)
                                    ) {    //Lekerdezes elso napja
                                        $this->calc[$this->dayToProcess][$employeeState] = 1;//$towork;
                                        $this->calc[$this->dayToProcess]['costcalc'][$this->calc[$this->dayToProcess]['claimid']][$costid][$employeeState]['newStateValue'] = 1;
                                        if ($this->stateType->getStateType(
                                            $this->dayToProcess,
                                            $employeeState,
                                            GStateType::WORKTIME
                                        )) {
                                            $this->calc[$this->dayToProcess][self::WORKTIME] = $towork;
                                            $this->calc[$this->dayToProcess]['firstregdt'] = $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
                                            $this->calc[$this->dayToProcess]['lastregdt'] = $this->dayTypeRows[$this->dayToProcess]['used_work_end'];
                                        } else {
                                            $toWorkInterval -= $towork;
                                        }
                                        if (!isset($this->calc[$this->dayToProcess][self::MARK]) or $this->calc[$this->dayToProcess][self::MARK] >= 0) {
                                            if ($this->dayTypeRows[$this->dayToProcess]['used_work_start'] >= $this->stateType->getCalculationStart(
                                                    $employeeState
                                                )) {
                                                $this->calc[$this->dayToProcess][self::MARK] = self::CALC_STATUS_OK;
                                                $this->calc[$this->dayToProcess]['workisalldayname'] = $this->stateType->getStateType(
                                                    $this->dayToProcess,
                                                    $employeeState,
                                                    'name_dict_id'
                                                );
                                                $this->calc[$this->dayToProcess]['workisalldayshort'] = $this->stateType->getStateType(
                                                    $this->dayToProcess,
                                                    $employeeState,
                                                    'short_name_dict_id'
                                                );
                                                $this->calc[$this->dayToProcess]['costcalc'][$this->calc[$this->dayToProcess]['claimid']][$costid][$employeeState]['workisalldayname'] = $this->stateType->getStateType(
                                                    $this->dayToProcess,
                                                    $employeeState,
                                                    'name_dict_id'
                                                );
                                                $this->calc[$this->dayToProcess]['costcalc'][$this->calc[$this->dayToProcess]['claimid']][$costid][$employeeState]['workisalldayshort'] = $this->stateType->getStateType(
                                                    $this->dayToProcess,
                                                    $employeeState,
                                                    'short_name_dict_id'
                                                );
                                                $this->calc[$this->dayToProcess]['claimid']++;
                                            } else {
                                                $this->calc[$this->dayToProcess][self::MARK] = 1;
                                            }
                                        }
                                    }
                                }
                            }

                            if ($state_type_status == 11) {
                                $this->stateType->setCalculationStart(
                                    $employeeState,
                                    0
                                );    //nem ezzel szamolok csak atjelzek, hogy ne vigye át a következő napra
                                $this->stateType->setCalculationState($employeeState, 0);
                            } else {
                                $this->stateType->setCalculationStart(
                                    $employeeState,
                                    1
                                );    //nem ezzel szamolok csak atjelzek
                                $this->stateType->setCalculationState($employeeState, 2);
                            }
                        } elseif ($this->stateType->getStateType(
                                $this->dayToProcess,
                                $employeeState,
                                'state_type_status'
                            ) == '0'
                            and $this->stateType->getCalculationStart($employeeState)
                            and $this->stateType->getCalculationStop($employeeState) === 0
                            and $this->stateType->getStateType(
                                $this->dayToProcess,
                                $employeeState,
                                GStateType::WORKTIME
                            ) == '1'
                            and (
                                !isset($this->calc[$this->dayToProcess]['claimid'])
                                or $this->calc[$this->dayToProcess]['claimid'] === 0
                                or (
                                    $this->calc[$this->dayToProcess]['preeventid'] != 'COSTEND'
                                    and
                                    $this->calc[$this->dayToProcess]['preeventid'] != 'COST'
                                )
                                or $this->optionCheck('COSTEND_LAST_EVENT') === false
                            )
                        ) {
                            $this->calc[$this->dayToProcess][self::MARK] =
                                ErrorDefinitionEnum::CALC_STATUS_LAST_REG_MISSING[ErrorDefinitionKeyEnum::CODE];
                            //Nincs lezárva az utolsó szakasz
                        } else {
                            if (!isset($this->calc[$this->dayToProcess][self::MARK])) {
                                $this->calc[$this->dayToProcess][self::MARK] = 1;
                            }
                        }
                    }//else informal
                }// if $firstregofday
                elseif ($this->stateType->getCalculationState($employeeState) === 2
                    and $this->stateType->getCalculationStart($employeeState) !== 0
                    and isset($towork) and $towork > 0
                    and !$this->workScheduleIsRestDay()
                    and !$this->workScheduleIsCompensatoryDay()
                    and empty ($phGet)
                    and $this->dayToProcess <= date('c')
                ) {
                    if ($this->isAbsenceExists === true) {
                        $this->stateType->setCalculationState($employeeState, 0);
                        $this->stateType->setCalculationStart($employeeState, 0);
                    } else {
                        $this->calc[$this->dayToProcess]['workisalldayname'] = $this->stateType->getStateType(
                            $this->dayToProcess,
                            $employeeState,
                            'name_dict_id'
                        );
                        $this->calc[$this->dayToProcess]['costcalc'][$this->calc[$this->dayToProcess]['claimid']][$costid][$employeeState] = $towork;
                        $this->calc[$this->dayToProcess]['claimid']++;

                        if (!isset($this->calc[$this->dayToProcess][self::MARK]) or $this->calc[$this->dayToProcess][self::MARK] > 0) {
                            $this->calc[$this->dayToProcess][self::MARK] = self::CALC_STATUS_OK;
                        }
                    }
                } else {
                    if ($towork > 0) {    //Nincs adat
                        $this->calc[$this->dayToProcess][self::MARK] =
                            ErrorDefinitionEnum::CALC_STATUS_REGS_MISSING[ErrorDefinitionKeyEnum::CODE];
                    }
                }
                $toWorkInterval += $towork;
            }//if towork>0
            unset($this->regRow);
            $this->ruleIndicatorAbsenceAndRegsError($firstregofday);
            if (self::DEBUG_INFO_TO_APPLICATION_MEASURE === self::MEASURE_WIDTH_DAILY) {
                $this->measure(
                    'Daily (' . $this->dayToProcess . '): registrations fetch post',
                    'Day:' . $this->dayToProcess
                );
            }

            $this->ruleCalcModifyNightShiftCompensatoryTime($firstregofday, $costid);

            $this->DailyBreakTimeSub();

            $this->ruleIndicationBreakTimeViolation();

            $PAY_NEWEND = true;
            if ($PAY_NEWEND) {
                $this->backLunchTime();
                $this->increaseCalculatedCostCalcByLunchtime($totalWorkTime);
                $this->overtimeBeforeWorkTimeFLEX();
                $this->break_time_after_worktime(); //9óra utáni levonás

                //$this->MovingShortInsideType();
                //this->NormalizeCalculatedClaim();
                $this->NormalizeCalculatedClaim();
                $this->BackWorktimeInPost();

                unset($sum);
                $worktime = 0;
                $overtime_type = 'before';
                $overtime['before'] = $overtime['after'] = 0;
                $i = 0;
                if (isset($this->calc[$this->dayToProcess]['costcalc'])) {
                    $claimidArray = [];
                    if ($this->OVERTIME_AFTER_WORKTIME
                        and $this->dayTypeRows[$this->dayToProcess]['OVERTIME_AFTER_WORKTIME_OVERRIDE'] === false
                        and $this->dayTypeRows[$this->dayToProcess]['worktype'] !== 'FLEXIBLE') {
                        foreach ($this->calc[$this->dayToProcess]['costcalc'] as $claimid => $costCalc) {
                            foreach ($costCalc as $costid => $state) {
                                foreach ($state as $name => $value) {
                                    if (!is_array($value) and $value != 0) {
                                        if (preg_match('/^o/', $name) === 1) {
                                            $overtime[$overtime_type] += $value;
                                            if ($overtime_type == 'before') {
                                                $claimidArray[] = $claimid;
                                            }
                                        } else {
                                            $overtime_type = 'after';
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if ($this->OVERTIME_BEFORE_UNSET && $this->OVERTIME_AFTER_WORKTIME && $overtime['before'] < $this->MIN_OVERTIME_BEFORE_WORK) {
                        foreach ($claimidArray as $key => $value) {
                            unset($this->calc[$this->dayToProcess]['costcalc'][$value]);
                        }
                    }
                    $otbeforeworktime = true;
                    $sum['shifts'] = [];
                    foreach ($this->calc[$this->dayToProcess]['costcalc'] as $claimid => $costCalc) {
                        foreach ($costCalc as $costid => $state) {
                            foreach ($state as $name => $value) {
                                $absenceIsWorktime = false;
                                if ($this->stateType->getStateType(
                                        $this->dayToProcess,
                                        $name,
                                        'state_type_status'
                                    ) === '1'
                                    &&
                                    $this->stateType->getStateType(
                                        $this->dayToProcess,
                                        $name,
                                        GStateType::WORKTIME
                                    ) === '1'
                                ) {
                                    $absenceIsWorktime = true;
                                }
                                if (is_array($value)) {
                                    array_push($sum['shifts'], $name);
                                    $sum[$name]['newStateValue'] = $value['newStateValue'];
                                    $sum[$name]['workisalldayname'] = $value['workisalldayname'];
                                    $sum[$name]['workisalldayshort'] = $value['workisalldayshort'];
                                    $sum[$name]['costid'] = $costid;
                                } elseif ($value != 0) {
                                    $sum['insideTypePrefix'] = $sum['insideTypePrefix'] ?? [];
                                    if (!in_array($name, $sum['shifts'])) {
                                        array_push($sum['shifts'], $name);
                                    }
                                    if (substr($name, -6) === 'nathol') {
                                        $shortName = substr($name, 0, -6);
                                        $sum['insideTypePrefix'][$shortName] = $sum['insideTypePrefix'][$shortName] ?? '';
                                        $sum['insideTypePrefix'][$shortName] .= (substr(
                                                $name,
                                                -6
                                            ) === 'nathol') ? 'nathol' : '';
                                    }
                                    if (substr($name, -3) === 'sun') {
                                        $shortName = substr($name, 0, -3);
                                        $sum['insideTypePrefix'][$shortName] = $sum['insideTypePrefix'][$shortName] ?? '';
                                        $sum['insideTypePrefix'][$shortName] .= (substr(
                                                $name,
                                                -3
                                            ) === 'sun') ? 'sun' : '';
                                    } elseif (substr($name, -4) === 'wknd') {
                                        $shortName = substr($name, 0, -4);
                                        $sum['insideTypePrefix'][$shortName] = $sum['insideTypePrefix'][$shortName] ?? '';
                                        $sum['insideTypePrefix'][$shortName] .= (substr(
                                                $name,
                                                -4
                                            ) === 'wknd') ? 'wknd' : '';
                                    }
                                    unset($newStates);
                                    if ($this->OVERTIME_AFTER_WORKTIME
                                        and $this->dayTypeRows[$this->dayToProcess]['OVERTIME_AFTER_WORKTIME_OVERRIDE'] === false
                                        and $this->dayTypeRows[$this->dayToProcess]['worktype'] !== 'FLEXIBLE'
                                        and (
                                            !$this->workScheduleIsRestDay() ||
                                            (
                                                $this->workScheduleIsRestDay() &&
                                                $this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FRAMEWORK_BALANCE'
                                            )
                                        )
                                    ) {
                                        $stateIsWorktime = $this->stateType->getStateType(
                                            $this->dayToProcess,
                                            $name,
                                            GStateType::WORKTIME
                                        ) === '0' ? 0 : 1;
                                        if ($overtime['before'] >= $this->MIN_OVERTIME_BEFORE_WORK
                                            and $otbeforeworktime === true
                                            and preg_match('/^o/', $name) === 1) {
                                        } elseif ($stateIsWorktime) {
                                            if ($worktime < $towork and $value > 0) {
                                                if ($worktime + $value <= $towork) { //Még nem érte el ezzel a szakasszal sem a munkaidejét
                                                    if (preg_match('/^o/', $name) === 1) {
                                                        //$newStateName		=  $this->changeType($newStateName, $otbeforeworktime);
                                                        $name = preg_replace('/^o/', 'w', $name, 1);
                                                    } elseif ($otbeforeworktime === true) {
                                                        $otbeforeworktime = false;
                                                    }
                                                    $worktime += $value;
                                                } else {
                                                    $wtvalue = $towork - $worktime;
                                                    $value = $value - $wtvalue;
                                                    if ($this->workScheduleIsWorkDay()) {
                                                        $name = preg_replace('/w/', 'o', $name, 1);
                                                        $wtname = preg_replace('/o/', 'w', $name, 1);
                                                    } else {
                                                        $nameT = (substr($name, 2, 1) === 'w') ? substr(
                                                                $name,
                                                                0,
                                                                2
                                                            ) . substr($name, 3) : $name;
                                                        $wtname = preg_replace('/o/', 'w', $nameT, 1);
                                                        $name = preg_replace('/w/', 'o', $name, 1);
                                                    }
                                                    $worktime += $wtvalue;
                                                    $newStates[$wtname] = $wtvalue;
                                                    $newStates[$name] = $value;
                                                }
                                            } else { //Ez a szakasz már túlóra mert megvan a munkaideje
                                                if ($this->workScheduleIsWorkDay()) {
                                                    $name = preg_replace('/^w/', 'o', $name, 1);
                                                }
                                            }
                                        }
                                    }
                                    if (!isset($newStates)) {
                                        $newStates[$name] = $value;
                                    }
                                    foreach ($newStates as $newStateName => $newStateValue) {
                                        if ($this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FRAMEWORK_BALANCE') {    //'FRAMEWORK_BALANCE' -ös túlórák göngyölt túlórába
                                            if ($this->workScheduleIsWorkDay()) {
                                                if (substr($newStateName, 0, 2) ===
                                                    EmployeeExtraHourSignEnum::SIGN_FIRST_TWO_CHAR_IF_OVERTIME
                                                ) {
                                                    $newStateName = 'b' . $newStateName;
                                                }
                                            }
                                            if ($this->workScheduleIsRestDay() &&
                                                !$this->BOT_ACCEPT_BY_OT_MANAGEMENT_TO_OT
                                            ) {
                                                // if(substr($newStateName,0,2) === 'ot'){
                                                // 	$newStateName	= "b".$newStateName;
                                                // }
                                                // Mivel a $sum és majd később az $output is 'cost_id' és  'state' ként van, így nem tudjuk
                                                // megkülönböztetni a jóváhagyott és a nem jóváhagyott szaldót hétvégén. (Normál estben más
                                                // a 'state') $sum és majd később az $output változókba be kellene vezetni egy sorszámot.
                                                if (substr($newStateName, 0, 3) === 'wtw') {
                                                    $newStateName = 'botw' . substr($newStateName, 3);
                                                } elseif (substr($newStateName, 0, 2) === self::SIGN_WORKTIME) {
                                                    $newStateName = 'botw' . substr($newStateName, 2);
                                                }
                                            }
                                        }
                                        if (!isset($sum[$newStateName]['newStateValue'])) {
                                            $sum[$newStateName]['newStateValue'] = 0;
                                        }
                                        $sum[$newStateName]['newStateValue'] += $newStateValue;

                                        //'Ha több bentléten volt, akkor a balance számításhoz kell a szumma';
                                        {
                                            $type = $absenceIsWorktime ? self::SIGN_WORKTIME : substr(
                                                $newStateName,
                                                0,
                                                2
                                            );
                                            if ($type === self::SIGN_WORKTIME ||
                                                $type === self::SIGN_OVERTIME ||
                                                $type === 'ba' ||
                                                $type === 'bo'
                                            ) {
                                                $sum['sum_' . $type]['newStateValue'] = (!isset($sum['sum_' . $type]['newStateValue'])) ? $newStateValue : ($sum['sum_' . $type]['newStateValue'] + $newStateValue);
                                            }
                                        }

                                        $partnoTmp = $i;
                                        $i = ($newStateName === 'balance') ? 0 : $i; //balance-nak mindig 0 kell lennnie
                                        $sum[$newStateName]['part'][$i]['costid'] = $costid;
                                        $newStateName === 'balance' && isset($sum[$newStateName]['part'][$i]['newStateValue']) ? $sum[$newStateName]['part'][$i]['newStateValue'] += $newStateValue : $sum[$newStateName]['part'][$i]['newStateValue'] = $newStateValue;
                                        //$sum[$newStateName]['part'][$i]['workstart']	= $workstart;
                                        //$sum[$newStateName]['part'][$i]['workstop']	= $workstop;
                                        $sum[$newStateName]['partno'] = $i;
                                        $i = $partnoTmp;
                                        $i++;
                                    }
                                }
                            }
                        }//$costCalc
                    }//foreach $this->calc[$this->dayToProcess]["costcalc"]
                    if (!isset($sum['sum_wt']['newStateValue'])) {
                        $sum['sum_wt']['newStateValue'] = 0;
                    }
                    if ($sum['sum_wt']['newStateValue'] < $towork &&
                        $this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FRAMEWORK_BALANCE' &&
                        $this->workScheduleIsWorkDay()
                    ) {
                        $value = $towork - $sum['sum_wt']['newStateValue'];
                        $name = 'wtde';
                        $costid = $this->getDefaultCostId();

                        if (!isset($sum[$name]['part'][$i]['newStateValue'])) {
                            $sum[$name]['part'][$i]['newStateValue'] = 0;
                        }
                        if (!isset($sum[$name]['newStateValue'])) {
                            $sum[$name]['newStateValue'] = 0;
                        }
                        if (array_key_exists($name, $sum)) {
                            $sum[$name]['part'][$i]['costid'] = $costid;
                            $sum[$name]['part'][$i]['newStateValue'] += $value;
                            $sum[$name]['partno'] = $i;
                            $sum[$name]['newStateValue'] += $value;
                        } else {
                            $sum[$name]['part'][$i]['costid'] = $costid;
                            $sum[$name]['part'][$i]['newStateValue'] = $value;
                            $sum[$name]['partno'] = $i;
                            $sum[$name]['newStateValue'] = $value;
                        }
                        $i++;
                        $name = 'botde';
                        if (!isset($sum[$name]['part'][$i]['newStateValue'])) {
                            $sum[$name]['part'][$i]['newStateValue'] = 0;
                        }
                        if (!isset($sum[$name]['newStateValue'])) {
                            $sum[$name]['newStateValue'] = 0;
                        }
                        if (array_key_exists($name, $sum)) {
                            $sum[$name]['part'][$i]['costid'] = $costid;
                            $sum[$name]['part'][$i]['newStateValue'] -= $value;
                            $sum[$name]['partno'] = $i;
                            $sum[$name]['newStateValue'] -= $value;
                        } else {
                            $sum[$name]['newStateValue'] = (-1) * $value;
                            $sum[$name]['part'][$i]['costid'] = $costid;
                            $sum[$name]['part'][$i]['newStateValue'] = (-1) * $value;
                            $sum[$name]['partno'] = $i;
                        }
                        if (!isset($sum['sum_bo']['newStateValue'])) {
                            $sum['sum_bo']['newStateValue'] = 0;
                        }
                        if (!isset($sum['sum_wt']['newStateValue'])) {
                            $sum['sum_wt']['newStateValue'] = 0;
                        }
                        $sum['sum_bo']['newStateValue'] -= $value;
                        $sum['sum_wt']['newStateValue'] += $value;
                        $i++;
                    }

                    $wtdekey = 'wtde' . ($sum['insideTypePrefix']['wtde'] ?? '');
                    $wtdu1key = 'wtdu1' . ($sum['insideTypePrefix']['wtdu1'] ?? '');
                    $wtdu2key = 'wtdu2' . ($sum['insideTypePrefix']['wtdu2'] ?? '');
                    $wtejkey = 'wtej' . ($sum['insideTypePrefix']['wtej'] ?? '');
                    //'legnagyobb bentlét keresése';
                    {
                        if (!isset($sum[$wtdekey])) {
                            $sum[$wtdekey] = 0;
                        }
                        if (!isset($sum[$wtdu1key])) {
                            $sum[$wtdu1key] = 0;
                        }
                        if (!isset($sum[$wtdu2key])) {
                            $sum[$wtdu2key] = 0;
                        }
                        if (!isset($sum[$wtejkey])) {
                            $sum[$wtejkey] = 0;
                        }
                        if ($sum[$wtdekey] > $sum[$wtdu1key]
                            and $sum[$wtdekey] > $sum[$wtdu2key]
                            and $sum[$wtdekey] > $sum[$wtejkey]) {
                            $largest = $wtdekey;
                        } elseif ($sum[$wtdu1key] > $sum[$wtejkey]
                            and $sum[$wtdu1key] > $sum[$wtdu2key]) {
                            $largest = $wtdu1key;
                        } elseif ($sum[$wtdu2key] > $sum[$wtejkey]) {
                            $largest = $wtdu2key;
                        } elseif (isset($sum[$wtejkey])) {
                            $largest = $wtejkey;
                        } else {
                            unset($largest);
                        }
                    }
                    //'FLEXIBLE'-es balance számítás

                    if (!($this->workScheduleIsRestDay() && $this->REST_DAY_BALANCE_TO_OVERTIME) &&
                        $this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FLEXIBLE'
                    ) {
                        $dailyToWork = ($this->dayTypeRows[$this->dayToProcess]['worktype_ext'] === 'FLEXIBLE_BASED_ON_CONTRACT' ? (((integer)$this->employeeMainData['daily_worktime']) * 3600) : $towork);

                        if (!isset($this->calc[$this->dayToProcess]['workisalldayname']) &&
                            !$this->workScheduleIsRestDay()
                        ) {
                            if ($this->dayTypeRows[$this->dayToProcess]['allowance'])//Irodai, de kell neki műszakpótlék $this->dayTypeRows[$this->dayToProcess]["allowance"] du-1,ej-2,du&ej-3
                            {
                                $tmp = $sum['sum_wt']['newStateValue'] - $dailyToWork;
                                if ($tmp > 0 and $balance_min > $tmp) {
                                    $sum['sum_wt']['newStateValue'] = $dailyToWork;
                                }
                                if ($balance_min <= $tmp or $tmp < 0) {
                                    $sum['balance']['part'][0]['newStateValue'] = $sum['sum_wt']['newStateValue'] - $dailyToWork;
                                    $sum[$largest]['newStateValue'] -= $sum['balance']['part'][0]['newStateValue'];//TODO: Valószínűleg jó, de hogy melyikből kellene levonni?
                                    $sum[$largest]['part'][0]['newStateValue'] -= $sum['balance']['part'][0]['newStateValue'];//TODO: Valószínűleg jó, de hogy melyikből kellene levonni?
                                    $sum['balance']['part'][0]['costid'] = $sum[$largest]['part'][$sum[$largest]['partno']]['costid'];//legutolsó costid
                                    $sum[$largest]['balance'] = true;
                                }
                            } else {
                                $balanceValue = $sum['sum_wt']['newStateValue'] - $dailyToWork;
                                $sum['sum_wt']['newStateValue'] = $dailyToWork;
                                $sum = $this->setBalanceToSum($sum, $balanceValue, $dailyToWork);
                            }
                            if (!isset($sum['balance']['part'][0]['newStateValue'])) {
                                $sum['balance']['part'][0]['newStateValue'] = 0;
                            }
                            if ($balance_step > 1) {
                                $sum['balance']['part'][0]['newStateValue'] = $this->roundByWorkgroupRoundType(
                                    $sum['balance']['part'][0]['newStateValue'],
                                    $balance_step,
                                    'balance'
                                );
                            }
                            $sum['balance']['newStateValue'] = $sum['balance']['part'][0]['newStateValue'];
                            $sum['balance']['partno'] = 0;
                        }
                    }//if($this->dayTypeRows[$this->dayToProcess]["worktype"] == 'FLEXIBLE') {

                    $sum['sum_wt']['newStateValue'] = isset($sum['sum_wt']['newStateValue']) ? $this->roundByWorkgroupRoundType(
                        $sum['sum_wt']['newStateValue'],
                        $worktime_interval_min,
                        'worktime'
                    ) : 0;
                    $sum['sum_ot']['newStateValue'] = isset($sum['sum_ot']['newStateValue']) ? $this->roundByWorkgroupRoundType(
                        $sum['sum_ot']['newStateValue'],
                        $overtime_interval_min,
                        'overtime'
                    ) : 0;
                    $sum['sum_bo']['newStateValue'] = isset($sum['sum_bo']['newStateValue']) ? $this->roundByWorkgroupRoundType(
                        $sum['sum_bo']['newStateValue'],
                        $balance_step,
                        'balance'
                    ) : 0;

                    unset($outworktime);

                    foreach ([self::SIGN_WORKTIME, self::SIGN_OVERTIME, 'bo', 'ba'] as $type) {
                        $outworktime[$type]['sum'] = $sum['sum_' . $type]['newStateValue'] ?? 0;
                        $outworktime[$type]['sum_rounded'] = 0;
                        $outworktime[$type]['rounded'] = 0;
                    }

                    unset($ouput);
                    unset($outputOrder);
                    $outputOrder = [];
                    $i = 0;

                    unset($sum['sum_wt']);
                    unset($sum['sum_ot']);
                    unset($sum['sum_ba']);


                    unset($this->calc[$this->dayToProcess]['costcalc']);

                    unset($remained);
                    unset($calc);
                    unset($sumrunded);
                    unset($this->calc[$this->dayToProcess][self::STATES]);
                    $sumRoundedWt = 0;
                    foreach ($sum as $name => &$value) {
                        $additionalFields =
                            substr($name, 0, 4) === 'sum_' || $name === 'shifts' || $name === 'insideTypePrefix';
                        if (!is_array($value) || $additionalFields) {
                            continue;
                        }
                        $absenceIsWorktime = false;
                        if ($this->stateType->getStateType($this->dayToProcess, $name, 'state_type_status') === '1'
                            &&
                            $this->stateType->getStateType($this->dayToProcess, $name, GStateType::WORKTIME) === '1') {
                            $absenceIsWorktime = true;
                        }
                        if (isset($value['part'][0]['costid'])) {
                            if ($value['part'][0]['costid'] === 0) {
                                $value['part'][0]['costid'] = $this->getDefaultCostId();
                            }
                        }
                        if (!isset($value['workisalldayname'])) {
                            if ((substr($name, 0, 2) === self::SIGN_WORKTIME)
                                and $worktime_interval_min > 1
                                and isset($value['newStateValue'])
                            ) {
                                $value['newStateValue'] =
                                    $this->roundByWorkgroupRoundType(
                                        $value['newStateValue'],
                                        $worktime_interval_min,
                                        'worktime'
                                    );
                            }
                            if ((substr($name, 0, 2) === self::SIGN_OVERTIME)
                                and $overtime_interval_min > 1
                                and isset($value['newStateValue'])
                            ) {
                                $value['newStateValue'] =
                                    $this->roundByWorkgroupRoundType(
                                        $value['newStateValue'],
                                        $overtime_interval_min,
                                        'overtime'
                                    );
                            }
                            if ((substr($name, 0, 3) === 'bot')
                                and $balance_step > 1
                                and isset($value['newStateValue'])
                            ) {
                                $value['newStateValue'] =
                                    $this->roundByWorkgroupRoundType(
                                        $value['newStateValue'],
                                        $balance_step,
                                        'balance'
                                    );
                            }
                            if (isset($value['newStateValue']) and $value['newStateValue'] != 0 and $name != 'balance') {
                                $sumcostvalue = 0;
                                $lunctimesub = 0;
                                unset($diff);
                                if (isset($value['part'])) {
                                    foreach ($value['part'] as $id => $cost) {
                                        $cost['roundedvalue'] = $this->timeRound(
                                            $cost['newStateValue'],
                                            $cost_time_interval_min
                                        );
                                        if ($cost['roundedvalue'] == 0) {
                                            unset($value['part'][$id]);
                                            $value['partno']--;
                                        } else {
                                            $value['part'][$id]['roundedvalue'] = $cost['roundedvalue'];
                                            $sumcostvalue += $cost['roundedvalue'];
                                            $diff[$id] = $cost['roundedvalue'] - $cost['newStateValue'];
                                        }
                                    }
                                }
                            } elseif (isset($value['part'][0]['newStateValue'])) {
                                if ($balance_max < $value['part'][0]['newStateValue'] and $value['part'][0]['newStateValue'] < 0) {
                                    $value['part'][0]['newStateValue'] = 0;
                                }
                                if ($balance_min > $value['part'][0]['newStateValue'] and $value['part'][0]['newStateValue'] > 0) {
                                    $value['part'][0]['newStateValue'] = 0;
                                }
                                if ($balance_step > 1) {
                                    $value['part'][0]['roundedvalue'] = $this->roundByWorkgroupRoundType(
                                        $value['part'][0]['newStateValue'],
                                        $balance_step,
                                        'balance'
                                    );
                                } else {
                                    $value['part'][0]['roundedvalue'] = $value['part'][0]['newStateValue'];
                                }
                                $sumcostvalue = $value['part'][0]['roundedvalue'];
                            }
                            if (isset($value['newStateValue']) and $value['newStateValue'] != 0) {
                                $i = 0;
                                if (isset($diff) and is_array($diff)
                                    and (substr($name, 0, 2) === self::SIGN_WORKTIME
                                        or substr($name, 0, 2) === self::SIGN_OVERTIME
                                        or substr($name, 0, 3) === 'bot')
                                ) {
                                    while ($value['newStateValue'] != $sumcostvalue) {
                                        if ($value['newStateValue'] > $sumcostvalue) {
                                            $sumcostvalue += $cost_time_interval_min;
                                            uasort($diff, 'cmp');
                                            reset($diff);
                                            $tmp = $this->eachLegacy($diff);
                                            if (isset($tmp['key'])) {
                                                $value['part'][$tmp['key']]['roundedvalue'] += $cost_time_interval_min;
                                            }
                                        } else {
                                            $sumcostvalue -= $cost_time_interval_min;
                                            uasort($diff, 'cmp');
                                            end($diff); //TODO: legkisebbet vagy a legnagyobbat???
                                            $tmp = $this->eachLegacy($diff);
                                            if (isset($tmp['key'])) {
                                                $value['part'][$tmp['key']]['roundedvalue'] -= $cost_time_interval_min;
                                                if ($value['part'][$tmp['key']]['roundedvalue'] == 0) {
                                                    unset($value['part'][$tmp['key']]);
                                                    unset($diff[$tmp['key']]);
                                                }
                                            }
                                        }
                                        $i++;
                                        if ($i > 60) {    //60 próbálkozás után kilép, elvben nem fordulhat elő
                                            break;
                                        }
                                    }
                                }
                                if (isset($value['part'])) {
                                    foreach ($value['part'] as $id => $cost) {
                                        if (!isset($output[$cost['costid']][$name]['roundedvalue'])) {
                                            $t['state'] = $name;
                                            $t['costid'] = $cost['costid'];
                                            array_push($outputOrder, $t);
                                        }

                                        if (!isset($cost['roundedvalue'])) {
                                            $cost['roundedvalue'] = 0;
                                        }
                                        $this->InitAndAddValueToVariable(
                                            $output[$cost['costid']][$name]['roundedvalue'],
                                            $cost['roundedvalue']
                                        );
                                        $this->InitAndAddValueToVariable(
                                            $output[$cost['costid']][$name]['newStateValue'],
                                            $cost['newStateValue']
                                        );
                                        $this->InitAndAddValueToVariable(
                                            $outworktime[$name],
                                            $cost['roundedvalue']
                                        );

                                        $type = $absenceIsWorktime ? self::SIGN_WORKTIME : substr($name, 0, 2);
                                        $outworktime[$type]['rounded'] += $cost['roundedvalue'];

                                        $outworktime[$type]['diff'][$id]['name'] = $name;
                                        $outworktime[$type]['diff'][$id]['costid'] = $cost['costid'];
                                        $outworktime[$type]['diff']['ids'][$id] = $cost['roundedvalue'] - $cost['newStateValue'];
                                        if ($type === 'ba') {
                                            $outworktime[self::SIGN_WORKTIME]['rounded'] += $cost['roundedvalue'];
                                        }
                                    }
                                }
                            }//if($newStateValue['newStateValue']!=0){
                            $type = $absenceIsWorktime ? self::SIGN_WORKTIME : substr($name, 0, 2);
                            if ($type === self::SIGN_WORKTIME ||
                                $type === self::SIGN_OVERTIME ||
                                $type === 'bo' ||
                                $absenceIsWorktime
                            ) {
                                $outworktime[$type]['sum_rounded'] = $outworktime[$type]['rounded'];
                            }
                        } else {
                            $actStatesRowInCalc = &$this->calc[$this->dayToProcess][self::STATES][$staterowid];
                            $actStatesRowInCalc[self::STATES_STATE] = $name;
                            $actStatesRowInCalc[self::STATES_WORK_IS_ALL_DAY_NAME] = $value['workisalldayname'];
                            $actStatesRowInCalc[self::STATES_WORK_IS_ALL_DAY_SHORT_NAME] = $value['workisalldayshort'];
                            $actStatesRowInCalc[self::STATES_VALUE] = $value['newStateValue'];
                            switch ($this->COST_MODE) {
                                case 'COST':
                                    $actStatesRowInCalc[self::STATES_COST_ID] = $value['costid'];
                                    break;
                                case 'COSTCENTER':
                                    $actStatesRowInCalc[self::STATES_COST_CENTER_ID] = $value['costid'];
                                    break;
                                case 'ACTIVITY':
                                    unset($tmp);
                                    $tmp = explode(';', $value['costid']);
                                    $actStatesRowInCalc[self::STATES_COST_ID]['costid'] = $tmp[0];
                                    $actStatesRowInCalc[self::STATES_COST_CENTER_ID]['costcenterid'] = $tmp[1];
                                    break;
                            }
                            $actStatesRowInCalc[self::STATES_STATUS] = 2;    //all day
                            $staterowid++;
                        }
                    }
                } elseif (isset($this->downtimeWorkstartWorkend[$this->employeeContractId][$this->dayToProcess]['workStart'])
                    && !empty($output)) {
                    reset($output);
                    $costid = $this->getDefaultCostId();
                    $this->setDowntime($costid, $output, $outputOrder);
                    $this->writeOutputToCalc($output, $outputOrder);
                }
                //Ha Rugalmas munkavallalo eseten nem az statuszokon toltott idok osszege kevesebb mint a beosztott
                // munkakaido (elobb generaljuk a balanszot aztan lesznek a reszek intrvallumra szamolva
                // és nem pihenőnap - ronda, de....
                $dailyToWork = $dailyToWork ?? null;
                $outworktime[self::SIGN_WORKTIME]['sum_rounded'] = $outworktime[self::SIGN_WORKTIME]['sum_rounded'] ?? null;
                if ((int)$outworktime[self::SIGN_WORKTIME]['sum_rounded'] !== (int)$dailyToWork
                    && $this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FLEXIBLE'
                    && !$this->workScheduleIsRestDay()
                    && isset($output)
                ) {
                    reset($output);
                    $costId = key($output);
                    $stateName = key($output[$costId]);
                    $output[$costId][$stateName]['roundedvalue'] += !empty($tmpRows) ? ($dailyToWork - $outworktime[self::SIGN_WORKTIME]['sum_rounded']) : 0;
                    $outworktime[self::SIGN_WORKTIME]['sum_rounded'] = $dailyToWork;
                    $outworktime[self::SIGN_WORKTIME]['rounded'] = $dailyToWork;
                    $outworktime[$stateName] = $output[$costId][$stateName]['roundedvalue'];
                }
                if (isset($output) and is_array($output)) {
                    $this->calcIntervalLostTimeBack(self::SIGN_WORKTIME, $outworktime, $output, $worktime_interval_min);
                    $this->calcIntervalLostTimeBack(self::SIGN_OVERTIME, $outworktime, $output, $overtime_interval_min);
                    $this->calcIntervalLostTimeBack('bo', $outworktime, $output, $balance_step);
                    $this->addAbsenceToBalance('ba', $outworktime, $output);
                    if ($this->USING_LOST_TIME_TYPE and $this->dayTypeRows[$this->dayToProcess]['towork'] >
                        $outworktime[self::SIGN_WORKTIME]['sum_rounded'] &&
                        $this->dayTypeRows[$this->dayToProcess]['worktype'] != 'FLEXIBLE' &&
                        (
                            !$this->workScheduleIsRestDay() || $this->USING_LOST_TIME_TYPE_IN_REST_DAY
                        )
                    ) {
                        $output[$costid]['lostTime']['roundedvalue'] = $this->dayTypeRows[$this->dayToProcess]['towork'] - $outworktime[self::SIGN_WORKTIME]['sum_rounded'];
                        $this->setBackLostTimeToWorktime(
                            $outworktime,
                            $output,
                            $output[$costid]['lostTime']['roundedvalue']
                        ); //TODO: Miert van kulon output es outworktime
                    }
                    $this->setOvertimeStatus($output, $outputOrder);
                    $this->ruleIndicationDifferenceDaytypeAndCalculation($output, $outputOrder);
                    $this->postOvertimeInterval($output, $outputOrder);
                    $this->setDowntime($costid, $output, $outputOrder);
                    $this->setStandbyDaytypeBreakTimeOverlap($costid, $output, $outputOrder);
                    $this->setBackTimeToNewInsideType($costid, $output, $outputOrder);
                    $this->setInsideTypeValuesMerged($costid, $output, $outputOrder);
                    $this->setInsideTypeValueRound($costid, $output, $outputOrder);
                    $this->writeOutputToCalc($output, $outputOrder);
                } elseif (isset($this->calc[$this->dayToProcess]['regscount']) &&
                    $this->calc[$this->dayToProcess]['regscount'] > 0 &&
                    $this->CAME_IN) {//Nincs elszámolt adat, de volt bent
                    $this->calc[$this->dayToProcess]['camein'] = true;
                }
                unset($output);
            }//if($PAY_NEWEND)
            unset($sum);
            unset($remained);
            unset($calc);
            unset($sumrunded);
            if (self::DEBUG_INFO_TO_APPLICATION_MEASURE === self::MEASURE_WIDTH_DAILY) {
                $this->measure('Daily (' . $this->dayToProcess . '): New pay', 'Day:' . $this->dayToProcess);
            }
            $this->setNexDayToProcess();
        }//next day
        unset($this->calc[$this->dayToProcess]);

        if (isset($this->calc)) {
            $this->writeEmployeeCalcTables();
            $this->measure('Write (' . $this->employeeContractId . '): writeEmployeeCalcTables');
        }
    }

//--------------------------------------------------------------------------------------------

    private function setBalanceToSum($sum, $balanceValue, $dailyToWork)
    {
        if ($balanceValue === 0) {
            return $sum;
        }
        $sum['balance']['part'][0]['newStateValue'] = $balanceValue;

        $shifts = $sum['shifts'];
        //itt mindegy, de ha az allowance -t is akarjuk kezelni, mert az sem telejsen ok
        // hogy a lenagyobb részből vonjuk vagy adjuk hozzá a balance-t, akkor itt különbséget
        // kell tenni, hogy levonjuk, vagy visszaadjuk. Visszaadáshoz el kellene tárolni,
        // hogy a munkarendjéhez képest mikor nem volt bent, vagy mikor volt bent többet.

        //A balance visszaadása vagy levonása, a bent töltött shiftek sorrendjében az utolsó
        // résztől (részek a costid bontás) visszafelé haladva történik.
        // Hogy miért? Mert eddig is hasonlóan volt, csak nem foglalkozott a costid felbontással.
        // És általában az emberek tovább dolgoznak, és nem hamarabb jönnek be, persze ezt is
        // lehetne figyelni, és az alapján visszaadni, levonni a balance idejét.
        if ($balanceValue > 0 or 1) {
            reset($shifts);
            $shiftWithPrefix = current($shifts);
            while ($balanceValue !== 0 && $shiftWithPrefix !== false) {
                $partNoMax = $sum[$shiftWithPrefix]['partno'] ?? -1;
                for ($partNo = $partNoMax; $partNo >= 0; $partNo--) {
                    if ($sum[$shiftWithPrefix]['part'][$partNo]['newStateValue'] >= $balanceValue) {
                        $sum[$shiftWithPrefix]['part'][$partNo]['newStateValue'] -= $balanceValue;
                        $sum[$shiftWithPrefix]['value'] -= $balanceValue;
                        $balanceValue = 0;
                        $partNo = -1;
                        break;
                    } else {
                        $balanceValue -= $sum[$shiftWithPrefix]['part'][$partNo]['newStateValue'];
                        $sum[$shiftWithPrefix]['newStateValue'] -= $sum[$shiftWithPrefix]['part'][$partNo]['newStateValue'];
                        unset($sum[$shiftWithPrefix]['part'][$partNo]);
                        $sum[$shiftWithPrefix]['partno']--;
                    }
                }
                $shiftWithPrefix = next($shifts);
            }
        }
        $shiftWithPrefix = end($shifts);
        $foundLastValidCostid = false;
        while (!$foundLastValidCostid && $shiftWithPrefix !== false) {
            $partNo = $sum[$shiftWithPrefix]['partno'];
            $foundLastValidCostid = isset($sum[$shiftWithPrefix]['part'][$partNo]['costid']) ? $shiftWithPrefix : false;
            $shiftWithPrefix = prev($shifts);
        }
        $costId = isset($partNo) ? $sum[$foundLastValidCostid]['part'][$partNo]['costid'] : 0; //legutolsó elem costid-ja
        $sum['balance']['part'][0]['costid'] = $costId === 0 ? $this->getDefaultCostId() : $costId;
        $sum[$shiftWithPrefix]['balance'] = true;

        return $sum;
    }

    private function calcIntervalLostTimeBack($type, &$outworktime, &$output, $interval_min)
    {
        if ($this->BALANCE_TIME_BACK && $type == 'bo') {
            return;
        }

        $outwt = $outworktime[$type];
        $diff = $outworktime[$type]['diff'] ?? null;
        $insideTypeList = [];
        if (($type == self::SIGN_WORKTIME || $type = self::SIGN_OVERTIME) && !is_null(
                $this->CALC_INTERVAL_LOST_TIME_BACK_INSIDE_TYPE_LIST
            ) && !empty($this->CALC_INTERVAL_LOST_TIME_BACK_INSIDE_TYPE_LIST)) {
            $insideTypeList = explode(',', $this->CALC_INTERVAL_LOST_TIME_BACK_INSIDE_TYPE_LIST);
        }
        $i = 0;
        while ($outwt['sum'] != $outwt['rounded'] and $outworktime['ba']['rounded'] == 0) {
            if ($outwt['sum'] > $outwt['rounded']) {
                $outwt['rounded'] += $interval_min;
                if (isset($diff['ids'])) {
                    uasort($diff['ids'], 'cmp');
                    reset($diff['ids']);
                    $tmp = $this->eachLegacy($diff['ids']);
                    $name = $diff[$tmp['key']]['name'];
                    $costid = $diff[$tmp['key']]['costid'];
                    if (!isset($output[$costid][$name]['roundedvalue'])) {
                        $output[$costid][$name]['roundedvalue'] = 0;
                    }
                    $output[$costid][$name]['roundedvalue'] += $interval_min;
                    $outworktime[$name] = $output[$costid][$name]['roundedvalue'];
                    $outworktime[$type]['rounded'] += $interval_min;
                    $outworktime[$type]['sum_rounded'] += $interval_min;
                }
            } else {
                $outwt['rounded'] -= $interval_min;
                if (isset($diff['ids'])) {
                    if (!empty($insideTypeList)) {
                        $diffArray = [];
                        foreach ($diff as $diffKey => $diffValue) {
                            if (isset($diffValue['name'])) {
                                $diffArray[$diffValue['name']] = $diffKey;
                            }
                        }

                        foreach ($insideTypeList as $insideType) {
                            $tmpKeyExists = array_key_exists($insideType, $diffArray);
                            $tmpKey = false;
                            if ($tmpKeyExists) {
                                $tmpKey = $diffArray[$insideType];
                                break;
                            }
                        }
                    }

                    if (empty($insideTypeList) || $tmpKey === false) {
                        uasort($diff['ids'], 'cmp');
                        end($diff['ids']); //TODO: legkisebbet vagy a legnagyobbat???
                        $tmp = $this->eachLegacy($diff['ids']);
                        $tmpKey = $tmp['key'];
                    }

                    $name = $diff[$tmpKey]['name'];
                    $costid = $diff[$tmpKey]['costid'];
                    if (!isset($output[$costid][$name]['roundedvalue'])) {
                        $output[$costid][$name]['roundedvalue'] = 0;
                    }
                    $output[$costid][$name]['roundedvalue'] -= $interval_min;
                    $outworktime[$name] = $output[$costid][$name]['roundedvalue'];
                    $outworktime[$type]['rounded'] -= $interval_min;
                    $outworktime[$type]['sum_rounded'] -= $interval_min;

                    if ($output[$costid][$name]['roundedvalue'] == 0) {
                        unset($output[$costid][$name]);
                        if (isset($outputdiff[$tmpKey])) {
                            unset($outputdiff[$tmpKey]);
                        }
                    }
                }
            }
            $i++;
            if ($i > 60) {    //60 próbálkozás után kilép, elvben nem fordulhat elő
                break;
            }
        }
    }

    private function addAbsenceToBalance($type, &$outworktime, &$output): void
    {
        $absence = &$this->employeeAbsences[$this->employeeContractId][$this->dayToProcess];
        if (!$this->ADD_ABSENCE_TO_BALANCE
            || $this->dayTypeRows[$this->dayToProcess]['worktype'] !== 'FLEXIBLE'
            || ((int)($absence[GetEmployeeAbsences::FULL_DAY_ABSENCE] ?? 0)) == 1
            || ((int)($absence[GetEmployeeAbsences::ABSENCE_HOUR] ?? 0)) === 0
            || ((int)($absence[GetEmployeeAbsences::WORKTIME] ?? 0)) != 1
            || ((int)($absence[GetEmployeeAbsences::ABSENCE_HOUR] ?? 0)) > 12
        ) {
            return;
        }
        $absenceHour = ((int)($absence[GetEmployeeAbsences::ABSENCE_HOUR] ?? 0)) * 60 * 60;

        $diff = $outworktime[$type]['diff'] ?? null;
        if (!is_null($diff) && isset($diff['ids'])) {
            uasort($diff['ids'], 'cmp');
            reset($diff['ids']);
            $tmp = $this->eachLegacy($diff['ids']);
            $name = $diff[$tmp['key']]['name'];
            $costid = $diff[$tmp['key']]['costid'];

            if (!isset($output[$costid][$name]['roundedvalue'])) {
                $output[$costid][$name]['roundedvalue'] = 0;
            }
            $output[$costid][$name]['roundedvalue'] += $absenceHour;
            $outworktime[$name] = $output[$costid][$name]['roundedvalue'];
            $outworktime[$type]['rounded'] += $absenceHour;
            $outworktime[$type]['sum_rounded'] += $absenceHour;
        }
    }

    private function addRegsAbsenceToBalance(&$regs)
    {
        $absence = &$this->employeeAbsences[$this->employeeContractId][$this->dayToProcess];
        $fullDayAbsence = (int)($absence[GetEmployeeAbsences::FULL_DAY_ABSENCE] ?? 0);
        if (!$this->ADD_ABSENCE_TO_BALANCE
            || $this->dayTypeRows[$this->dayToProcess]['worktype'] !== 'FLEXIBLE'
            || $fullDayAbsence == 1
            || ((int)($absence[GetEmployeeAbsences::ABSENCE_HOUR] ?? 0)) === 0
            || ((int)($absence[GetEmployeeAbsences::ABSENCE_HOUR] ?? 0)) > 12
            || !empty($regs)) {
            return;
        }
        $ts = $this->dayTypeRows[$this->dayToProcess]['used_work_start'] + 3600;
        $workStartDate = new DateTime("@$ts", new DateTimeZone(self::DEFAULT_DATE_TIMEZONE));
        $regs[0]['reg_time'] = $workStartDate->format($this->WfmDateTimeFormat);
        $regs[0]['event_type_id'] = self::NMB_EVENT_TYPE_ID;
        $regs[0]['terminal_id'] = 'default';
        $regs[0]['reader_id'] = 'default';
        $regs[0]['calc_status'] = 0;
        $regs[0]['event_type_name'] = 'Normál Be';
        $regs[0]['from_work_start'] = 0;
        $regs[0]['to_work_end'] = 0;
        $regs[0]['cost_id'] = '';
        $regs[0]['card'] = 'default';
        $regs[0][CommonFieldEnum::STATUS] = Status::PUBLISHED;

        $ts = $this->dayTypeRows[$this->dayToProcess]['used_work_end'];
        $workEndDate = new DateTime("@$ts", new DateTimeZone(self::DEFAULT_DATE_TIMEZONE));
        $regs[1]['reg_time'] = $workEndDate->format($this->WfmDateTimeFormat);
        $regs[1]['event_type_id'] = self::NMK_EVENT_TYPE_ID;
        $regs[1]['terminal_id'] = 'default';
        $regs[1]['reader_id'] = 'default';
        $regs[1]['calc_status'] = 0;
        $regs[1]['event_type_name'] = 'Normál Ki';
        $regs[1]['from_work_start'] = 0;
        $regs[1]['to_work_end'] = 0;
        $regs[1]['cost_id'] = '';
        $regs[1]['card'] = 'default';
        $regs[1][CommonFieldEnum::STATUS] = Status::PUBLISHED;

        $validBalanceTime = ($this->dayTypeRows[$this->dayToProcess]['towork'] -
            ((int)($absence[GetEmployeeAbsences::ABSENCE_HOUR] ?? 0) * 60 * 60));
        $absence[GetEmployeeAbsences::ABSENCE_HOUR] = -1 * ($validBalanceTime / 3600) + 1;
    }

    private function addLastNMKRegIfNone($tmpRows)
    {
        if ($this->fromRunType == self::RUN_TYPE_ONLY_MEMORY) {
            $lastArrayKey = array_key_last($tmpRows);
            if ($tmpRows[$lastArrayKey]['event_type_id'] != self::NMK_EVENT_TYPE_ID) {
                $regs = [];
                date_default_timezone_set('Europe/Budapest');
                $ts = $this->dayToProcess . ' ' . date('H:i');
                date_default_timezone_set(self::DEFAULT_DATE_TIMEZONE);
                $workEndDate = new DateTime("$ts", new DateTimeZone(self::DEFAULT_DATE_TIMEZONE));
                $regs['reg_time'] = $workEndDate->format($this->WfmDateTimeFormat);
                $regs['event_type_id'] = self::NMK_EVENT_TYPE_ID;
                $regs['terminal_id'] = 'default';
                $regs['reader_id'] = 'default';
                $regs['calc_status'] = 0;
                $regs['event_type_name'] = 'Normál Ki';
                $regs['from_work_start'] = 0;
                $regs['to_work_end'] = 0;
                $regs['cost_id'] = '';
                $regs['card'] = 'default';
                $regs['status'] = Status::PUBLISHED;
                $tmpRows[] = $regs;
            }
        }
        return $tmpRows;
    }

    private function addRegsIfNone($tmpRows)
    {
        if ($this->fromRunType != self::RUN_TYPE_WORK_SCHEDULE_BY_WORK_TIME) {
            return $tmpRows;
        }
        $tmpKey = array_key_first($tmpRows);
        if ($tmpRows[$tmpKey]['event_type_id'] != self::NMB_EVENT_TYPE_ID) {
            $regs = [];
            date_default_timezone_set('Europe/Budapest');
            $ts = $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
            date_default_timezone_set(self::DEFAULT_DATE_TIMEZONE);
            $workDate = new DateTime("@$ts", new DateTimeZone(self::DEFAULT_DATE_TIMEZONE));
            $regs['reg_time'] = $workDate->format($this->WfmDateTimeFormat);
            $regs['event_type_id'] = self::NMB_EVENT_TYPE_ID;
            $regs['terminal_id'] = 'default';
            $regs['reader_id'] = 'default';
            $regs['calc_status'] = 0;
            $regs['event_type_name'] = 'Normál Be';
            $regs['from_work_start'] = 0;
            $regs['to_work_end'] = 0;
            $regs['cost_id'] = '';
            $regs['card'] = 'default';
            $regs['status'] = Status::PUBLISHED;
            $tmpRows[] = $regs;
        }

        $tmpKey = array_key_last($tmpRows);
        if ($tmpRows[$tmpKey]['event_type_id'] != self::NMK_EVENT_TYPE_ID) {
            $regs = [];
            date_default_timezone_set('Europe/Budapest');
            $ts = $this->dayTypeRows[$this->dayToProcess]['used_work_end'];
            date_default_timezone_set(self::DEFAULT_DATE_TIMEZONE);
            $workDate = new DateTime("@$ts", new DateTimeZone(self::DEFAULT_DATE_TIMEZONE));
            $regs['reg_time'] = $workDate->format($this->WfmDateTimeFormat);
            $regs['event_type_id'] = self::NMK_EVENT_TYPE_ID;
            $regs['terminal_id'] = 'default';
            $regs['reader_id'] = 'default';
            $regs['calc_status'] = 0;
            $regs['event_type_name'] = 'Normál Ki';
            $regs['from_work_start'] = 0;
            $regs['to_work_end'] = 0;
            $regs['cost_id'] = '';
            $regs['card'] = 'default';
            $regs['status'] = Status::PUBLISHED;
            $tmpRows[] = $regs;
        }

        return $tmpRows;
    }

    private function writeOutputToCalc($output, $outputOrder)
    {
        $staterowid = 0;
        foreach ($outputOrder as $id => $outputValues) {
            $costid = $outputValues['costid'];
            $state = $outputValues['state'];
            $values = $output[$costid][$state] ?? null;

            if (isset($values['roundedvalue']) && $values['roundedvalue'] != 0) {
                if ($this->dayTypeRows[$this->dayToProcess]['overtimeType'] === 'BEFOREWORK'
                    and isset($values['broken'])) {
                    $this->writeBrokenValues($staterowid, $state, $values['broken'], $costid);
                    unset($values['broken']);
                }

                $this->writeValuesToCalc($staterowid, $state, $values, $costid);
                $staterowid++;

                if (isset($values['broken'])) {
                    $this->writeBrokenValues($staterowid, $state, $values['broken'], $costid);
                }
            }
            unset($output[$costid][$state]);
        }
        foreach ($output as $costid => $states) {
            foreach ($states as $state => $values) {
                if ($values['roundedvalue'] != 0) {
                    $this->writeValuesToCalc($staterowid, $state, $values, $costid);
                    $staterowid++;
                }
            }
        }
    }

    private function writeBrokenValues(&$staterowid, $state, $broken, $costid)
    {
        foreach ($broken as $id => $brokenValues) {
            $state = isset($brokenValues['state']) ? $brokenValues['state'] : $state;
            $this->writeValuesToCalc($staterowid, $state, $brokenValues, $costid);
            $staterowid++;
        }
    }

    private function writeValuesToCalc($staterowid, $state, $values, $costid)
    {
        $cost = [];
        $this->getCostCostcenterByCostMode($cost, $costid);

        $status = $this->getStatus($values, $state);
        $actStatesRowInCalc = &$this->calc[$this->dayToProcess][self::STATES][$staterowid];
        $actStatesRowInCalc[self::STATES_STATE] = $state;
        $actStatesRowInCalc[self::STATES_VALUE] = $values['roundedvalue'];
        $actStatesRowInCalc[self::STATES_STATUS] = $status;
        $actStatesRowInCalc[self::STATES_COST_ID] = $cost['costid'];
        $actStatesRowInCalc[self::STATES_COST_CENTER_ID] = $cost['costcenterid'];
    }

    private function getStatus($values, $state)
    {
        $lockable = ($this->optionCheck('AUTOLOCK') === true
            && (!isset($this->calc[$this->dayToProcess][self::MARK])
                || $this->calc[$this->dayToProcess][self::MARK] >= 0
            )
        );
        $botToDraft = (!$this->BOT_AUTOMATIC_ACCEPT && substr($state, 0, 3) === 'bot');
        $stDraft = (substr($state, 0, 2) === self::SIGN_OVERTIME || $botToDraft)
            && (!isset($values['status']) || $values['status'] === Status::DRAFT);

        if ($lockable === true) {    //TODO: Tesztelni a AUTOLOCK-t
            if ((isset($values['status']) && in_array($values['status'], [Status::SAVED], Status::PUBLISHED))
                || !$stDraft
            ) {
                return Status::LOCKED;
            }
            if (isset($values['status'])) {
                return $values['status'];
            }
            if ($stDraft) {
                return Status::DRAFT;
            }
        }

        return isset($values['status']) ? $values['status'] : ($stDraft ? Status::DRAFT : Status::PUBLISHED);
    }

    private function setOvertimeStatus(&$calculatedValues, &$calculatedValuesOrder)
    {
        $this->calc[$this->dayToProcess]['statusChanged'] = false;
        if ($this->OVERTIME_MODE === 'paid'
            and !empty($this->dayTypeRows[$this->dayToProcess]['overtimeType'])
            and $this->dayTypeRows[$this->dayToProcess]['overtimeTime'] > 0) {
            if ($this->dayTypeRows[$this->dayToProcess]['worktype'] == 'FLEXIBLE') {
                $BalanceIDs = [];
                $this->getBalanceOrderIDs($BalanceIDs, $calculatedValuesOrder);
                $this->setBalanceToOvertime($calculatedValues, $calculatedValuesOrder, $BalanceIDs);
                unset($BalanceIDs);
            } else {
                if ($this->dayTypeRows[$this->dayToProcess]['overtimeType'] === 'BEFOREWORK') {
                    $beforeWorkOvertimeOrdesIDs = [];
                    $this->getBeforeWorkOvertimeOrderIDs($beforeWorkOvertimeOrdesIDs, $calculatedValuesOrder);
                    $this->reverseOrderOverTime($beforeWorkOvertimeOrdesIDs);
                    $this->setStatusToCalculatedValues(
                        $calculatedValues,
                        $calculatedValuesOrder,
                        $beforeWorkOvertimeOrdesIDs
                    );
                    unset($beforeWorkOvertimeOrdesIDs);
                }
                if ($this->dayTypeRows[$this->dayToProcess]['overtimeType'] === 'AFTERWORK') {
                    $afterWorkOvertimeOrdesIDs = [];
                    $this->getAfterWorkOvertimeOrderIDs($afterWorkOvertimeOrdesIDs, $calculatedValuesOrder);
                    $this->setStatusToCalculatedValues(
                        $calculatedValues,
                        $calculatedValuesOrder,
                        $afterWorkOvertimeOrdesIDs
                    );
                }
                if ($this->workScheduleIsRestDay()) {
                    $this->setStatusToCalculatedValuesRestday($calculatedValues, $calculatedValuesOrder, false);
                }
            }
        } elseif ($this->workScheduleIsRestDay()) {
            $this->setStatusToCalculatedValuesRestday($calculatedValues, $calculatedValuesOrder, true);
        }

        $this->setOvertimeStatusBySignModifier($calculatedValues, $calculatedValuesOrder);
        $this->changeBOTUnderStandbyToOvertime($calculatedValues, $calculatedValuesOrder);
    }

    private function changeBOTUnderStandbyToOvertime(&$calculatedValues, &$calculatedValuesOrder)
    {
        if (!$this->STANDBY_BOT_ACCEPT_AND_CHANGE_TO_OT) {
            return;
        }
        foreach ($calculatedValuesOrder as $orderId => &$valuesOrder) {
            $state = $valuesOrder['state'];
            $stateNotContainingStandby = !$this->containsStandByInInsideType($state);
            $stateNotContainingBalanceOvertime = substr_count($state, 'bot') === 0;
            if ($stateNotContainingStandby || $stateNotContainingBalanceOvertime) {
                continue;
            }
            $costId = $valuesOrder['costid'];
            $newState = substr($state, 1);

            $calculatedValues[$costId][$newState]['roundedvalue'] = $calculatedValues[$costId][$state]['roundedvalue'];
            $calculatedValues[$costId][$newState]['value'] = $calculatedValues[$costId][$state]['value'];
            $calculatedValues[$costId][$newState]['status'] = Status::PUBLISHED;
            unset($calculatedValues[$costId][$state]);
            $calculatedValuesOrder[$orderId]['state'] = $newState;
        }
    }

    protected function setOvertimeStatusBySignModifier(&$calculatedValues, &$calculatedValuesOrder)
    {
        foreach ($calculatedValuesOrder as $orderId => &$valuesOrder) {
            $state = $valuesOrder['state'];
            if (substr_count($state, self::SIGN_MODIFIER_CHAR) === 0) {
                continue;
            }
            $costid = $valuesOrder['costid'];
            if (substr_count($state, self::SIGN_MODIFIER_ACCEPTED_OVERTIME)) {
                $newState = (substr($state, 0, 3) === 'bot' && $this->BOT_ACCEPT_BY_OT_MANAGEMENT_TO_OT) ?
                    substr($state, 1) :
                    $state;
                $newState = preg_replace('/' . self::SIGN_MODIFIER_ACCEPTED_OVERTIME . '/', '', $newState, 1);
                $newStatus = Status::PUBLISHED;
                if (isset($calculatedValues[$costid][$newState])) {
                    $newCalculatedValues[$costid][$newState] = $calculatedValues[$costid][$newState];

                    $newCalculatedValues[$costid][$newState]['broken'] = [];
                    $t['roundedvalue'] = $calculatedValues[$costid][$state]['roundedvalue'];
                    $t['status'] = $newStatus;
                    array_push($newCalculatedValues[$costid][$newState]['broken'], $t);
                    unset($calculatedValuesOrder[$orderId]);
                } else {
                    isset($newCalculatedValues[$costid][$newState]['roundedvalue'])
                        ? $newCalculatedValues[$costid][$newState]['roundedvalue'] += $calculatedValues[$costid][$state]['roundedvalue']
                        : $newCalculatedValues[$costid][$newState]['roundedvalue'] = $calculatedValues[$costid][$state]['roundedvalue'];
                    $newCalculatedValues[$costid][$newState]['status'] = $newStatus;
                    $valuesOrder['state'] = $newState;
                }
                unset($calculatedValues[$costid][$state]);
            }
        }
        if (isset($newCalculatedValues)) {
            $this->calc[$this->dayToProcess]['statusChanged'] = true;

            foreach ($newCalculatedValues as $key => $state) {
                foreach ($state as $stateKey => $stateValue) {
                    $calculatedValues[$key][$stateKey] = $newCalculatedValues[$key][$stateKey];
                }
            }
        }
    }

    private function setStatusToCalculatedValuesRestday(&$calculatedValues, &$calculatedValuesOrder, $used_type_daytype)
    {
        $newCalculatedValues = [];
        foreach ($calculatedValuesOrder as $orderId => &$valuesOrder) {
            $costid = $valuesOrder['costid'];
            $state = $valuesOrder['state'];
            if (isset($calculatedValues[$costid][$state]['roundedvalue']) && $calculatedValues[$costid][$state]['roundedvalue'] != 0) {
                if (substr($state, 0, 4) === 'botw') {
                    $newCalculatedValues[$costid][$state]['roundedvalue'] = $calculatedValues[$costid][$state]['roundedvalue'];
                    $newCalculatedValues[$costid][$state]['status'] = Status::PUBLISHED;
                    unset($calculatedValues[$costid][$state]);
                }
                if (substr($state, 0, 2) === self::SIGN_OVERTIME) {
                    if ($this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FRAMEWORK_BALANCE'
                        && $this->BOT_ACCEPT_BY_OT_MANAGEMENT_TO_OT
                    ) {
                        $newState = 'bot' . substr($state, 2, strlen($state));
                        $newStatus = $this->BOT_AUTOMATIC_ACCEPT ? Status::PUBLISHED : Status::DRAFT;

                        if (isset($newCalculatedValues[$costid][$newState])) {
                            $newCalculatedValues[$costid][$newState]['broken'] = [];
                            $t['roundedvalue'] = $calculatedValues[$costid][$state]['roundedvalue'];
                            $t['status'] = $newStatus;
                            array_push($newCalculatedValues[$costid][$newState]['broken'], $t);

                            unset($calculatedValuesOrder[$orderId]);
                        } else {
                            $newCalculatedValues[$costid][$newState]['roundedvalue'] = $calculatedValues[$costid][$state]['roundedvalue'];
                            $newCalculatedValues[$costid][$newState]['status'] = $newStatus;
                            $valuesOrder['state'] = $newState;
                        }
                        unset($calculatedValues[$costid][$state]);
                    } else {
                        $newCalculatedValues[$costid][$state]['roundedvalue'] = $calculatedValues[$costid][$state]['roundedvalue'];
                        $newCalculatedValues[$costid][$state]['status'] = Status::DRAFT;
                        unset($calculatedValues[$costid][$state]);
                    }
                    unset($calculatedValues[$costid][$state]);
                } elseif (substr($state, 0, 2) === self::SIGN_WORKTIME) {
                    $i = (substr($state, 0, 3) === 'wtw') ? 3 : 2;
                    $newState = 'otw' . substr($state, $i, strlen($state));
                    $newStatus = Status::PUBLISHED;
                    if (isset($calculatedValues[$costid][$newState])) {
                        $newCalculatedValues[$costid][$newState]['broken'] = [];
                        $t['roundedvalue'] = $calculatedValues[$costid][$state]['roundedvalue'];
                        $t['status'] = $newStatus;
                        array_push($newCalculatedValues[$costid][$newState]['broken'], $t);
                        unset($calculatedValuesOrder[$orderId]);
                    } else {
                        isset($newCalculatedValues[$costid][$newState]['roundedvalue'])
                            ? $newCalculatedValues[$costid][$newState]['roundedvalue'] += $calculatedValues[$costid][$state]['roundedvalue']
                            : $newCalculatedValues[$costid][$newState]['roundedvalue'] = $calculatedValues[$costid][$state]['roundedvalue'];
                        $newCalculatedValues[$costid][$newState]['status'] = $newStatus;
                        $valuesOrder['state'] = $newState;
                    }
                    unset($calculatedValues[$costid][$state]);
                }
            }
        }
        if ($newCalculatedValues) {
            $this->calc[$this->dayToProcess]['statusChanged'] = true;
            foreach ($calculatedValues as $key => $value) {
                if ($newCalculatedValues[$key]) {
                    $calculatedValues[$key] = $newCalculatedValues[$key];
                }
            }
        }
    }

    private function setBalanceToOvertime(&$calculatedValues, &$calculatedValuesOrder, $BalanceIDs)
    {
        $setOvertime = 0;

        isset($this->dayTypeRows[$this->dayToProcess]['shifts_with_prefix']['desun']) ? $sunPrefix = true : $sunPrefix = false;

        foreach ($BalanceIDs as $id) {
            $costid = $calculatedValuesOrder[$id]['costid'];
            $state = $calculatedValuesOrder[$id]['state'];
            $roundedValue = $calculatedValues[$costid][$state]['roundedvalue'];
            if ($roundedValue <= 0) {
                break;
            }
            $sunPrefix ? $prefix = 'sun' : $prefix = '';
            $newState = $this->workScheduleIsRestDay() ? 'otwde' . $prefix : 'otde' . $prefix;
            if (($setOvertime + $roundedValue) <= $this->dayTypeRows[$this->dayToProcess]['overtimeTime']) {
                $calculatedValues[$costid][$state]['status'] = 2;
                $roundedValueFromInterval = $this->getOvertimeIntervalFromValue($roundedValue);
                if ($roundedValue !== (int)$roundedValueFromInterval) {
                    $remaindRoundedValue = $calculatedValues[$costid][$state]['roundedvalue'] - $roundedValueFromInterval;
                    $calculatedValues[$costid][$state]['roundedvalue'] = $roundedValueFromInterval;

                    $calculatedValues[$costid][$state]['broken'] = [];
                    $t['roundedvalue'] = $remaindRoundedValue;
                    $t['state'] = $state;
                    $t['status'] = 2;
                    array_push($calculatedValues[$costid][$state]['broken'], $t);
                }
                $setOvertime += $roundedValue;
            } else {
                $calculatedValues[$costid][$state]['status'] = 2;

                $remaindOvertime = $this->getOvertimeIntervalFromValue(
                    $this->dayTypeRows[$this->dayToProcess]['overtimeTime'] - $setOvertime
                );

                $remaindRoundedValue = $calculatedValues[$costid][$state]['roundedvalue'] - $remaindOvertime;
                $calculatedValues[$costid][$state]['roundedvalue'] = $remaindOvertime;

                $calculatedValues[$costid][$state]['broken'] = [];
                $t['roundedvalue'] = $remaindRoundedValue;
                $t['state'] = $state;
                $t['status'] = 2;
                array_push($calculatedValues[$costid][$state]['broken'], $t);

                $setOvertime += $remaindRoundedValue;
            }

            $calculatedValuesOrder[$id]['state'] = $newState;
            foreach ($calculatedValues[$costid][$state] as $key => $value) {
                $calculatedValues[$costid][$newState][$key] = $value;
            }
            unset($calculatedValues[$costid][$state]);


            if ($setOvertime == $this->dayTypeRows[$this->dayToProcess]['overtimeTime']) {
                return;
            }
        }
    }

    private function setStatusToCalculatedValues(&$calculatedValues, $calculatedValuesOrder, $overtimeOrdesIDs)
    {
        $setOvertime = 0;
        foreach ($overtimeOrdesIDs as $id) {
            $costid = $calculatedValuesOrder[$id]['costid'];
            $state = $calculatedValuesOrder[$id]['state'];
            $roundedValue = $calculatedValues[$costid][$state]['roundedvalue'];

            $newState = $state;
            if (substr($state, 0, 3) === 'bot'
                && $this->BOT_ACCEPT_BY_OT_MANAGEMENT_TO_OT
            ) {
                $newState = ltrim($state, 'b');
            }
            if (($setOvertime + $roundedValue) <= $this->dayTypeRows[$this->dayToProcess]['overtimeTime']) {
                $calculatedValues[$costid][$state]['status'] = STATUS::PUBLISHED;
                $calculatedValues[$costid] = $this->change_array_key($calculatedValues[$costid], $state, $newState);
                $setOvertime += $roundedValue;
                $this->calc[$this->dayToProcess]['statusChanged'] = true;
            } else {
                $calculatedValues[$costid][$newState]['status'] = STATUS::PUBLISHED;
                $remaindOvertime = $this->dayTypeRows[$this->dayToProcess]['overtimeTime'] - $setOvertime;
                $remaindRoundedValue = $calculatedValues[$costid][$state]['roundedvalue'] - $remaindOvertime;
                $calculatedValues[$costid][$newState]['roundedvalue'] = $remaindOvertime;

                if ($newState === $state) {
                    $calculatedValues[$costid][$state]['broken'] = [];
                    $t['roundedvalue'] = $remaindRoundedValue;
                    $t['status'] = STATUS::DRAFT;
                    array_push($calculatedValues[$costid][$state]['broken'], $t);
                } else {
                    $acceptBOT = $this->BOT_ACCEPT_BY_OT_MANAGEMENT || $this->BOT_ACCEPT_BY_OT_MANAGEMENT_TO_OT;
                    $calculatedValues[$costid][$state]['roundedvalue'] = $remaindRoundedValue;
                    $calculatedValues[$costid][$state]['status'] = $acceptBOT ? STATUS::PUBLISHED : STATUS::DRAFT;
                }

                $setOvertime += $remaindOvertime;
            }

            if ($setOvertime == $this->dayTypeRows[$this->dayToProcess]['overtimeTime']) {
                return;
            }
        }
    }

    private function getBalanceOrderIDs(&$BalanceIDs, $calculatedValuesOrder)
    {
        foreach ($calculatedValuesOrder as $id => $values) {
            if ($values['state'] === 'balance') {
                if (!isset($BalanceIDs)) {
                    $BalanceIDs = [];
                }
                array_push($BalanceIDs, $id);
            }
        }
    }

    private function getBeforeWorkOvertimeOrderIDs(&$beforeWorkOvertimeOrdesIDs, $calculatedValuesOrder)
    {
        foreach ($calculatedValuesOrder as $id => $values) {
            $acceptBOT = $this->BOT_ACCEPT_BY_OT_MANAGEMENT || $this->BOT_ACCEPT_BY_OT_MANAGEMENT_TO_OT;
            if ((
                substr($values['state'], 0, 2) === self::SIGN_OVERTIME
                || (substr($values['state'], 0, 3) === 'bot' && $acceptBOT)
            )
            ) {
                if (!isset($beforeWorkOvertimeOrdesIDs)) {
                    $beforeWorkOvertimeOrdesIDs = [];
                }
                array_push($beforeWorkOvertimeOrdesIDs, $id);
            } else {
                return;
            }
        }
    }

    private function getAfterWorkOvertimeOrderIDs(&$afterWorkOvertimeOrdesIDs, $calculatedValuesOrder)
    {
        $isWorktime = false;
        foreach ($calculatedValuesOrder as $id => $values) {
            if (substr($values['state'], 0, 2) === self::SIGN_WORKTIME and $isWorktime === false) {
                $isWorktime = true;
            }
            $acceptBOT = $this->BOT_ACCEPT_BY_OT_MANAGEMENT || $this->BOT_ACCEPT_BY_OT_MANAGEMENT_TO_OT;
            if ((
                    substr($values['state'], 0, 2) === self::SIGN_OVERTIME
                    || (substr($values['state'], 0, 3) === 'bot' && $acceptBOT)
                )
                and $isWorktime === true) {
                if (!isset($afterWorkOvertimeOrdesIDs)) {
                    $afterWorkOvertimeOrdesIDs = [];
                }
                array_push($afterWorkOvertimeOrdesIDs, $id);
            }
        }
    }

    private function getOvertimeOrderIds($calculatedValuesOrder)
    {
        $overtimeOrdesIDs = [];
        foreach ($calculatedValuesOrder as $id => $values) {
            $acceptBOT = $this->BOT_ACCEPT_BY_OT_MANAGEMENT || $this->BOT_ACCEPT_BY_OT_MANAGEMENT_TO_OT;
            if (substr($values['state'], 0, 2) === self::SIGN_OVERTIME
                || (substr($values['state'], 0, 3) === 'bot' && $acceptBOT)
            ) {
                array_push($overtimeOrdesIDs, $id);
            }
        }
        return $overtimeOrdesIDs;
    }

    private function reverseOrderOverTime(&$array)
    {
        if (is_array($array)) {
            $array = array_reverse($array);
            foreach ($array as $costid => &$names) {
                if (is_array($names)) {
                    $names = array_reverse($names);
                }
            }
        }
    }

    private function postOvertimeInterval(&$calculatedValues, &$calculatedValuesOrder)
    {
        if ($this->calc[$this->dayToProcess]['statusChanged'] === false) {
            return;
        }
        $sumOvertimeValue = $this->getSumOvertimeValueFromCalculatedValues($calculatedValues);
        if ($this->dayTypeRows[$this->dayToProcess]['overtimeTime'] < $sumOvertimeValue['value']) {
            return;
        }

        $diff = $sumOvertimeValue['value'] - $sumOvertimeValue['roundedValue'];
        $reverseCalculatedValuesOrder = $this->getOvertimeOrderIds($calculatedValuesOrder);
        $this->reverseOrderOverTime($reverseCalculatedValuesOrder);
        foreach ($reverseCalculatedValuesOrder as $id) {
            $costid = $calculatedValuesOrder[$id]['costid'];
            $state = $calculatedValuesOrder[$id]['state'];
            $roundedValue = $calculatedValues[$costid][$state]['roundedvalue'];
            if ($roundedValue > $diff) {
                $calculatedValues[$costid][$state]['roundedvalue'] -= $diff;
                $diff = 0;
            } else {
                $diff -= $roundedValue;
                unset($calculatedValues[$costid][$state]);
            }
            if ($diff === 0) {
                return;
            }
        }
    }

    private function getSumOvertimeValueFromCalculatedValues($calculatedValues)
    {
        $out['value'] = 0;
        foreach ($calculatedValues as $costId => $states) {
            foreach ($states as $stateId => $values) {
                $acceptBOT = $this->BOT_ACCEPT_BY_OT_MANAGEMENT || $this->BOT_ACCEPT_BY_OT_MANAGEMENT_TO_OT;
                if (substr($stateId, 0, 2) === self::SIGN_OVERTIME
                    || (substr($stateId, 0, 3) === 'bot' && $acceptBOT)
                ) {
                    $out['value'] += $values['roundedvalue'];
                }
            }
        }
        if ($out['value'] > 0) {
            $out['roundedValue'] = $this->getOvertimeIntervalFromValue($out['value']);
        }
        return $out;
    }

    public function workScheduleIsRestDay(string $dayToProcess = null): bool
    {
        $dayToProcess = $dayToProcess ?? $this->dayToProcess;
        return isset($this->dayTypeRows[$dayToProcess]['used_type_of_daytype']) &&
            $this->dayTypeRows[$dayToProcess]['used_type_of_daytype'] ===
            WorkScheduleUsedTypeOfDayTypeSourceEnum::RESTDAY;
    }

    public function workScheduleIsCompensatoryDay(string $dayToProcess = null): bool
    {
        $dayToProcess = $dayToProcess ?? $this->dayToProcess;
        return isset($this->dayTypeRows[$dayToProcess]['used_type_of_daytype']) &&
            $this->dayTypeRows[$dayToProcess]['used_type_of_daytype'] ===
            WorkScheduleUsedTypeOfDayTypeSourceEnum::COMPENSATORYDAY;
    }

    public function workScheduleIsWorkDay(string $dayToProcess = null): bool
    {
        $dayToProcess = $dayToProcess ?? $this->dayToProcess;
        return isset($this->dayTypeRows[$dayToProcess]['used_type_of_daytype']) &&
            $this->dayTypeRows[$dayToProcess]['used_type_of_daytype'] ===
            WorkScheduleUsedTypeOfDayTypeSourceEnum::WORKDAY;
    }

    private function array_insert(&$array, $position, $insert)
    {
        if (is_int($position)) {
            array_splice($array, $position, 0, $insert);
        } else {
            $pos = array_search($position, array_keys($array));
            $a = array_slice($array, 0, $pos);
            $b = array_slice($array, $pos);
            $array = $a + $insert + $b;
        }
    }

    private function getCostCostcenterByCostMode(&$cost, $costid)
    {
        switch ($this->COST_MODE) {
            case 'COST':
                $cost['costid'] = $costid;
                $cost['costcenterid'] = isset($this->employeeMainData['def_cost_center_id']) ? $this->employeeMainData['def_cost_center_id'] : 1;
                break;
            case 'COSTCENTER':
                $cost['costid'] = isset($this->employeeMainData['def_cost_id']) ? $this->employeeMainData['def_cost_id'] : 1;
                $cost['costcenterid'] = $costid;
                break;
            case 'ACTIVITY':
                unset($tmp);
                $tmp = explode(';', $costid);
                $cost['costid'] = $tmp[0];
                if (count($tmp) === 2) {
                    $cost['costcenterid'] = $tmp[1];
                    $cc = $this->costCenter->get($tmp[1], $this->dayToProcess);
                    if ($cc['improductive'] == 1) {
                        $this->calc[$this->dayToProcess]['warning']['isImproductiveTime'] = true;
                    }
                }
                break;
        }
    }

    private function getOtherRuleWorkTypeWithoutAcs(): bool
    {
        if (empty($this->RULE_WORK_TYPE_WITHOUT_ACS_PAYROLL)) {
            return false;
        }

        return $this->RULE_WORK_TYPE_WITHOUT_ACS_PAYROLL == $this->employeeMainData['payroll_id'];
    }

    private function workTypeWithoutACS(&$dailyRegistration)
    {
        if ($this->dayTypeRows[$this->dayToProcess]['use_acs'] !== false || $this->getOtherRuleWorkTypeWithoutAcs()) {
            return;
        }

        $otherRegType = array_filter(
            $dailyRegistration,
            function ($value, $key) {
                return !in_array($value['event_type_id'], [self::NMB_EVENT_TYPE_ID, self::NMK_EVENT_TYPE_ID]);
            },
            ARRAY_FILTER_USE_BOTH
        );

        $otherRegTypeRule = $this->AUTOMATIC_REGS_OTHER_REGS_RULE && !empty($otherRegType);
        if ($otherRegTypeRule) {
            return;
        }

        $onlyNMBAndNMK = [];
        foreach ($dailyRegistration as $regKey => $regValues) {
            if (in_array($regValues['event_type_id'], [self::NMB_EVENT_TYPE_ID, self::NMK_EVENT_TYPE_ID])) {
                $onlyNMBAndNMK[] = $regValues;
                unset($dailyRegistration[$regKey]);
            }
        }
        $holiday = $this->publicHolidays->get(
            $this->dayToProcess,
            $this->PAID_PUBLIC_HOLIDAY_TYPES,
            $this->employeeMainData['country']
        );
        if ($this->HOLIDAY_AS_WORKDAY && !empty($holiday)) {
            $holiday = [];
        }

        $deleteInOutRegs = ($this->isAbsenceExists && $this->isAbsenceFullDay) ||
            ($this->isAbsenceExists &&
                !$this->isAbsenceFullDay &&
                $this->AUTOMATIC_REGS_MODIFY_ABSENCE_CASE === 'DELETE');

        if ($this->isAbsenceExists === false
            && $this->workScheduleIsWorkDay()
            && empty($holiday)
        ) {
            $use4regs = $this->dayTypeRows[$this->dayToProcess]['used_pause_start'] > 0 && $this->dayTypeRows[$this->dayToProcess]['used_pause_end'] > 0;
            if ((bool)$this->SETTING_4_REG_TURNED_ON) {
                $use4regs = false;
                if (!empty($this->SETTING_4_REG_WORKGROUP)
                    && !empty($this->SETTING_4_REG_DAY_TYPES)
                    && (strpos($this->employeeMainData['workgroup_name'], $this->SETTING_4_REG_WORKGROUP) !== false)) {
                    if (in_array(
                        $this->dayTypeRows[$this->dayToProcess]['used_daytype_id'],
                        explode(';', $this->SETTING_4_REG_DAY_TYPES)
                    )) {
                        $use4regs = true;
                    }
                }
            }
            if (empty($onlyNMBAndNMK)) {
                $this->insertInOutRegistrationWhere_WorkTypeWithoutACS($onlyNMBAndNMK, $use4regs);
            } elseif (count($onlyNMBAndNMK) === 1 || ($use4regs && count($onlyNMBAndNMK) < 4)) {
                $this->deleteInOutRegistrationWhere_WorkTypeWithoutACS($onlyNMBAndNMK);
                $this->insertInOutRegistrationWhere_WorkTypeWithoutACS($onlyNMBAndNMK, $use4regs);
            } elseif (count($onlyNMBAndNMK) === 2 && !$use4regs) {
                $wtstart = $this->convertTimestampToString($this->dayTypeRows[$this->dayToProcess]['used_work_start']);
                $wtstop = $this->convertTimestampToString($this->dayTypeRows[$this->dayToProcess]['used_work_end']);
                if ($onlyNMBAndNMK[0]['event_type_id'] === self::NMB_EVENT_TYPE_ID &&
                    $onlyNMBAndNMK[0]['reg_time'] !== $wtstart) {
                    $this->updateInOutRegistrationWhere_WorkTypeWithoutACS($onlyNMBAndNMK[0], $wtstart);
                }
                if ($onlyNMBAndNMK[1]['event_type_id'] === self::NMK_EVENT_TYPE_ID &&
                    $onlyNMBAndNMK[1]['reg_time'] !== $wtstop) {
                    $this->updateInOutRegistrationWhere_WorkTypeWithoutACS($onlyNMBAndNMK[1], $wtstop);
                }
            }
        } elseif ((!empty($holiday)
                || $this->workScheduleIsRestDay()
                || $deleteInOutRegs
            )
            && !empty($onlyNMBAndNMK)
        ) {
            $this->deleteInOutRegistrationWhere_WorkTypeWithoutACS($onlyNMBAndNMK);
        }
        $dailyRegistration = array_merge($onlyNMBAndNMK, $dailyRegistration);
    }

    private function convertTimestampToString($ts)
    {
        $dateValue = new DateTime("@$ts", new DateTimeZone(self::DEFAULT_DATE_TIMEZONE));
        return $dateValue->Format($this->WfmDateTimeFormat);
    }

    private function deleteInOutRegistrationWhere_WorkTypeWithoutACS(&$dailyRegistration)
    {
        foreach ($dailyRegistration as $rowcount => &$regRow) {
            $r = Registration::model()->findByPk($regRow['reg_row_id']);
            if ($r->created_by === 'Automatic' and (is_null($r->modified_by) or $r->modified_by === 'Automatic')) {
                $r->modified_by = 'Automatic';
                $r->status = Status::DELETED;
                $r->save();
                unset($dailyRegistration[$rowcount]);
            }
        }
    }

    private function insertInOutRegistrationWhere_WorkTypeWithoutACS(&$dailyRegistration, $use4regs = false)
    {
        $ts = $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
        $workStartDate = new DateTime("@$ts", new DateTimeZone(self::DEFAULT_DATE_TIMEZONE));
        $r = new Registration();
        $r->setIsNewRecord(true);
        $r->time = $workStartDate->format($this->WfmDateTimeFormat);
        $r->card = (string)$this->employeeMainData['card'];
        $r->terminal_id = 0;
        $r->reader_id = 0;
        $r->event_type_id = self::NMB_EVENT_TYPE_ID;
        $r->cost_id = $this->employeeMainData['def_cost_id'];
        $r->sync = 0;
        $r->status = Status::PUBLISHED;
        $r->created_by = 'Automatic';

        if ($r->validate()) {
            $r->save();
        } else {
            throw new Exception('Insert registration (NMB) ' . MyActiveForm::_validate($r));
        }
        $dailyRegistration[0] = $r->getAttributes();
        $dailyRegistration[0]['reg_row_id'] = $dailyRegistration[0]['row_id'];
        $dailyRegistration[0]['time'] = $workStartDate->format($this->WfmDateTimeFormat);
        $dailyRegistration[0]['reg_time'] = $workStartDate->format($this->WfmDateTimeFormat);
        $dailyRegistration[0]['from_work_start'] = 0;
        $dailyRegistration[0]['to_work_end'] = 0;

        unset($r);
        unset($ts);

        if ($use4regs) {
            $ts = $this->dayTypeRows[$this->dayToProcess]['used_pause_start'];
            $workStartDate = new DateTime("@$ts", new DateTimeZone(self::DEFAULT_DATE_TIMEZONE));
            $r = new Registration();
            $r->setIsNewRecord(true);
            $r->time = $workStartDate->format($this->WfmDateTimeFormat);
            $r->card = (string)$this->employeeMainData['card'];
            $r->terminal_id = 0;
            $r->reader_id = 0;
            $r->event_type_id = self::NMK_EVENT_TYPE_ID;
            $r->cost_id = $this->employeeMainData['def_cost_id'];
            $r->sync = 0;
            $r->status = Status::PUBLISHED;
            $r->created_by = 'Automatic';

            if ($r->validate()) {
                $r->save();
            } else {
                throw new Exception('Insert registration (NMB) ' . MyActiveForm::_validate($r));
            }
            $dailyRegistration[2] = $r->getAttributes();
            $dailyRegistration[2]['reg_row_id'] = $dailyRegistration[0]['row_id'];
            $dailyRegistration[2]['time'] = $workStartDate->format($this->WfmDateTimeFormat);
            $dailyRegistration[2]['reg_time'] = $workStartDate->format($this->WfmDateTimeFormat);
            $dailyRegistration[2]['from_work_start'] = 0;
            $dailyRegistration[2]['to_work_end'] = 0;

            unset($r);
            unset($ts);

            $ts = $this->dayTypeRows[$this->dayToProcess]['used_pause_end'];
            $workStartDate = new DateTime("@$ts", new DateTimeZone(self::DEFAULT_DATE_TIMEZONE));
            $r = new Registration();
            $r->setIsNewRecord(true);
            $r->time = $workStartDate->format($this->WfmDateTimeFormat);
            $r->card = (string)$this->employeeMainData['card'];
            $r->terminal_id = 0;
            $r->reader_id = 0;
            $r->event_type_id = self::NMB_EVENT_TYPE_ID;
            $r->cost_id = $this->employeeMainData['def_cost_id'];
            $r->sync = 0;
            $r->status = Status::PUBLISHED;
            $r->created_by = 'Automatic';

            if ($r->validate()) {
                $r->save();
            } else {
                throw new Exception('Insert registration (NMB) ' . MyActiveForm::_validate($r));
            }
            $dailyRegistration[3] = $r->getAttributes();
            $dailyRegistration[3]['reg_row_id'] = $dailyRegistration[0]['row_id'];
            $dailyRegistration[3]['time'] = $workStartDate->format($this->WfmDateTimeFormat);
            $dailyRegistration[3]['reg_time'] = $workStartDate->format($this->WfmDateTimeFormat);
            $dailyRegistration[3]['from_work_start'] = 0;
            $dailyRegistration[3]['to_work_end'] = 0;

            unset($r);
            unset($ts);
        }

        $ts = $this->dayTypeRows[$this->dayToProcess]['used_work_end'];
        $r = new Registration();
        $r->setIsNewRecord(true);
        $workEndDate = new DateTime("@$ts", new DateTimeZone(self::DEFAULT_DATE_TIMEZONE));
        $r->time = $workEndDate->format($this->WfmDateTimeFormat);
        $r->card = (string)$this->employeeMainData['card'];
        $r->terminal_id = 0;
        $r->reader_id = 0;
        $r->event_type_id = self::NMK_EVENT_TYPE_ID;
        $r->cost_id = $this->employeeMainData['def_cost_id'];
        $r->sync = 0;
        $r->status = Status::PUBLISHED;
        $r->created_by = 'Automatic';
        $r->created_on = date(AppConfigEnum::DATE_TIME_FORMAT);

        if ($r->validate()) {
            $r->save();
        } else {
            throw new Exception('Insert registration (NMK) ' . MyActiveForm::_validate($r));
        }
        $dailyRegistration[1] = $r->getAttributes();
        $dailyRegistration[1]['reg_row_id'] = $dailyRegistration[1]['row_id'];
        $dailyRegistration[1]['time'] = $workEndDate->Format(AppConfigEnum::DATE_TIME_FORMAT);
        $dailyRegistration[1]['reg_time'] = $workEndDate->Format(AppConfigEnum::DATE_TIME_FORMAT);
        $dailyRegistration[1]['from_work_start'] = 0;
        $dailyRegistration[1]['to_work_end'] = 0;

        unset($r);
    }

    private function updateInOutRegistrationWhere_WorkTypeWithoutACS(&$dailyRegistration, $regTime)
    {
        $r = Registration::model()->findByPk($dailyRegistration['reg_row_id']);
        if ($r->created_by === 'Automatic' and (is_null($r->modified_by) or $r->modified_by === 'Automatic')) {
            $r->time = $regTime;
            $r->created_by = 'Automatic';
            $r->modified_by = 'Automatic';
            $r->save();
            $dailyRegistration['reg_time'] = $regTime;
        }
    }

    private function getOvertimeIntervalFromValue($value)
    {
        $overtime_interval_min = $this->dayTypeRows[$this->dayToProcess]['overtime_interval_min']; //túlóra lépésköz
        if ($overtime_interval_min > 1) {
            $value = $this->roundByWorkgroupRoundType($value, $overtime_interval_min, 'overtime');
        }
        return $value;
    }

    private function checkInputs()
    {
        if (empty($this->employeeContractIds) || empty($this->startDate) || empty($this->endDate)) {
            throw new Exception('Missing parameters in payrollcalculation()');
        }
        $date = DateTime::createFromFormat(AppConfigEnum::DATE_FORMAT, $this->startDate);
        if ($date === false or !(date_format($date, AppConfigEnum::DATE_FORMAT) == $this->startDate)) {
            throw new Exception($this->startDate . ' is not a date in payrollcalculation()');
        }
        $date = DateTime::createFromFormat(AppConfigEnum::DATE_FORMAT, $this->endDate);
        if ($date === false or !(date_format($date, AppConfigEnum::DATE_FORMAT) == $this->endDate)) {
            throw new Exception($this->startDate . ' is not a date in payrollcalculation()');
        }
    }

    private function timeRound($time, $roundingSeconds): float
    {
        return round($time / ($roundingSeconds)) * ($roundingSeconds);
    }

    private function roundByWorkgroupRoundType(
        $value,
        $significance = 1,
        $type = 'worktime'
    ) // https://innote.login.hu/n-7da20qta
    {
        if ($this->BALANCE_ROUND_TOLERANCE === true
            && $type == 'balance'
            && $this->dayTypeRows[$this->dayToProcess]['balanceroundtolerance'] > 0
        ) {
            $value +=
                ($this->dayTypeRows[$this->dayToProcess]['balanceroundtolerance'] <
                $this->dayTypeRows[$this->dayToProcess]['balance_step'] ?
                    $this->dayTypeRows[$this->dayToProcess]['balanceroundtolerance'] :
                    $this->dayTypeRows[$this->dayToProcess]['balance_step'] - 60
                );
        }

        if ($this->WORKGROUP_SHOW_ROUND_TYPES === false
            || !isset($this->employeeMainData[$type . '_interval_min_type'])) {
            return $this->customizedRoundDown($value, $significance);
        }
        switch ((int)$this->employeeMainData[$type . '_interval_min_type']) {
            case WorktimeRoundTypeEnum::NORMAL:
                return $this->customizedRound($value, $significance);
            case WorktimeRoundTypeEnum::UP:
                return $this->customizedRoundUp($value, $significance);
            default:
                return $this->customizedRoundDown($value, $significance);
        }
    }

    private function customizedRound($value, $significance = 1)
    {
        if ($significance === 0) {
            return $value;
        }
        return $significance * round($value / $significance);
    }

    private function customizedRoundDown($value, $significance = 1)
    {
        if ($significance === 0) {
            return $value;
        }
        return $significance * floor($value / $significance);
    }

    private function customizedRoundUp($value, $significance = 1)
    {
        if ($significance === 0) {
            return $value;
        }
        $value += $significance;
        return $significance * floor($value / $significance);
    }

    private function setDailyConst($dayToProcess)
    {
        $this->DE_START = $this->convertTimeStringToSeconds('06:00:00', $dayToProcess);
        $this->DU_START1 = $this->convertTimeStringToSeconds('14:00:00', $dayToProcess);
        $this->DU_START2 = $this->convertTimeStringToSeconds('18:00:00', $dayToProcess);
        $this->EJ_START = $this->convertTimeStringToSeconds('22:00:00', $dayToProcess);
        //$this->PREEJSTART = strtotime('-1 day', $this->EJSTART);
        $this->EJ_STOP = strtotime('+1 day', $this->DE_START);
    }

    private function setWorktimeOvertime($costid, &$work, &$workstart, &$workstop)
    {
        $in = $workstart;
        $out = $workstop;

        $otde = 'otde';
        $otdu1 = 'otdu1';
        $otdu2 = 'otdu2';
        $otej = 'otej';

        $positive = false;
        $negative = true;

        if ($work < 0)    //FRAMEWORK_BALANCE miatt ha munkaidőben kimegy aztán bejön
        {
            if ($in >= $this->EJ_START) {
                if ($out <= $this->EJ_STOP) {
                    if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otej', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtej', $workstart, $workstop, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift']) {
                        $this->setOverTimetoCalc($costid, 'otdu2', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu2', $workstart, $workstop, $positive);
                    } else {
                        $this->setOverTimetoCalc($costid, 'otde', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $workstart, $workstop, $positive);
                    }
                }
            } elseif ($in >= $this->DU_START2) {
                if ($out >= $this->EJ_START) {
                    if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0 and $this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otde', $in, $this->EJ_START, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $in, $this->EJ_START, $positive);
                        $this->setOverTimetoCalc($costid, 'otej', $this->EJ_START, $out, $negative);
                        $this->setOverTimetoCalc($costid, 'wtej', $this->EJ_START, $out, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otdu2', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu2', $workstart, $workstop, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift']) {
                        $this->setOverTimetoCalc($costid, 'otde', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $workstart, $workstop, $positive);
                    } else {
                        $this->setOverTimetoCalc($costid, 'otej', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtej', $workstart, $workstop, $positive);
                    }
                } else {
                    if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otdu2', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu2', $workstart, $workstop, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift']) {
                        $this->setOverTimetoCalc($costid, 'otde', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $workstart, $workstop, $positive);
                    } else {
                        $this->setOverTimetoCalc($costid, 'otej', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtej', $workstart, $workstop, $positive);
                    }
                }
            } elseif ($in >= $this->DU_START1) {
                if ($out >= $this->EJ_START) {
                    if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0 and $this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otdu1', $in, $this->DU_START2, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu1', $in, $this->DU_START2, $positive);
                        $this->setOverTimetoCalc($costid, 'otdu2', $this->DU_START2, $this->EJ_START, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu2', $this->DU_START2, $this->EJ_START, $positive);
                        $this->setOverTimetoCalc($costid, 'otej', $this->EJ_START, $out, $negative);
                        $this->setOverTimetoCalc($costid, 'wtej', $this->EJ_START, $out, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otdu1', $in, $this->DU_START1, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu1', $in, $this->DU_START1, $positive);
                        $this->setOverTimetoCalc($costid, 'otdu2', $this->DU_START1, $out, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu2', $this->DU_START1, $out, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift']) {
                        $this->setOverTimetoCalc($costid, 'otde', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $workstart, $workstop, $positive);
                    } else {
                        $this->setOverTimetoCalc($costid, 'otej', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtej', $workstart, $workstop, $positive);
                    }
                } elseif ($out >= $this->DU_START2) {
                    if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otdu1', $in, $this->DU_START1, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu1', $in, $this->DU_START1, $positive);
                        $this->setOverTimetoCalc($costid, 'otdu2', $this->DU_START1, $out, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu2', $this->DU_START1, $out, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift']) {
                        $this->setOverTimetoCalc($costid, 'otde', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $workstart, $workstop, $positive);
                    } else {
                        $this->setOverTimetoCalc($costid, 'otej', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtej', $workstart, $workstop, $positive);
                    }
                } else {
                    if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otdu1', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu1', $workstart, $workstop, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift']) {
                        $this->setOverTimetoCalc($costid, 'otde', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $workstart, $workstop, $positive);
                    } else {
                        $this->setOverTimetoCalc($costid, 'otej', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtej', $workstart, $workstop, $positive);
                    }
                }
            } else {
                if ($out >= $this->EJ_START) {
                    if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift'] > 0 and $this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0 and $this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otde', $in, $this->DU_START1, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $in, $this->DU_START1, $positive);
                        $this->setOverTimetoCalc($costid, 'otdu1', $this->DU_START1, $this->DU_START2, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu1', $this->DU_START1, $this->DU_START2, $positive);
                        $this->setOverTimetoCalc($costid, 'otdu2', $this->DU_START2, $this->EJ_START, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu2', $this->DU_START2, $this->EJ_START, $positive);
                        $this->setOverTimetoCalc($costid, 'otej', $this->EJ_START, $out, $negative);
                        $this->setOverTimetoCalc($costid, 'wtej', $this->EJ_START, $out, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift'] > 0 and $this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otde', $in, $this->DU_START1, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $in, $this->DU_START1, $positive);
                        $this->setOverTimetoCalc($costid, 'otdu1', $this->DU_START1, $this->DU_START2, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu1', $this->DU_START1, $this->DU_START2, $positive);
                        $this->setOverTimetoCalc($costid, 'otdu2', $this->DU_START2, $out, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu2', $this->DU_START2, $out, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otde', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $workstart, $workstop, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift']) {
                        $this->setOverTimetoCalc($costid, 'otej', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtej', $workstart, $workstop, $positive);
                    } else {
                        $this->setOverTimetoCalc($costid, 'otdu1', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu1', $workstart, $workstop, $positive);
                    }
                } elseif ($out >= $this->DU_START2) {
                    if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift'] > 0 and $this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otde', $in, $this->DU_START1, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $in, $this->DU_START1, $positive);
                        $this->setOverTimetoCalc($costid, 'otdu1', $this->DU_START1, $this->DU_START2, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu1', $this->DU_START1, $this->DU_START2, $positive);
                        $this->setOverTimetoCalc($costid, 'otdu2', $this->DU_START2, $out, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu2', $this->DU_START2, $out, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otde', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $workstart, $workstop, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift']) {
                        $this->setOverTimetoCalc($costid, 'otej', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtej', $workstart, $workstop, $positive);
                    } else {
                        $this->setOverTimetoCalc($costid, 'otdu1', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu1', $workstart, $workstop, $positive);
                    }
                } elseif ($out >= $this->DU_START1) {
                    if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift'] > 0 and $this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otde', $in, $this->DU_START1, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $in, $this->DU_START1, $positive);
                        $this->setOverTimetoCalc($costid, 'otdu1', $this->DU_START1, $out, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu1', $this->DU_START1, $out, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otde', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $workstart, $workstop, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift']) {
                        $this->setOverTimetoCalc($costid, 'otej', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtej', $workstart, $workstop, $positive);
                    } else {
                        $this->setOverTimetoCalc($costid, 'otdu1', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu1', $workstart, $workstop, $positive);
                    }
                } else {
                    if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift'] > 0) {
                        $this->setOverTimetoCalc($costid, 'otde', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtde', $workstart, $workstop, $positive);
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift']) {
                        $this->setOverTimetoCalc($costid, 'otej', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtej', $workstart, $workstop, $positive);
                    } else {
                        $this->setOverTimetoCalc($costid, 'otdu1', $workstart, $workstop, $negative);
                        $this->setOverTimetoCalc($costid, 'wtdu1', $workstart, $workstop, $positive);
                    }
                }
            }
        } else {
            if ($in <= $this->dayTypeRows[$this->dayToProcess]['used_work_start']) {
                if ($out <= $this->dayTypeRows[$this->dayToProcess]['used_work_start']) {
                    $otbeforeworkstart = $in;
                    $otbeforeworkstop = $out;
                    $otbeforework = ($otbeforeworkstop - $otbeforeworkstart);
                    $work = 0;
                } elseif ($out <= $this->dayTypeRows[$this->dayToProcess]['used_work_end']) {
                    $otbeforeworkstart = $in;
                    $otbeforeworkstop = $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
                    $workstart = $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
                    $otbeforework = ($otbeforeworkstop - $otbeforeworkstart);
                    $work = ($workstop - $this->dayTypeRows[$this->dayToProcess]['used_work_start']);
                } else {
                    $otbeforeworkstart = $in;
                    $otbeforeworkstop = $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
                    $workstart = $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
                    $workstop = $this->dayTypeRows[$this->dayToProcess]['used_work_end'];
                    $otafterworkstart = $this->dayTypeRows[$this->dayToProcess]['used_work_end'];
                    $otafterworkstop = $out;
                    $otbeforework = ($this->dayTypeRows[$this->dayToProcess]['used_work_start'] - $in);
                    $work = ($workstop - $workstart);
                    $otafterwork = ($otafterworkstop - $otafterworkstart);
                }
            } elseif ($out <= $this->dayTypeRows[$this->dayToProcess]['used_work_end']) {
                $work = ($out - $in);
            } else {
                if ($in < $this->dayTypeRows[$this->dayToProcess]['used_work_end']) {
                    $workstop = $this->dayTypeRows[$this->dayToProcess]['used_work_end'];
                    $otafterworkstart = $this->dayTypeRows[$this->dayToProcess]['used_work_end'];
                    $otafterworkstop = $out;
                    $work = ($this->dayTypeRows[$this->dayToProcess]['used_work_end'] - $in);
                    $otafterwork = ($otafterworkstop - $otafterworkstart);
                } else {
                    $work = 0;
                    $otafterworkstart = $in;
                    $otafterworkstop = $out;
                    $otafterwork = ($otafterworkstop - $otafterworkstart);
                }
            }
        }
        if (isset($otbeforework) and $otbeforework > 0) {
            $sundaySignInterval = $this->employeeMainData['sunday_sign_interval'] ?? '';
            if ($this->SHIFT_START_IN_DAYTYPE) {
                $workTimeToShift = new WorkTimeToShift(
                    $this->employeeWorkSchedule->getShiftStartType($this->employeeContractId, $this->dayToProcess),
                    $sundaySignInterval
                );
            } else {
                $workTimeToShift = new WorkTimeToShift(
                    $this->employeesWithMainData->getShiftStartType($this->employeeContractId, $this->dayToProcess),
                    $sundaySignInterval
                );
            }
            if ($this->STANDBY_SIGN) {
                $intersectionDescriptors = $this->getOvertimeStandByOverlap($otbeforeworkstart, $otbeforeworkstop);
                $this->writeOvertimeToCalcFromIntervalDescriptor(
                    $costid ?? '',
                    $workTimeToShift,
                    $intersectionDescriptors
                );
            } else {
                $workTimeToShift
                    ->setPublicHolidays($this->publicHolidaysThisCountry)
                    ->setEmployeeMainData($this->employeeMainData);
                $writeArray = $workTimeToShift->getShift(
                    $this->dayToProcess,
                    $otbeforeworkstart,
                    $otbeforeworkstop
                );

                if (is_array($writeArray)) {
                    foreach ($writeArray as $write) {
                        $insideType = self::SIGN_OVERTIME .
                            $this->changeCalcTypeDependOnWorkOrder($write['insideType']);

                        $this->setClaimtoCalc($costid, $insideType, $write['value']);
                    }
                } else {
                    Log::create($this, 'LOG_calc_error-dt_setting-night_shift_pre_day');
                }
            }
            isset($this->calc[$this->dayToProcess]['otbeforework']) ?
                $this->calc[$this->dayToProcess]['otbeforework'] += $otbeforework :
                $this->calc[$this->dayToProcess]['otbeforework'] = $otbeforework;
        }
        if ($work > 0) {
            if ($this->workScheduleIsRestDay()) {
                $signModifier[self::SIGN_MODIFIER_ACCEPTED_OVERTIME] = true;
                $this->setOvertime($costid, $work, $workstart, $workstop, $signModifier);
            } else {
                $this->setWorktime($costid, $work, $workstart, $workstop);
            }

            $work = 0;
        }
        if (isset($otafterwork) and $otafterwork > 0) {
            $sundaySignInterval = $this->employeeMainData['sunday_sign_interval'] ?? '';
            if ($this->SHIFT_START_IN_DAYTYPE) {
                $workTimeToShift = new WorkTimeToShift(
                    $this->employeeWorkSchedule->getShiftStartType($this->employeeContractId, $this->dayToProcess),
                    $sundaySignInterval
                );
            } else {
                $workTimeToShift = new WorkTimeToShift(
                    $this->employeesWithMainData->getShiftStartType($this->employeeContractId, $this->dayToProcess),
                    $sundaySignInterval
                );
            }
            if ($this->STANDBY_SIGN) {
                $intersectionDescriptors = $this->getOvertimeStandByOverlap($otafterworkstart, $otafterworkstop);
                $this->writeOvertimeToCalcFromIntervalDescriptor(
                    $costid ?? '',
                    $workTimeToShift,
                    $intersectionDescriptors
                );
            } else {
                $workTimeToShift
                    ->setPublicHolidays($this->publicHolidaysThisCountry)
                    ->setEmployeeMainData($this->employeeMainData);
                $writeArray = $workTimeToShift->getShift(
                    $this->dayToProcess,
                    $otafterworkstart,
                    $otafterworkstop
                );

                foreach ($writeArray as $write) {
                    $insideType = self::SIGN_OVERTIME . $this->changeCalcTypeDependOnWorkOrder($write['insideType']);

                    $this->setOverTimetoCalc($costid, $insideType, $write['workStart'], $write['workStop'], false);
                }
            }
            isset($this->calc[$this->dayToProcess]['otafterwork']) ?
                $this->calc[$this->dayToProcess]['otafterwork'] += $otafterwork :
                $this->calc[$this->dayToProcess]['otafterwork'] = $otafterwork;
        }
    }

    private function setWorktime($costid, $work, $workstart, $workstop): void
    {
        if (!isset($workstart) or !isset($workstop)) {
            // 'Error - Missing parameter $workstart or $workstop'
            return;
        }

        $sundaySignInterval = $this->employeeMainData['sunday_sign_interval'] ?? '';
        if ($this->SHIFT_START_IN_DAYTYPE) {
            $workTimeToShift = new WorkTimeToShift(
                $this->employeeWorkSchedule->getShiftStartType($this->employeeContractId, $this->dayToProcess),
                $sundaySignInterval
            );
        } else {
            $workTimeToShift = new WorkTimeToShift(
                $this->employeesWithMainData->getShiftStartType($this->employeeContractId, $this->dayToProcess),
                $sundaySignInterval
            );
        }
        if ($this->STANDBY_SIGN) {
            $intersectionDescriptors = $this->getOvertimeStandByOverlap($workstart, $workstop);
            $this->writeWorktimeToCalcFromIntervalDescriptor(
                $costid ?? '',
                $workTimeToShift,
                $intersectionDescriptors
            );
        } else {
            $workTimeToShift
                ->setPublicHolidays($this->publicHolidaysThisCountry)
                ->setEmployeeMainData($this->employeeMainData);
            $writeArray = $workTimeToShift->getShift(
                $this->dayToProcess,
                $workstart,
                $workstop
            );
            foreach ($writeArray as $write) {
                $insideType = self::SIGN_WORKTIME . $this->changeCalcTypeDependOnWorkOrder($write['insideType']);
                $this->setWorkTimetoCalc($costid, $insideType, $write['workStart'], $write['workStop']);
            }
        }
        if (isset($this->calc[$this->dayToProcess][self::WORKTIME])) {
            $this->calc[$this->dayToProcess][self::WORKTIME] += $work;
        } else {
            $this->calc[$this->dayToProcess][self::WORKTIME] = $work;
        }
    }

    private function setOvertime($costid, $work, $workstart, $workstop, $signModifier = null)
    {
        $sundaySignInterval = $this->employeeMainData['sunday_sign_interval'] ?? '';
        if ($this->SHIFT_START_IN_DAYTYPE) {
            $workTimeToShift = new WorkTimeToShift(
                $this->employeeWorkSchedule->getShiftStartType($this->employeeContractId, $this->dayToProcess),
                $sundaySignInterval
            );
        } else {
            $workTimeToShift = new WorkTimeToShift(
                $this->employeesWithMainData->getShiftStartType($this->employeeContractId, $this->dayToProcess),
                $sundaySignInterval
            );
        }
        $sign = isset($signModifier[self::SIGN_MODIFIER_ACCEPTED_OVERTIME]) ? self::SIGN_MODIFIER_ACCEPTED_OVERTIME : '';
        if ($this->STANDBY_SIGN) {
            $intersectionDescriptors = $this->getOvertimeStandByOverlap($workstart, $workstop);
            $this->writeOvertimeToCalcFromIntervalDescriptor(
                $costid ?? '',
                $workTimeToShift,
                $intersectionDescriptors,
                $sign
            );
        } else {
            $workTimeToShift
                ->setPublicHolidays($this->publicHolidaysThisCountry)
                ->setEmployeeMainData($this->employeeMainData);
            $writeArray = $workTimeToShift->getShift($this->dayToProcess, $workstart, $workstop);
            foreach ($writeArray as $write) {
                $insideType = self::SIGN_OVERTIME . $sign . $this->changeCalcTypeDependOnWorkOrder(
                        $write['insideType']
                    );

                $this->setOverTimetoCalc($costid, $insideType, $write['workStart'], $write['workStop'], false);
            }
        }
    }

    private function setDowntime($costid, &$output, &$outputOrder)
    {
        if (!$this->WORK_SCHEDULE_OUT_OF_TIME_CHANGE_IN_HOUR) {
            return;
        }
        $workStart = $this->downtimeWorkstartWorkend[$this->employeeContractId][$this->dayToProcess]['workStart'];
        $workStop = $this->downtimeWorkstartWorkend[$this->employeeContractId][$this->dayToProcess]['workEnd'];
        if (!isset($workStart) or !isset($workStop)) {
            // 'Error - Missing parameter $workStart or $workStop';
            return;
        }

        $sundaySignInterval = $this->employeeMainData['sunday_sign_interval'] ?? '';
        if ($this->SHIFT_START_IN_DAYTYPE) {
            $workTimeToShift = new WorkTimeToShift(
                $this->employeeWorkSchedule->getShiftStartType($this->employeeContractId, $this->dayToProcess),
                $sundaySignInterval
            );
        } else {
            $workTimeToShift = new WorkTimeToShift(
                $this->employeesWithMainData->getShiftStartType($this->employeeContractId, $this->dayToProcess),
                $sundaySignInterval
            );
        }
        $workTimeToShift
            ->setPublicHolidays($this->publicHolidaysThisCountry)
            ->setEmployeeMainData($this->employeeMainData);
        $writeArray = $workTimeToShift->getShift(
            $this->dayToProcess,
            $workStart,
            $workStop
        );

        foreach ($writeArray as $write) {
            $insideType = 'downtime_wt' . $this->changeCalcTypeDependOnWorkOrder($write['insideType']);
            $output[$costid][$insideType]['roundedvalue'] = $write['value'];
            $output[$costid][$insideType]['value'] = $write['value'];

            $tempOutputOrder['state'] = $insideType;
            $tempOutputOrder['costid'] = $costid;

            $outputOrder[] = $tempOutputOrder;
        }
    }

    private function setBackTimeToNewInsideType($costid, &$output, &$outputOrder)
    {
        if (!$this->dayTypeRows[$this->dayToProcess]['daily_working_time_consolidation']) {
            return;
        }

        $insideType = 'unjustified';
        $output[$costid][$insideType]['roundedvalue'] = (-1) * $this->backTimeToNewInsideType;
        $output[$costid][$insideType]['value'] = (-1) * $this->backTimeToNewInsideType;

        $tempOutputOrder['state'] = $insideType;
        $tempOutputOrder['costid'] = $costid;

        $outputOrder[] = $tempOutputOrder;

        if (isset($outputOrder['0']['state']) && $outputOrder['0']['state'] == 'wtej') {
            if ($output[$costid]['wtej']['roundedvalue'] > $this->backTimeToNewInsideType) {
                $output[$costid]['wtej']['roundedvalue'] -= $this->backTimeToNewInsideType;
                $output[$costid]['wtej']['value'] -= $this->backTimeToNewInsideType;
                $output[$costid]['wtde']['roundedvalue'] += $this->backTimeToNewInsideType;
                $output[$costid]['wtde']['value'] += $this->backTimeToNewInsideType;
            } else {
                $output[$costid]['wtde']['roundedvalue'] += $output[$costid]['wtej']['roundedvalue'];
                $output[$costid]['wtde']['value'] += $output[$costid]['wtej']['value'];
                $output[$costid]['wtej']['roundedvalue'] = 0;
                $output[$costid]['wtej']['value'] = 0;
            }
        }

        $this->backTimeToNewInsideType = 0;
    }

    private function setFirstRegModUsedWorkStart($regTimeTs, $usedWorkStart)
    {
        if (!$this->dayTypeRows[$this->dayToProcess]['daily_working_time_consolidation']) {
            return $regTimeTs;
        }
        $this->backTimeToNewInsideType = 0;
        $diff = (int)$regTimeTs - $usedWorkStart;
        if ($diff > $this->dayTypeRows[$this->dayToProcess]['balanceroundtolerance']) {
            $this->backTimeToNewInsideType = (int)$regTimeTs - $usedWorkStart;
        }
        if ($diff > 0) {
            $regTimeTs = $usedWorkStart;
        }
        return $regTimeTs;
    }

    private function setInsideTypeValuesMerged($costid, &$output, &$outputOrder)
    {
        if (!$this->dayTypeRows[$this->dayToProcess]['daily_working_time_consolidation']) {
            return;
        }
        $mergeInsideTypeName = 'wtdailyworktime';
        $mergeIndideTypeNameValues = ['wtde', 'wtdu1', 'wtdu2'];

        $mergeInsideType = [$mergeInsideTypeName => $mergeIndideTypeNameValues];
        $outputTemp = [];

        foreach ($output as $costId => $insideType) {
            foreach ($insideType as $insideTypeName => $insideTypeValue) {
                if (in_array($insideTypeName, $mergeInsideType[$mergeInsideTypeName])) {
                    $outputTemp[$costId][$mergeInsideTypeName]['value'] += $insideTypeValue['value'];
                    $outputTemp[$costId][$mergeInsideTypeName]['roundedvalue'] += $insideTypeValue['roundedvalue'];
                } else {
                    $outputTemp[$costId][$insideTypeName]['value'] = $output[$costId][$insideTypeName]['value'];
                    $outputTemp[$costId][$insideTypeName]['roundedvalue'] = $output[$costId][$insideTypeName]['roundedvalue'];
                }
            }
        }

        $outputOrderTemp = [];
        foreach ($outputOrder as $key => $values) {
            if (!in_array($values['state'], $mergeInsideType[$mergeInsideTypeName])
                || $values['state'] == $mergeInsideTypeName
            ) {
                $outputOrderTemp[$key] = $values;
            }
        }
        $output = $outputTemp;
        $outputOrder = $outputOrderTemp;
    }

    private function setInsideTypeValueRound($costid, &$output, &$outputOrder)
    {
        if (!$this->dayTypeRows[$this->dayToProcess]['daily_working_time_consolidation']) {
            return;
        }
        $balanceRoundTolerance = $this->dayTypeRows[$this->dayToProcess]['balanceroundtolerance'];
        $output[$costid]['wtej']['roundedvalue'] =
            $this->customizedRoundDown(
                $output[$costid]['wtej']['roundedvalue'] + $balanceRoundTolerance,
                1800
            );
        $output[$costid]['wtdailyworktime']['roundedvalue'] =
            $this->customizedRoundUp(
                $output[$costid]['wtdailyworktime']['roundedvalue'] - $balanceRoundTolerance,
                1800
            );
    }

    private function collectDailyGoneHomeState($employeestate)
    {
        if (1
        ) {
            $workstart = $this->stateType->getCalculationStart($employeestate);
            $workstop = $this->regRow['reg_time_ts'];
            $outSide = $workstop - $workstart;
            if ($outSide === 0) {
                return;
            }
            $this->calc[$this->dayToProcess][$this->GONEHOME_STATE_TYPE_ID] += $outSide;
            $sundaySignInterval = $this->employeeMainData['sunday_sign_interval'] ?? '';
            $workTimeToShift = new WorkTimeToShift(
                $this->employeeWorkSchedule->getShiftStartType($this->employeeContractId, $this->dayToProcess),
                $sundaySignInterval
            );
            $workTimeToShift
                ->setPublicHolidays($this->publicHolidaysThisCountry)
                ->setEmployeeMainData($this->employeeMainData);
            $shift = $workTimeToShift->getShift(
                $this->dayToProcess,
                $workstart,
                $workstop
            );
            foreach ($shift as $id => $sh) {
                $this->InitAndAddValueToVariable(
                    $this->calc[$this->dayToProcess]['notInside'][$sh['insideType']],
                    $sh['value']
                );
            }
            $this->stateType->setCalculationStart($employeestate, 0);
        }
    }

    private function getInsideType($costid, $workstart, $workstop)
    {
        $ret = [];
        if ($workstart <= $this->DE_START) {
            if ($workstop <= $this->DE_START) {
                if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift'] > 0) {
                    $ret[] = ['insideType' => 'ej', 'workStart' => $workstart, 'workStop' => $workstop];
                } else {
                    $ret[] = ['insideType' => 'de', 'workStart' => $workstart, 'workStop' => $workstop];
                }
            } else {
                if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift'] > 0) {
                    $ret[] = ['insideType' => 'ej', 'workStart' => $workstart, 'workStop' => $this->DE_START];
                }
                if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                    if ($workstop <= $this->DU_START1) {
                        $ret[] = ['insideType' => 'de', 'workStart' => $this->DE_START, 'workStop' => $workstop];
                    } else {
                        if ($workstop <= $this->DU_START2) {
                            $ret[] = [
                                'insideType' => 'de',
                                'workStart' => $this->DE_START,
                                'workStop' => $this->DU_START1
                            ];
                            $ret[] = [
                                'insideType' => 'du1',
                                'workStart' => $this->DU_START1,
                                'workStop' => $workstop
                            ];
                        } else {
                            if ($workstop <= $this->EJ_START) {
                                $ret[] = [
                                    'insideType' => 'de',
                                    'workStart' => $this->DE_START,
                                    'workStop' => $this->DU_START1
                                ];
                                $ret[] = [
                                    'insideType' => 'du1',
                                    'workStart' => $this->DU_START1,
                                    'workStop' => $this->DU_START2
                                ];
                                $ret[] = [
                                    'insideType' => 'du2',
                                    'workStart' => $this->DU_START2,
                                    'workStop' => $workstop
                                ];
                            } else {
                                $ret[] = [
                                    'insideType' => 'de',
                                    'workStart' => $this->DE_START,
                                    'workStop' => $this->DU_START1
                                ];
                                $ret[] = [
                                    'insideType' => 'du1',
                                    'workStart' => $this->DU_START1,
                                    'workStop' => $this->DU_START2
                                ];
                                $ret[] = [
                                    'insideType' => 'du2',
                                    'workStart' => $this->DU_START2,
                                    'workStop' => $this->EJ_START
                                ];
                                $ret[] = [
                                    'insideType' => 'ej',
                                    'workStart' => $this->EJ_START,
                                    'workStop' => $workstop
                                ];
                            }
                        }
                    }
                } else {
                    $ret[] = ['insideType' => 'de', 'workStart' => $workstart, 'workStop' => $workstop];
                }
            }
        } elseif ($workstart <= $this->DU_START1) {
            if ($workstop <= $this->DU_START1) {
                if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift'] > 0) {
                    $ret[] = ['insideType' => 'de', 'workStart' => $workstart, 'workStop' => $workstop];
                } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                    $ret[] = ['insideType' => 'du1', 'workStart' => $workstart, 'workStop' => $workstop];
                }
            } else {
                $tmp = (int)date('H', $workstart);
                if ($tmp > 12 and $tmp < 16 &&
                    ($this->dayTypeRows[$this->dayToProcess]['allowance'] == 1
                        || $this->dayTypeRows[$this->dayToProcess]['allowance'] == 3
                    )
                ) {
                } else {
                    if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift'] > 0) {
                        $ret[] = ['insideType' => 'de', 'workStart' => $workstart, 'workStop' => $this->DU_START1];
                    } else {
                        $ret[] = ['insideType' => 'du1', 'workStart' => $workstart, 'workStop' => $this->DU_START1];
                    }
                }
                if ($workstop <= $this->DU_START2) {
                    if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                        $ret[] = ['insideType' => 'du1', 'workStart' => $this->DU_START1, 'workStop' => $workstop];
                    } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift'] > 0) {
                        $ret[] = ['insideType' => 'de', 'workStart' => $this->DU_START1, 'workStop' => $workstop];
                    }
                } else {
                    $ret[] = [
                        'insideType' => 'du1',
                        'workStart' => $this->DU_START1,
                        'workStop' => $this->DU_START2
                    ];
                    $ret[] = ['insideType' => 'du2', 'workStart' => $this->DU_START2, 'workStop' => $workstop];
                }
            }
        } elseif ($workstart <= $this->DU_START2) {
            if ($workstop <= $this->DU_START2) {
                if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                    $ret[] = ['insideType' => 'du1', 'workStart' => $workstart, 'workStop' => $workstop];
                } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift'] > 0) {
                    $ret[] = ['insideType' => 'ej', 'workStart' => $workstart, 'workStop' => $workstop];
                } else {
                    $ret[] = ['insideType' => 'de', 'workStart' => $workstart, 'workStop' => $workstop];
                }
            } else {
                if ($workstop <= $this->EJ_START) {
                    $ret[] = ['insideType' => 'du1', 'workStart' => $workstart, 'workStop' => $this->DU_START2];
                    $ret[] = ['insideType' => 'du2', 'workStart' => $this->DU_START2, 'workStop' => $workstop];
                } else {
                    if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift'] > 0) {
                        $ret[] = ['insideType' => 'du1', 'workStart' => $workstart, 'workStop' => $this->DU_START2];
                        $ret[] = [
                            'insideType' => 'du2',
                            'workStart' => $this->DU_START2,
                            'workStop' => $this->EJ_START
                        ];
                        $ret[] = ['insideType' => 'ej', 'workStart' => $this->EJ_START, 'workStop' => $workstop];
                    } else {
                        $ret[] = ['insideType' => 'du1', 'workStart' => $workstart, 'workStop' => $this->DU_START2];
                        $ret[] = ['insideType' => 'du2', 'workStart' => $this->DU_START2, 'workStop' => $workstop];
                    }
                }
            }
        } elseif ($workstart <= $this->EJ_START) {
            if ($workstop <= $this->EJ_START) {
                if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                    $ret[] = ['insideType' => 'du2', 'workStart' => $workstart, 'workStop' => $workstop];
                } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift'] > 0) {
                    $ret[] = ['insideType' => 'ej', 'workStart' => $workstart, 'workStop' => $workstop];
                } else {
                    $ret[] = ['insideType' => 'de', 'workStart' => $workstart, 'workStop' => $workstop];
                }
            } else {
                if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift'] > 0) {
                    if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                        $ret[] = ['insideType' => 'du2', 'workStart' => $workstart, 'workStop' => $this->EJ_START];
                        $ret[] = ['insideType' => 'ej', 'workStart' => $this->EJ_START, 'workStop' => $workstop];
                    } else {
                        $ret[] = ['insideType' => 'ej', 'workStart' => $workstart, 'workStop' => $workstop];
                    }
                } else {
                    $ret[] = ['insideType' => 'du2', 'workStart' => $workstart, 'workStop' => $workstop];
                }
            }
        } elseif ($workstart <= $this->EJ_STOP) {
            if ($workstop <= $this->EJ_STOP) {
                if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_night_shift'] > 0) {
                    $ret[] = ['insideType' => 'ej', 'workStart' => $workstart, 'workStop' => $workstop];
                } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_evening_shift'] > 0) {
                    $ret[] = ['insideType' => 'du2', 'workStart' => $workstart, 'workStop' => $workstop];
                } elseif ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift'] > 0) {
                    $ret[] = ['insideType' => 'de', 'workStart' => $workstart, 'workStop' => $workstop];
                }
            } else {
                if ($this->dayTypeRows[$this->dayToProcess]['used_work_time_day_shift'] > 0) {
                    $ret[] = ['insideType' => 'ej', 'workStart' => $workstart, 'workStop' => $this->EJ_STOP];
                    $ret[] = ['insideType' => 'de', 'workStart' => $this->EJ_STOP, 'workStop' => $workstop];
                } else {
                    $ret[] = ['insideType' => 'ej', 'workStart' => $workstart, 'workStop' => $workstop];
                }
            }
        }
        return $ret;
    }

    private function setBackLostTimeToWorktime($outworktime, &$output, $diff)
    {
        $defCostId = $this->getDefaultCostId();
        if (is_array($defCostId)) {
            $def_array = implode(';', $defCostId);
            $defCostId = $def_array[0];
        }
        $shifts = $this->dayTypeRows[$this->dayToProcess]['shifts_with_prefix'];
        foreach ($shifts as $shift => $shiftValue) {
            $wtKey = self::SIGN_WORKTIME . $shift;
            if ($outworktime[$wtKey] < $shiftValue) {
                $lt = MIN($shiftValue - $outworktime[$wtKey], $diff);
                $diff -= $lt;
                $this->InitAndAddValueToVariable($output[$defCostId][$wtKey]['roundedvalue'], $lt);
                if ($diff == 0) {
                    break;
                }
            }
        }
    }

    private function getBreakTime(int $workStart, int $workStop, bool $sub): int
    {
        if (!isset($this->dayTypeRows[$this->dayToProcess]['breaks'])) {
            return 0;
        }
        $work = $workStop - $workStart;
        $breakTimeSum = 0;
        foreach ($this->dayTypeRows[$this->dayToProcess]['breaks'] as &$break) {
            if (empty($break['break_time']) || $break['break_time'] <= 0) {
                continue;
            }
            $this->initVariable($break['break_time_from']);

            $breakTime = 0;
            $breakTimeTo = ($break['break_time_from'] + $break['break_time']);

            if (($workStart < $breakTimeTo) && ($break['break_time_from'] < $workStop)) //Metszi az szünet a munkaidőt
            {
                $from = max($workStart, $break['break_time_from']);
                $to = min($workStop, $breakTimeTo);
                $breakTime = max(0, ($to - $from));
            }

            //Nem metszi de még maradt szünet amit nem vontunk még le
            if (!$this->DEDUCTION_OF_BREAKS_OUTSIDE_WORKING_HOURS &&
                $break['break_time_from'] <= $workStart && $breakTimeTo <= $workStop) {
                $breakTime += (($work >= $break['break_time']) ? $break['break_time'] : $work) - $breakTime;
            }

            if ($breakTime === 0) {
                continue;
            }

            if ($sub) {
                $break['break_time_from'] += $breakTime;
                $break['break_time'] -= $breakTime;
                $work -= $breakTime;
                $this->initVariable($this->calc[$this->dayToProcess]['lunchtime']['subtract_from_worktime']);
                $this->calc[$this->dayToProcess]['lunchtime']['subtract_from_worktime'] += $breakTime;
            }
            $breakTimeSum += $breakTime;
        }
        return $breakTimeSum;
    }

    private function backLunchTime()
    {
        $this->backRealLunchTime();
        //$this->backOutsideLunchTime();
    }

    private function backBreakTimeAfterWorktime()
    {
        $calc = $this->calc[$this->dayToProcess];
        $calc['lunchtime']['after'] = $calc['lunchtime']['after'] ?? null;
        if (!is_array($calc['lunchtime']['after']) or empty($calc['lunchtime']['after'])) {
            return;
        }
        $dt = $this->dayTypeRows[$this->dayToProcess];
        $costArray = array_keys($calc['costcalc'][0]);
        $thereIsAPreWorktimeOvertime = count(preg_grep('/^ot/', array_keys($calc['costcalc'][0][$costArray[0]]))) > 0;
        foreach ($this->calc[$this->dayToProcess]['lunchtime']['after'] as $v) {
            $stillBreakTime = isset($dt['break_time_after_worktime_duration']) && $dt['break_time_after_worktime_duration'] > 0;
            $entryAfterWorktime = $this->RETURN_A_BREAK_AFTER_WORKTIME_ONLY_FROM_BEING_OUT_AFTER_THE_END_OF_WORKTIME ?
                $dt['used_work_end'] <= $v['workstart'] :
                true;
            $wtIsBiggerThenBTAW = $dt['towork'] >= $dt['break_time_after_worktime'];
            $isFlexible = $this->employeeMainData['work_type'] === 'FLEXIBLE';

            if ($stillBreakTime
                && ($entryAfterWorktime /** munkaido utani ment ki */
                    || $wtIsBiggerThenBTAW /** nem tudom hol fordulha elo.... Tesztek utan kiveheto */
                    || $thereIsAPreWorktimeOvertime /** munkaido elotti tuloraja van */
                    || $isFlexible/** mukodik igy, de valami altalanosabb megoldas kellene ra, hogy mikor adja vissza a 9 ora utan szunet kintletit */
                )

            ) {
                $back = min(
                    $dt['break_time_after_worktime_duration'],
                    $v['outside']
                );
                $dt['break_time_after_worktime_duration'] -= $back;
                $v['workstop'] = $v['workstart'] + $back;
                $this->setOutSideToworkTime($v['costid'], $back, $v['workstart'], $v['workstop']);
            }
        }
    }

    /**
     *
     * Ebédidő visszaadása, akkor amikor a kint tartózkodásai az ebéd időt képezik
     *
     */
    private function backOutsideLunchTime()
    {
        if (isset($this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'])
            && ($this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'] > 0)
        ) {
            $back = min(
                $this->dayTypeRows[$this->dayToProcess]['lunchtime'],
                $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val']
            );
            $this->writeLunchBackValueToCalculatedCostCalc($back);
        }
    }

    /**
     *
     * Ebédidő visszaadása, akkor amikor a kivett ebédszünete (["lunchtime"]["real_lunch_time"]) és a
     * munkaidőből levont munkaidejének (["lunchtime"]["subtract_from_worktime"]) összege több mint a
     * munkarendje szerinti ebédidő ($this->dayTypeRows[$this->dayToProcess]['lunchtime']).
     *
     */
    private function backRealLunchTime()
    {
        if ($this->dayTypeRows[$this->dayToProcess]['lunchtime'] > 0
            && $this->dayTypeRows[$this->dayToProcess]['worktype'] !== 'INFORMAL'
            && isset($this->calc[$this->dayToProcess]['lunchtime']['subtract_from_worktime'])
            && isset($this->calc[$this->dayToProcess]['lunchtime']['real_lunch_time'])
            && ($this->calc[$this->dayToProcess]['lunchtime']['subtract_from_worktime'] +
                $this->calc[$this->dayToProcess]['lunchtime']['real_lunch_time']) > $this->dayTypeRows[$this->dayToProcess]['lunchtime']
        ) {
            $back = min(
                (
                    $this->calc[$this->dayToProcess]['lunchtime']['subtract_from_worktime'] +
                    $this->calc[$this->dayToProcess]['lunchtime']['real_lunch_time'] - $this->dayTypeRows[$this->dayToProcess]['lunchtime']
                ),
                $this->calc[$this->dayToProcess]['lunchtime']['subtract_from_worktime']
            );
            $this->writeLunchBackValueToCalculatedCostCalc($back);
        }
    }

    /**
     *
     * Ebéd idő visszaadása a kiszámolt tömbbe
     *
     */
    private function writeLunchBackValueToCalculatedCostCalc($back)
    {
        $sum = 0;
        if (isset($this->break_notes) and is_array($this->break_notes)) {
            foreach ($this->break_notes as $claimid => $values_cost) {
                foreach ($values_cost as $costid => $values_type) {
                    foreach ($values_type as $type => $value) {
                        if ($back > $sum) {
                            $tmp = (($sum + $value) > $back) ? ($back - $sum) : $value;
                            $sum += $tmp;
                            $this->calc[$this->dayToProcess]['costcalc'][$claimid][$costid][$type] += $tmp;
                        }
                    }
                }
            }
        }
    }

    /**
     *
     * A rövid bentlét szakaszok kiszűrése. Rövid értéke: $this->MIN_SECONDS_IN_INSIDE_TYPE
     * A rövid szakaszt a következő szakaszba teszi. Ha az utolsó szakasz a rövid szakasz, akkor azt az előzőbe.
     *
     * @return
     */
    private function MovingShortInsideType(): void
    {
        if (!isset($this->calc[$this->dayToProcess]['costcalc'])
            or !isset($this->calc[$this->dayToProcess]['claimid'])
            or $this->calc[$this->dayToProcess]['claimid'] < 1) {
            return;
        }
        /* claimId	- hanyadik szakasz
	 * costíid	- szakasz költséghely, költségviselője beállítás szerint
	 * typeId	- szakasz insideTypeId-ja
	 */
        $preCostId = $preinsideTypeId = 'STARTVALUE';
        $preClaimId = $preValue = 0;
        foreach ($this->calc[$this->dayToProcess]['costcalc'] as $claimId => $costCalc) {
            foreach ($costCalc as $costId => $typeCalc) {
                foreach ($typeCalc as $insideTypeId => $valueCalc) {
                    // Ha előző szakasz rövidebb volt, akkor beteszi az aktuális szakaszba és törli az előzőt
                    if ($preValue > 0) {
                        $this->calc[$this->dayToProcess]['costcalc'][$claimId][$costId][$insideTypeId] += $preValue;
                        $valueCalc += $preValue;
                        unset($this->calc[$this->dayToProcess]['costcalc'][$preClaimId][$preCostId][$preinsideTypeId]);
                        $preValue = 0;
                    }
                    // ha az aktuális szakasz rövidebb, és nincs költséghely váltás, vagy még nincs előző költséghely
                    if ($valueCalc < $this->MIN_SECONDS_IN_INSIDE_TYPE
                        and ($preCostId === $costId or $preCostId === 'STARTVALUE')) {
                        $preValue = $valueCalc;
                    }
                    /* Ha	utolsó szakasz
				*	és	az aktuális szakasz rövidebb
				*	és	létezik az előző sor
				* Akkor az előzőhöz hozzáadja és törli az aktuálisat
				*/
                    if ($this->calc[$this->dayToProcess]['claimid'] === ($claimId + 1)
                        and $preValue > 0
                        and isset($this->calc[$this->dayToProcess]['costcalc'][$preClaimId][$preCostId][$preinsideTypeId])
                    ) {
                        $this->calc[$this->dayToProcess]['costcalc'][$preClaimId][$preCostId][$preinsideTypeId] += $preValue;
                        unset($this->calc[$this->dayToProcess]['costcalc'][$claimId][$costId][$insideTypeId]);
                    }
                    $preClaimId = $claimId;
                    $preCostId = $costId;
                    $preinsideTypeId = $insideTypeId;
                }
            }
        }
    }

    private function NormalizeCalculatedClaim()
    {
        if (isset($this->calc[$this->dayToProcess]['claimid']) and $this->calc[$this->dayToProcess]['claimid'] > 0) {
            for ($claimid = 0; $claimid < $this->calc[$this->dayToProcess]['claimid']; $claimid++) {
                if (array_key_exists($claimid, $this->calc[$this->dayToProcess]['costcalc'])) {
                    $tmp = array_keys($this->calc[$this->dayToProcess]['costcalc'][$claimid]);
                    $costid = $tmp[0];
                    if (empty($this->calc[$this->dayToProcess]['costcalc'][$claimid][$costid])) {
                        unset($this->calc[$this->dayToProcess]['costcalc'][$claimid][$costid]);
                    } else {
                        $tmp = array_keys($this->calc[$this->dayToProcess]['costcalc'][$claimid][$costid]);
                        $type = $tmp[0];
                        for ($claimid1 = ($claimid + 1); $claimid1 < $this->calc[$this->dayToProcess]['claimid']; $claimid1++) {
                            if (isset($this->calc[$this->dayToProcess]['costcalc'][$claimid1])
                                and array_key_exists(
                                    $costid,
                                    $this->calc[$this->dayToProcess]['costcalc'][$claimid1]
                                )) {
                                if (isset($this->calc[$this->dayToProcess]['costcalc'][$claimid1][$costid])
                                    and array_key_exists(
                                        $type,
                                        $this->calc[$this->dayToProcess]['costcalc'][$claimid1][$costid]
                                    )) {
                                    if (!isset($this->calc[$this->dayToProcess]['costcalc'][$claimid][$costid][$type])) {
                                        $this->calc[$this->dayToProcess]['costcalc'][$claimid][$costid][$type] = 0;
                                    }
                                    $this->calc[$this->dayToProcess]['costcalc'][$claimid][$costid][$type] += $this->calc[$this->dayToProcess]['costcalc'][$claimid1][$costid][$type];
                                    unset($this->calc[$this->dayToProcess]['costcalc'][$claimid1]);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private function BackWorktimeInPost()
    {
        if ($this->RULE_WORKTIME_IN_POST) {
            //beosztástól eltérő munkaidő keresése és kigyűjtése az $insideTypeMove -ba
            {
                $insideTypeMove = [];
                if (isset($this->calc[$this->dayToProcess]['costcalc'])) {
                    foreach ($this->calc[$this->dayToProcess]['costcalc'] as $claimId => $costCalc) {
                        foreach ($costCalc as $costId => $typeCalc) {
                            foreach ($typeCalc as $insideTypeId => $valueCalc) {
                                if (substr($insideTypeId, 0, 2) == self::SIGN_WORKTIME
                                    and $this->dayTypeRows[$this->dayToProcess]['diff_between_daytype_and_calc'][$insideTypeId] == 0
                                    and $valueCalc != 0
                                    and $valueCalc < 300) {
                                    $insideTypeMove[$costId]['value'] = $valueCalc;
                                    $insideTypeMove[$costId]['insideType'] = $insideTypeId;
                                    $insideTypeMove[$costId]['claimId'] = $claimId;
                                }
                            }
                        }
                    }
                }
            }

            //Ha van beosztástól eltérő munkaidő akkor annak a beosztási időra való visszaadása ha van hely
            {
                if (count($insideTypeMove) > 0) {
                    foreach ($this->calc[$this->dayToProcess]['costcalc'] as $claimId => $costCalc) {
                        foreach ($costCalc as $costId => $typeCalc) {
                            foreach ($typeCalc as $insideTypeId => $valueCalc) {
                                if (substr($insideTypeId, 0, 2) == self::SIGN_WORKTIME
                                    and $this->dayTypeRows[$this->dayToProcess]['diff_between_daytype_and_calc'][$insideTypeId] != 0
                                    and $this->dayTypeRows[$this->dayToProcess]['diff_between_daytype_and_calc'][$insideTypeId] > $valueCalc) {
                                    $insideTypeIdDelete = $insideTypeMove[$costId]['insideType'];
                                    $claimIdDelete = $insideTypeMove[$costId]['claimId'];

                                    $maxValue = $this->dayTypeRows[$this->dayToProcess]['diff_between_daytype_and_calc'][$insideTypeId] - $valueCalc;
                                    $valueTmp = min($maxValue, $insideTypeMove[$costId]['value']);
                                    $this->calc[$this->dayToProcess]['costcalc'][$claimId][$costId][$insideTypeId] += $valueTmp;
                                    $this->calc[$this->dayToProcess]['costcalc'][$claimIdDelete][$costId][$insideTypeIdDelete] -= $valueTmp;
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    private function break_time_after_worktime()
    {
        $i = 0;
        $paid_inside = 0;
        $claimPreIds = [];
        $paid = $this->dayTypeRows[$this->dayToProcess]['paid_break_time_after_worktime'];
        if (isset($this->calc[$this->dayToProcess]['claimid']) and $this->calc[$this->dayToProcess]['claimid'] > 0) {
            foreach ($this->calc[$this->dayToProcess]['costcalc'] as $claimid => $costCalc) {
                foreach ($costCalc as $costid => $state) {
                    foreach ($state as $stateName => $value) {
                        $claimPreIds[$i]['claimid'] = $claimid;
                        $claimPreIds[$i]['costid'] = $costid;
                        $claimPreIds[$i]['stateName'] = $stateName;
                        if (!is_array($value)) {
                            $stateIsWorktime = $this->stateType->getStateType(
                                $this->dayToProcess,
                                $stateName,
                                GStateType::WORKTIME
                            ) === '0' ? 0 : 1;
                            $paid_inside += $stateIsWorktime ? $value : 0;
                            if ($paid_inside >= $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime']) {
                                if (!$paid) {
                                    $this->break_time_after_worktime_sub(
                                        $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime_duration'],
                                        $claimPreIds,
                                        $i
                                    );
                                }
                                $this->backBreakTimeAfterWorktime();
                                return;
                            }
                        }
                        $i++;
                    }
                }
            }
        }
    }

    private function break_time_after_worktime_sub($sub_value, $claimPreIds, $i)
    {
        $claimid = $claimPreIds[$i]['claimid'];
        $costid = $claimPreIds[$i]['costid'];
        $stateName = $claimPreIds[$i]['stateName'];
        if ($sub_value <= $this->calc[$this->dayToProcess]['costcalc'][$claimid][$costid][$stateName]) {
            $this->calc[$this->dayToProcess]['costcalc'][$claimid][$costid][$stateName] -= $sub_value;
        } else {
            $remainedValue = $sub_value - $this->calc[$this->dayToProcess]['costcalc'][$claimid][$costid][$stateName];
            $this->calc[$this->dayToProcess]['costcalc'][$claimid][$costid][$stateName] = 0;
            $this->break_time_after_worktime_sub($remainedValue, $claimPreIds, ($i - 1));
        }
    }

    private function DailyBreakTimeSub()
    {
        if ($this->RULE_DAILY_BREAK_TIME === true
            and isset($this->calc[$this->dayToProcess]['costcalc'])) {
            $breakTimeLeave = (isset($this->calc[$this->dayToProcess]['83b2789f74438d800a261f5ccb21b2fb'])) ?
                $this->calc[$this->dayToProcess]['83b2789f74438d800a261f5ccb21b2fb'] : 0;
            $fullBreakTime = (isset($this->dayTypeRows[$this->dayToProcess]['lunchtime'])) ?
                $this->dayTypeRows[$this->dayToProcess]['lunchtime'] : 0;
            $goneHomeTime = ($this->RULE_DAILY_BREAK_TIME_WITH_GONE_HOME === true
                and isset($this->calc[$this->dayToProcess]['GONEHOME'])) ?
                $this->calc[$this->dayToProcess]['GONEHOME'] : 0;
            $breakTimeSub = $fullBreakTime - $breakTimeLeave - $goneHomeTime;

            if ($breakTimeSub > 0) // kevesebbet volt távol mint az ebéd ideje
            {
                $costcalc = ($this->RULE_DAILY_BREAK_TIME_SUB_BACKWARDS === true ?
                    array_reverse($this->calc[$this->dayToProcess]['costcalc'], true) :
                    $this->calc[$this->dayToProcess]['costcalc']);

                foreach ($costcalc as $claimid => $costRow) {
                    foreach ($costRow as $costid => $insideTypeRow) {
                        foreach ($insideTypeRow as $insideType => $value) {
                            if ($value > $breakTimeSub) {
                                $this->calc[$this->dayToProcess]['costcalc'][$claimid][$costid][$insideType] -= $breakTimeSub;
                                $breakTimeSub = 0;
                            } else {
                                $breakTimeSub -= $value;
                                unset($this->calc[$this->dayToProcess]['costcalc'][$claimid][$costid][$insideType]);
                            }
                            if ($breakTimeSub === 0) {
                                return;
                            }
                        }
                    }
                }
            }
            if ($goneHomeTime > 0) {
                $this->setGoneHometoCalc($this->getDefaultCostId(), 'GONEHOME', $goneHomeTime);
            }
        }
    }

    private function setGoneHometoCalc($costid, $type, $value = 0)
    {
        $this->calc[$this->dayToProcess]['costcalc'][$this->calc[$this->dayToProcess]['claimid']][$costid][$type] = $value;
        $this->calc[$this->dayToProcess]['prewrite']['costid'] = $costid;
        $this->calc[$this->dayToProcess]['prewrite']['type'] = $type;
        $this->calc[$this->dayToProcess]['prewrite']['claimid'] = $this->calc[$this->dayToProcess]['claimid'];
        $this->calc[$this->dayToProcess]['claimid']++;
    }

    private function setWorkTimetoCalc($costid, $type_in, $workstart, $workstop, $value = 0)
    {
        if (!isset($this->calc[$this->dayToProcess]['claimid'])) {
            $this->calc[$this->dayToProcess]['claimid'] = 0;
        }
        $type = $this->getCompensatoryDayAndWeekendOvertimeToType($type_in);

        if (!isset($this->calc[$this->dayToProcess]['costcalc'][$this->calc[$this->dayToProcess]['claimid']][$costid][$type])) {
            $this->calc[$this->dayToProcess]['costcalc'][$this->calc[$this->dayToProcess]['claimid']][$costid][$type] = 0;
        }
        if ($workstart > 0 and $workstop > $workstart and $value === 0) {
            $sub = true;    //mindig csökkenteni kell a breaktime értékét ha már levontuk
            $tmp = ($this->RULE_DAILY_BREAK_TIME === false) ? $this->getBreakTime($workstart, $workstop, $sub) : 0;
            $work = $workstop - $workstart;
            $tmp = min($work, $tmp);    //elvileg nem lehetne, mert a getBreakTime() fgv megoldja, de....
            $val = $work - $tmp;

            if ($tmp > 0) {
                if (!isset($this->break_notes[$this->calc[$this->dayToProcess]['claimid']][$costid][$type])) {
                    $this->break_notes[$this->calc[$this->dayToProcess]['claimid']][$costid][$type] = 0;
                }
                $this->break_notes[$this->calc[$this->dayToProcess]['claimid']][$costid][$type] += $tmp;
            }
        } else {
            $val = $value;
        }
        $this->calc[$this->dayToProcess]['costcalc'][$this->calc[$this->dayToProcess]['claimid']][$costid][$type] += $val;
        $this->calc[$this->dayToProcess]['prewrite']['costid'] = $costid;
        $this->calc[$this->dayToProcess]['prewrite']['type'] = $type;
        $this->calc[$this->dayToProcess]['prewrite']['claimid'] = $this->calc[$this->dayToProcess]['claimid'];
        $this->calc[$this->dayToProcess]['claimid']++;
    }

    private function getCompensatoryDayAndWeekendOvertimeToType($type)
    {
        if (substr($type, 0, 2) == self::SIGN_WORKTIME ||
            substr($type, 0, 2) == self::SIGN_OVERTIME
        ) {
            if ($this->workScheduleIsCompensatoryDay()) {
                return substr_replace($type, self::SIGN_OVERTIME, 0, 2);
            }
        }
        return $type;
    }

    private function setWeekendToType($insideType)
    {
        $typeFirstTwoCharacter = substr($insideType, 0, 2);
        if (($typeFirstTwoCharacter === self::SIGN_WORKTIME || $typeFirstTwoCharacter === self::SIGN_OVERTIME)
            && ($this->workScheduleIsRestDay())) {
            if ($this->containsStandByInInsideType($insideType)) {
                return substr_replace($insideType, 'w', 4, 0);
            } else {
                return substr_replace($insideType, 'w', 2, 0);
            }
        }
        return $insideType;
    }

    private function setClaimtoCalc($costid, $type_in, $value)
    {
        if (!isset($this->calc[$this->dayToProcess]['claimid'])) {
            $this->calc[$this->dayToProcess]['claimid'] = 0;
        }

        $type = $this->setWeekendToType($type_in);

        if (!isset($this->calc[$this->dayToProcess]['costcalc'][$this->calc[$this->dayToProcess]['claimid']][$costid][$type])) {
            $this->calc[$this->dayToProcess]['costcalc'][$this->calc[$this->dayToProcess]['claimid']][$costid][$type] = 0;
        }
        $this->calc[$this->dayToProcess]['costcalc'][$this->calc[$this->dayToProcess]['claimid']][$costid][$type] += $value;
        $this->calc[$this->dayToProcess]['prewrite']['costid'] = $costid;
        $this->calc[$this->dayToProcess]['prewrite']['type'] = $type;
        $this->calc[$this->dayToProcess]['prewrite']['claimid'] = $this->calc[$this->dayToProcess]['claimid'];
        $this->calc[$this->dayToProcess]['claimid']++;
    }

    /**
     * @param string $costId
     * @param WorkTimeToShift $workTimeToShift
     * @param IntervalIntersectionDescriptor[] $intersectionDescriptors
     * @return void
     * @throws Exception
     */
    private function writeWorktimeToCalcFromIntervalDescriptor(
        string $costId,
        WorkTimeToShift $workTimeToShift,
        array $intersectionDescriptors
    ) {
        $workTimeToShift
            ->setPublicHolidays($this->publicHolidaysThisCountry)
            ->setEmployeeMainData($this->employeeMainData);
        /** @var IntervalIntersectionDescriptor $intersectionDescriptor */
        foreach ($intersectionDescriptors as $intersectionDescriptor) {
            foreach ($intersectionDescriptor->getIntersectionAndAfterFirstIntervals() as $interval) {
                $writeArray = $workTimeToShift->getShift(
                    $this->dayToProcess,
                    $interval->getFrom()->getTimestamp(),
                    $interval->getTo()->getTimestamp()
                );
                foreach ($writeArray as $write) {
                    $hasSign = $interval->hasSign();
                    $insideType = self::SIGN_WORKTIME .
                        ($interval->hasSign() ? $interval->getSign() : '') .
                        $this->changeCalcTypeDependOnWorkOrder($write['insideType']);

                    $this->setWorkTimetoCalc(
                        $costId,
                        $insideType,
                        $write['workStart'],
                        $write['workStop']
                    );
                }
            }
        }
    }

    /**
     * @param string $costId
     * @param WorkTimeToShift $workTimeToShift
     * @param IntervalIntersectionDescriptor[] $intersectionDescriptors
     * @return void
     * @throws Exception
     */
    private function writeOvertimeToCalcFromIntervalDescriptor(
        string $costId,
        WorkTimeToShift $workTimeToShift,
        array $intersectionDescriptors,
        string $acceptedOverTime = ''
    ) {
        $workTimeToShift
            ->setPublicHolidays($this->publicHolidaysThisCountry)
            ->setEmployeeMainData($this->employeeMainData);
        /** @var IntervalIntersectionDescriptor $intersectionDescriptor */
        foreach ($intersectionDescriptors as $intersectionDescriptor) {
            foreach ($intersectionDescriptor->getIntersectionAndAfterFirstIntervals() as $interval) {
                $writeArray = $workTimeToShift->getShift(
                    $this->dayToProcess,
                    $interval->getFrom()->getTimestamp(),
                    $interval->getTo()->getTimestamp()
                );
                foreach ($writeArray as $write) {
                    $insideType = ($interval->hasSign() ? $interval->getSign() : '') === self::SIGN_OVERTIME ?
                        self::SIGN_OVERTIME :
                        self::SIGN_OVERTIME . ($interval->hasSign() ? $interval->getSign() : '');
                    $insideType .= $acceptedOverTime .
                        $this->changeCalcTypeDependOnWorkOrder($write['insideType']);

                    $this->setOverTimetoCalc(
                        $costId,
                        $insideType,
                        $write['workStart'],
                        $write['workStop'],
                        false
                    );
                }
            }
        }
    }

    private function setOverTimetoCalc($costid, $type_in, $workstart, $workstop, $negative)
    {
        if (!isset($this->calc[$this->dayToProcess]['claimid'])) {
            $this->calc[$this->dayToProcess]['claimid'] = 0;
        }

        $type = $this->setWeekendToType($type_in);

        if (!isset($this->calc[$this->dayToProcess]['costcalc'][$this->calc[$this->dayToProcess]['claimid']][$costid][$type])) {
            $this->calc[$this->dayToProcess]['costcalc'][$this->calc[$this->dayToProcess]['claimid']][$costid][$type] = 0;
        }
        $sub = !$negative;
        // nem kell csökkenteni a breaktime érékét ha negatív overtime-t adok hozzá,
        //  mert annak lesz egy pozitív párja is.
        $tmp = $this->getBreakTime($workstart, $workstop, $sub);
        if ($tmp > 0) {
            if (!isset($this->break_notes[$this->calc[$this->dayToProcess]['claimid']][$costid][$type])) {
                $this->break_notes[$this->calc[$this->dayToProcess]['claimid']][$costid][$type] = 0;
            }
            $this->break_notes[$this->calc[$this->dayToProcess]['claimid']][$costid][$type] += $tmp;
        }
        $this->calc[$this->dayToProcess]['costcalc'][$this->calc[$this->dayToProcess]['claimid']][$costid][$type] += $negative ? ($workstop - $workstart - $tmp) * (-1) : ($workstop - $workstart - $tmp);
        $this->calc[$this->dayToProcess]['prewrite']['costid'] = $costid;
        $this->calc[$this->dayToProcess]['prewrite']['type'] = $type;
        $this->calc[$this->dayToProcess]['prewrite']['claimid'] = $this->calc[$this->dayToProcess]['claimid'];
        $this->calc[$this->dayToProcess]['claimid']++;
    }

    private function changeType($name, $otbeforeworktime)
    {
        if ($otbeforeworktime === true) {
            switch ($name) {
                case 'otde':
                    $name = 'wtdu1';
                    break;
                case 'otdu1':
                    $name = 'wtdu2';
                    break;
                case 'otdu2':
                    $name = 'wtej';
                    break;
                case 'otej':
                    $name = 'wtde';
                    break;
                default:
                    break;
            }
        } else {
            switch ($name) {
                case 'otde':
                    $name = 'wtej';
                    break;
                case 'otdu1':
                    $name = 'wtde';
                    break;
                case 'otdu2':
                    $name = 'wtdu1';
                    break;
                case 'otej':
                    $name = 'wtdu2';
                    break;
                default:
                    break;
            }
        }
        return $name;
    }

    private function getFirstAllDayAbsence($employeestate, $newemployeestate)
    {
        if ($employeestate === $newemployeestate) {
            if (isset($this->yesterday)) {
                if (isset($this->calc[$this->yesterday]['workisalldayfirst'])) {
                    return $this->calc[$this->yesterday]['workisalldayfirst'];
                } else { //hétvége esetén lehetséges, hogy nem változik a távollét, de nincs előzőleg bementve a dátum
                    return $this->dayToProcess;
                }
            } else {
                $startDate = date(AppConfigEnum::DATE_FORMAT, strtotime($this->startDate . ' -31 day'));
                $endDate = date(AppConfigEnum::DATE_FORMAT, strtotime($this->startDate . ' -1 day'));
                $ws = $this->employeeWorkSchedule->getWorkScheduleFromLastRestday(
                    $startDate,
                    $endDate,
                    $this->employeeContractId,
                    true
                    /*$skipCheckApprovers*/,
                    'workSchedule',
                    true /*onlyFirstWorkday*/
                );
                if (isset($ws[$this->employeeContractId])) {
                    $firstWorkday = array_keys($ws[$this->employeeContractId]);
                    $absenceStartDateTime = $this->stateType->getCalculationStart($employeestate);
                    $absenceStartDate = date(AppConfigEnum::DATE_FORMAT, strtotime($absenceStartDateTime));
                    if ($firstWorkday[0] > $absenceStartDate) {
                        return $firstWorkday[0];
                    } else {
                        return $absenceStartDate;
                    }
                }
            }
        }
        return $this->dayToProcess;
    }

    private function breakTimeFromOutSide($costid, $employeestate)
    {
        $workstart = $this->stateType->getCalculationStart($employeestate);
        $workstop = $this->regRow['reg_time_ts'];
        $originalOutSide = $workstop - $workstart;
        $outSide = $originalOutSide;
        if ($this->employeeMainData['outside_breaktime'] === 'OUTSIDE_BREAKTIME_ADD_ALL') {
            //azért nem kell figyelni, hogy ebéd idő alatt távozott-e, mert úgy is csak annyit tud vissza adni, amennyit
            //a naptípusában beállított ebédidő alatt dolgozott.

            if ($this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['done'] === false) //nincs minden visszaadva
            {
                $allLunchtime = $this->dayTypeRows[$this->dayToProcess]['paid_lunchtime'] + $this->dayTypeRows[$this->dayToProcess]['lunchtime'];
                if ($allLunchtime > ($this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'] + $outSide)) {
                    $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'] += $outSide;
                } else {
                    $outSide = $allLunchtime - $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'];
                    $workstop = $workstart + $outSide;

                    $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'] = $allLunchtime;
                    $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['done'] = true;
                }
                if ($outSide > 0) {
                    $this->setOutSideToworkTime($costid, $outSide, $workstart, $workstop);
                    $outSide = $originalOutSide - $outSide;
                }
            }
        } elseif ($this->employeeMainData['outside_breaktime'] === 'OUTSIDE_BREAKTIME_ADD_NONE_PIAD') //TODO: Változó név elírás - adatbázis szerkezet módosítás
        {
            //azért nem kell figyelni, hogy ebéd idő alatt távozott-e, mert úgy is csak annyit tud vissza adni, amennyit
            //a naptípusában beállított ebédidő alatt dolgozott.

            if ($this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['done'] === false) //nincs minden visszaadva
            {
                if ($this->dayTypeRows[$this->dayToProcess]['lunchtime'] > ($this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'] + $outSide)) {
                    $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'] += $outSide;
                } else {
                    $outSide = $this->dayTypeRows[$this->dayToProcess]['lunchtime'] - $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'];
                    $workstop = $workstart + $outSide;

                    $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'] = $this->dayTypeRows[$this->dayToProcess]['lunchtime'];
                    $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['done'] = true;
                }
                if ($outSide > 0) {
                    $this->setOutSideToworkTime($costid, $outSide, $workstart, $workstop);
                    $outSide = $originalOutSide - $outSide;
                }
            }
        } elseif ($this->employeeMainData['outside_breaktime'] === 'OUTSIDE_BREAKTIME_ADD_PAID') {
            if ($this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['done'] === false) //nincs minden visszaadva
            {
                if ($this->dayTypeRows[$this->dayToProcess]['paid_lunchtime'] > ($this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'] + $outSide)) {
                    $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'] += $outSide;
                } else {
                    $outSide = $this->dayTypeRows[$this->dayToProcess]['paid_lunchtime'] - $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'];
                    $workstop = $workstart + $outSide;

                    $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'] = $this->dayTypeRows[$this->dayToProcess]['paid_lunchtime'];
                    $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['done'] = true;
                }
                if ($outSide > 0) {
                    $this->setOutSideToworkTime($costid, $outSide, $workstart, $workstop);
                    $outSide = $originalOutSide - $outSide;
                }
            }
        }
        if ($outSide > 0) {
            if ($originalOutSide > $outSide) {
                $workstart = $workstop;
                $workstop = $workstart + $outSide;
            }
            $this->breakTimeAfterFromOutSide($costid, $outSide, $workstart, $workstop);
        }
    }

    private function setOutSideToworkTime($costid, $work, $workstart, $workstop)
    {
        if ($this->workScheduleIsRestDay() &&
            $this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FLEXIBLE'
        ) {
            $remainingWork = $this->SetOvertimeStandByOverlap($costid, $work, $workstart, $workstop);
            foreach ($remainingWork as $key => $value) {
                $this->calc[$this->dayToProcess]['balance'] = $value['work'];
                $this->setClaimtoCalc($costid, 'balance', $value['work']);
            }
        } else {
            if ($this->dayTypeRows[$this->dayToProcess]['worktype'] === 'SHIFT'
                or $this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FRAMEWORK'
                or $this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FRAMEWORK_BALANCE'
                or $this->dayTypeRows[$this->dayToProcess]['worktype'] === 'STANDBYJOB') {
                $this->setWorktimeOvertime($costid, $work, $workstart, $workstop);
            } else {
                if ($this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FLEXIBLE') {
                    $remainingWork = $this->SetOvertimeStandByOverlap($costid, $work, $workstart, $workstop);
                    foreach ($remainingWork as $key => $value) {
                        $this->setWorktime($costid, $value['work'], $value['workstart'], $value['workstop']);
                    }
                } else {
                    $this->setWorktime($costid, $work, $workstart, $workstop);
                }
            }
        }
    }

    private function breakTimeAfterFromOutSide($costid, $outSide, $workstart, $workstop)
    {
        if ($this->employeeMainData['outside_breaktime'] === 'OUTSIDE_BREAKTIME_ADD_ALL') //TODO: Változó név elírás - adatbázis szerkezet módosítás
        {
            $this->initVariable($this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after']);
            //azért nem kell figyelni, hogy ebéd idő alatt távozott-e, mert úgy is csak annyit tud vissza adni, amennyit
            //a naptípusában beállított ebédidő alatt dolgozott.

            if ($this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after'] === $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime']  //minden visszaadva
            ) {
                return;
            }
            if ($this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime'] >= ($this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after'] + $outSide)) {
                $this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after'] += $outSide;
            } else {
                $outSide = $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime'] - $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'];
                $workstop = $workstart + $outSide;

                $this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after'] = $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime'];
            }
            $v = [
                'costid' => $costid,
                'outside' => $outSide,
                'workstart' => $workstart,
                'workstop' => $workstop,
            ];
            if (!isset($this->calc[$this->dayToProcess]['lunchtime']['after'])) {
                $this->calc[$this->dayToProcess]['lunchtime']['after'] = [];
            }
            if (!is_array($this->calc[$this->dayToProcess]['lunchtime']['after'])) {
                $this->calc[$this->dayToProcess]['lunchtime']['after'] = [];
            }
            array_push($this->calc[$this->dayToProcess]['lunchtime']['after'], $v);
        } elseif ($this->employeeMainData['outside_breaktime'] === 'OUTSIDE_BREAKTIME_ADD_NONE_PIAD') //TODO: Változó név elírás - adatbázis szerkezet módosítás
        {
            $this->initVariable($this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after']);
            //azért nem kell figyelni, hogy ebéd idő alatt távozott-e, mert úgy is csak annyit tud vissza adni, amennyit
            //a naptípusában beállított ebédidő alatt dolgozott.

            if ($this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after'] === $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime']  //minden visszaadva
                or $this->dayTypeRows[$this->dayToProcess]['paid_break_time_after_worktime'] === 1 //paid
            ) {
                return;
            }
            if ($this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime'] >= ($this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after'] + $outSide)) {
                $this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after'] += $outSide;
            } else {
                $outSide = $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime'] - $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'];
                $workstop = $workstart + $outSide;

                $this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after'] = $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime'];
            }
            $v = [
                'costid' => $costid,
                'outside' => $outSide,
                'workstart' => $workstart,
                'workstop' => $workstop,
            ];
            if (!isset($this->calc[$this->dayToProcess]['lunchtime']['after']) || !is_array(
                    $this->calc[$this->dayToProcess]['lunchtime']['after']
                )) {
                $this->calc[$this->dayToProcess]['lunchtime']['after'] = [];
            }
            array_push($this->calc[$this->dayToProcess]['lunchtime']['after'], $v);
        } elseif ($this->employeeMainData['outside_breaktime'] === 'OUTSIDE_BREAKTIME_ADD_PAID') {
            $this->initVariable($this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after']);

            if ($this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after'] === $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime']  //minden visszaadva
                or $this->dayTypeRows[$this->dayToProcess]['paid_break_time_after_worktime'] === 0 //unpaid
            ) {
                return;
            }
            if ($this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime'] >= ($this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after'] + $outSide)) {
                $this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after'] += $outSide;
            } else {
                $outSide = $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime'] - $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'];
                $workstop = $workstart + $outSide;

                $this->calc[$this->dayToProcess]['lunchtime']['lunchtime_back_from_outside_after'] = $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime'];
            }
            $v = [
                'costid' => $costid,
                'outside' => $outSide,
                'workstart' => $workstart,
                'workstop' => $workstop,
            ];
            if (!isset($this->calc[$this->dayToProcess]['lunchtime']['after'])) {
                $this->calc[$this->dayToProcess]['lunchtime']['after'] = [];
            }
            if (!is_array($this->calc[$this->dayToProcess]['lunchtime']['after'])) {
                $this->calc[$this->dayToProcess]['lunchtime']['after'] = [];
            }
            array_push($this->calc[$this->dayToProcess]['lunchtime']['after'], $v);
        }
    }
//----------------------------------------------------------------------------
    //------------- Write output tables ------------------------------------------
    //----------------------------------------------------------------------------
    public function writeEmployeeCalcTables(): void
    {
        $insertEmployeeCalcData = $insertEmployeeCalcDataUsedDayType = [];
        $insertEmployeeAbsence = $insertEmployeeCalcMessage = $insertEmpCalcOtherStates = [];
        $updateRegistration['not_used'] = $updateRegistration['used'] = [];
        $dayToProcess = $this->startDate;
        while ($dayToProcess <= $this->endDate) {
            $this->collectDailyEmployeeCalc($dayToProcess, $insertEmployeeCalcData, $insertEmployeeAbsence);
            $this->collectDailyEmployeeCalcUsedDayType($dayToProcess, $insertEmployeeCalcDataUsedDayType);
            $this->collectDailyRegistration($dayToProcess, $updateRegistration);
            $this->collectDailyEmployeeMessage($dayToProcess, $insertEmployeeCalcMessage);
            $this->collectDailyEmpCalcOtherState($dayToProcess, $insertEmpCalcOtherStates);

            $dayToProcessTs = strtotime($dayToProcess . '+1 day');
            $dayToProcess = date(AppConfigEnum::DATE_FORMAT, $dayToProcessTs);
        }
        if ($this->fromRunType == self::RUN_TYPE_ONLY_MEMORY) {
            $this->allEmployeeCalcData[$this->employeeContractId] = $insertEmployeeCalcData;
        } elseif ($this->optionCheck('AUTOLOCK')) {
            $this->writeRegistration($updateRegistration);
            $this->writeEmployeeCalc($insertEmployeeCalcData);
            $this->writeEmployeeCalcUsedDayType($insertEmployeeCalcDataUsedDayType);
            $this->writeEmployeeCalcMessage($insertEmployeeCalcMessage);
            $this->writeEmployeeCalcOtherStates($insertEmpCalcOtherStates);
        } else {
            $this->writeCalculateDataToDb[$this->employeeContractId][TimeCardCalculationEnum::EMPLOYEE_CALC] = $insertEmployeeCalcData;
            $this->writeCalculateDataToDb[$this->employeeContractId][TimeCardCalculationEnum::EMPLOYEE_CALC_USED_DAY_TYPE] = $insertEmployeeCalcDataUsedDayType;
            $this->writeCalculateDataToDb[$this->employeeContractId][TimeCardCalculationEnum::REGISTRATION] = $updateRegistration;
            $this->writeCalculateDataToDb[$this->employeeContractId][TimeCardCalculationEnum::EMPLOYEE_CALC_MESSAGE] = $insertEmployeeCalcMessage;
            $this->writeCalculateDataToDb[$this->employeeContractId][TimeCardCalculationEnum::EMPLOYEE_CALC_OTHER_STATES] = $insertEmpCalcOtherStates;
        }
    }

    private function collectDailyEmpCalcOtherState($dayToProcess, &$insertEmpCalcOtherStates)
    {
        if (isset($this->calc[$dayToProcess]['other_state'])
            && $this->SAVING_OTHER_STATES
        ) {
            foreach ($this->calc[$dayToProcess]['other_state'] as $state => $value) {
                $status = ($this->optionCheck('AUTOLOCK')) ?
                    Status::LOCKED :
                    (isset($state['status']) ? $state['status'] : Status::PUBLISHED);

                unset($data);
                $data['employee_contract_id'] = $this->employeeContractId;
                $data['day'] = $dayToProcess;
                $data['state_type_id'] = $state;
                $data['value'] = $value;
                $data['status'] = $status;
                $data['created_by'] = $this->createdBy;
                $data['created_on'] = date('Y-m-d H:i:s');
                array_push($insertEmpCalcOtherStates, $data);
            }
        }
    }

    private function writeEmployeeCalcOtherStates($insertEmpCalcOtherStates): void
    {
        $stateInWhere = ($this->optionCheck('AUTOLOCK') === true) ?
            "(`status`<6 OR `created_by` = '" . $this->createdBy . "')" :
            '(`status`>1 AND `status`<5)';

        dbExecute(
            "DELETE FROM employee_calc_other_states 
                    WHERE employee_contract_id = '$this->employeeContractId' 
                      AND `day` BETWEEN '$this->startDate' AND '$this->endDate' 
                      AND ( $stateInWhere OR `state_type_id` is null)"
        );
        if (count($insertEmpCalcOtherStates) > 0) {
            $this->writeMultipleInsert('employee_calc_other_states', $insertEmpCalcOtherStates);
        }
    }


    private function collectDailyRegistration($dayToProcess, &$updateRegistration)
    {
        if (isset($this->calc[$dayToProcess]['regs'])) {
            foreach ($this->calc[$dayToProcess]['regs'] as $row_id => $reg) {
                switch ($reg['calc_status']) {
                    case TimeCardCalculation::REGISTRATION_IS_NOT_USED_IN_CALCULATION:
                        array_push($updateRegistration['not_used'], $row_id);
                        break;
                    case TimeCardCalculation::REGISTRATION_IS_USED_IN_CALCULATION:
                    default:
                        array_push($updateRegistration['used'], $row_id);
                }
            }
        }
    }

    private function writeRegistration($updateRegistration)
    {
        $rowCount = 0;
        if (!empty($updateRegistration['not_used'])) {
            $sql = '
			UPDATE `registration`
				SET `calc_status` = ' . TimeCardCalculation::REGISTRATION_IS_NOT_USED_IN_CALCULATION . '
				WHERE `row_id` IN ( ' . implode(',', $updateRegistration['not_used']) . ')
		';
            $rowCount = dbExecute($sql);
        }
        if (!empty($updateRegistration['used'])) {
            $sql = '
			UPDATE `registration`
				SET `calc_status` = ' . TimeCardCalculation::REGISTRATION_IS_USED_IN_CALCULATION . '
				WHERE `row_id` IN ( ' . implode(',', $updateRegistration['used']) . ')
		';
            $rowCount = dbExecute($sql);
        }
        return $rowCount;
    }

    private function collectDailyEmployeeCalc($dayToProcess, &$insertEmployeeCalcData, &$insertEmployeeAbsence)
    {
        if (isset($this->calc[$dayToProcess][self::STATES])) {
            $this->collectDailyEmployeeCalc_ifState(
                $dayToProcess,
                $insertEmployeeCalcData,
                $insertEmployeeAbsence
            );
        } elseif (isset($this->calc[$dayToProcess][self::MARK]) &&
            (
                (
                    ($this->workScheduleIsWorkDay($dayToProcess) ||
                        ($this->workScheduleIsRestDay($dayToProcess)
                            && $this->dayTypeRows[$dayToProcess]['towork'] > 0)
                    )
                    && $this->calc[$dayToProcess]['holiday'] >= 10000
                )
                ||
                (isset($this->calc[$dayToProcess]['regscount']) && $this->calc[$dayToProcess]['regscount'] > 0)
            )
        ) {
            $this->collectDailyEmployeeCalc_OnlyErrorMessage(
                $dayToProcess,
                $insertEmployeeCalcData,
                abs($this->calc[$dayToProcess][self::MARK])
            );
        } elseif (isset($this->calc[$dayToProcess][self::CALC_ERROR])) {
            $this->collectDailyEmployeeCalc_OnlyErrorMessage(
                $dayToProcess,
                $insertEmployeeCalcData,
                $this->calc[$dayToProcess][self::CALC_ERROR]->getCode(),
            );
        }
    }

    private function collectDailyEmployeeCalc_OnlyErrorMessage(
        string $dayToProcess,
        array &$insertEmployeeCalcData,
        int $employee_calc_error_type_id
    ): void {
        unset($data);
        $data[EmployeeCalcFieldEnum::EMPLOYEE_CONTRACT_ID] = $this->employeeContractId;
        $data[EmployeeCalcFieldEnum::DAY] = $dayToProcess;
        $data[EmployeeCalcFieldEnum::INSIDE_TYPE_ID] = null;
        $data[EmployeeCalcFieldEnum::VALUE] = null;
        $data[EmployeeCalcFieldEnum::COST_ID] = null;
        $data[EmployeeCalcFieldEnum::COST_CENTER_ID] = null;
        $data[EmployeeCalcFieldEnum::SHIFT_TYPE_ID] = null;
        $data[CommonFieldEnum::STATUS] = Status::PUBLISHED;
        $data[EmployeeCalcFieldEnum::IS_PREV_DAY_CALC] = '0';
        $data[EmployeeCalcFieldEnum::IS_NEXT_DAY_CALC] = '0';
        $data[CommonFieldEnum::CREATED_BY] = $this->createdBy;
        $data[CommonFieldEnum::CREATED_ON] = date('Y-m-d H:i:s');
        $data[EmployeeCalcFieldEnum::EMPLOYEE_CALC_ERROR_TYPE_ID] =
            $employee_calc_error_type_id == 0 ?
                abs(ErrorDefinitionEnum::CALC_STATUS_VALUE_MISSING[ErrorDefinitionKeyEnum::CODE]) :
                $employee_calc_error_type_id;
        $insertEmployeeCalcData[] = $data;
    }

    private function collectDailyEmployeeCalc_ifState($dayToProcess, &$insertEmployeeCalcData, &$insertEmployeeAbsence)
    {
        foreach ($this->calc[$dayToProcess][self::STATES] as $state) {
            if (!isset($state['sstid']) and isset($state['state'])
                and isset($state['value'])) {
                unset($data);
                $data['employee_contract_id'] = $this->employeeContractId;
                $data['day'] = $dayToProcess;
                if (isset($state['workisalldayname'])) {
                    $lockable = ($this->optionCheck('AUTOLOCK') === true
                        && (!isset($this->calc[$dayToProcess][self::MARK])
                            || $this->calc[$dayToProcess][self::MARK] >= 0
                        )
                    );
                    $status = ($lockable) ? Status::LOCKED : Status::PUBLISHED;

                    $data['inside_type_id'] = null;
                    $data['value'] = null;
                    $data['cost_id'] = null;
                    $data['costcenter_id'] = null;
                    $data['status'] = $status;
                    $data['is_prev_day_calc'] = '0';
                    $data['is_next_day_calc'] = '0';

                    $emd = $this->employeesWithMainData->get($this->employeeContractId, $dayToProcess);
                    unset($dataAbsence);
                    $dataAbsence['company_id'] = $emd['company_id'];
                    $dataAbsence['employee_contract_id'] = $this->employeeContractId;
                    $dataAbsence['employee_id'] = 'NEWAPGRID';
                    $dataAbsence['day'] = $dayToProcess;
                    $dataAbsence['state_type_id'] = $state['state'];
                    $dataAbsence['employee_absence_id'] = $this->calc[$dayToProcess]['workisalldayfirst'] . '_' . $this->employeeContractId;

                    $insertEmployeeAbsence[] = $dataAbsence;
                } else {
                    $status = isset($state['status']) ? $state['status'] : Status::PUBLISHED;
                    $is_prev_day_calc = $is_next_day_calc = 0;
                    $insideTypeId = $state['state'];
                    if (strpos($state['state'], 'prev')) {
                        $insideTypeId = str_replace('prev', '', $state['state']);
                        $is_prev_day_calc = 1;
                    } elseif (strpos($state['state'], 'next')) {
                        $insideTypeId = str_replace('next', '', $state['state']);
                        $is_next_day_calc = 1;
                    }
                    $data['inside_type_id'] = $insideTypeId;
                    $data['shift_type_id'] = $this->insideType->get(
                        $dayToProcess,
                        $data['inside_type_id'],
                        'shift_type_id'
                    );
                    $data['value'] = $state['value'];
                    $data['cost_id'] = $state['costid'] ?? 1;
                    $data['costcenter_id'] = $state['costcenterid'] ?? 1;
                    $data['status'] = $status;
                    $data['is_prev_day_calc'] = $is_prev_day_calc;
                    $data['is_next_day_calc'] = $is_next_day_calc;
                    $data['note'] = $note ?? '';
                }
                $data[EmployeeCalcFieldEnum::EMPLOYEE_CALC_ERROR_TYPE_ID] =
                    (isset($this->calc[$dayToProcess][self::MARK]) ?
                        abs($this->calc[$dayToProcess][self::MARK]) :
                        0
                    );

                $data['created_by'] = $this->createdBy;
                $data['created_on'] = date('Y-m-d H:i:s');
                $insertEmployeeCalcData[] = $data;
            }
        }
        if (isset($this->calc[$dayToProcess][$this->GONEHOME_STATE_TYPE_ID])
            and $this->calc[$dayToProcess][$this->GONEHOME_STATE_TYPE_ID] > 0
            and $this->SAVING_UNDISCLOSED_TO_EMPLOYEE_CALC === true) {
            $lockable = ($this->optionCheck('AUTOLOCK') === true
                && (!isset($this->calc[$dayToProcess][self::MARK])
                    || $this->calc[$dayToProcess][self::MARK] >= 0
                )
            );
            $status = ($lockable) ? Status::LOCKED : Status::PUBLISHED;

            unset($data);
            $data['employee_contract_id'] = $this->employeeContractId;
            $data['day'] = $dayToProcess;
            $data['inside_type_id'] = $this->UNDISCLOSED_STATE_TYPE_ID;
            $data['value'] = $this->calc[$dayToProcess][$this->GONEHOME_STATE_TYPE_ID];
            $data['cost_id'] = null;
            $data['costcenter_id'] = null;
            $data['status'] = $status;
            $data['is_prev_day_calc'] = '0';
            $data['is_next_day_calc'] = '0';
            $data['created_by'] = $this->createdBy;
            $data['created_on'] = date('Y-m-d H:i:s');

            $insertEmployeeCalcData[] = $data;
        }
    }

    private function writeEmployeeCalc($insertEmployeeCalcData): void
    {
        $stateInWhere = ($this->optionCheck('AUTOLOCK') === true) ?
            "(`status`<6 OR `created_by` = '" . $this->createdBy . "')" :
            '(`status`>1 AND `status`<5)';

        EmployeeCalc::model()->deleteAll(
            "employee_contract_id = '$this->employeeContractId' 
                AND `day` BETWEEN '$this->startDate' AND '$this->endDate' 
                AND 
                ( 
                    (`status`=1 /*AND `created_by`='$this->createdBy' */) 
                    OR $stateInWhere 
                    OR `inside_type_id` is null
                )"
        );
        if (count($insertEmployeeCalcData) > 0) {
            $this->writeMultipleInsert('employee_calc', $insertEmployeeCalcData);
        }
    }

    private function collectDailyEmployeeCalcUsedDayType($dayToProcess, &$insertEmployeeCalcDataUsedDayType)
    {
        $isUnLocked = (isset($this->calc[$dayToProcess][self::LOCKED]) and $this->calc[$dayToProcess][self::LOCKED] === false);
        $isDayTypeProp = isset($this->dayProperties[$dayToProcess]['daytypeid']);
        $isNotCalcResults = !isset($this->employeeCalcResults[$this->employeeContractId][$dayToProcess]);
        $isAutolock = ($this->optionCheck('AUTOLOCK') === true);
        if ($isUnLocked
            and $isDayTypeProp
            and ($isNotCalcResults or $isAutolock)
        ) {
            $lockable = ($this->optionCheck('AUTOLOCK') === true
                && (!isset($this->calc[$dayToProcess][self::MARK])
                    || $this->calc[$dayToProcess][self::MARK] >= 0
                )
            );
            $status = ($lockable) ? Status::LOCKED : Status::PUBLISHED;
            $ews = $this->employeeWorkSchedule->get($this->employeeContractId, $this->dayToProcess);

            $data['employee_contract_id'] = $this->employeeContractId;
            $data['day'] = $dayToProcess;
            $data['used_daytype_table_name'] = $this->dayTypeRows[$dayToProcess]['used_daytype_table_name'] ?? null;
            $data['used_table_name'] = $this->dayTypeRows[$dayToProcess]['used_table_name'] ?? null;
            $data['daytype_id'] = $this->dayProperties[$dayToProcess]['daytypeid'] ?? null;
            $data['reg_filter_from'] = $this->calc[$dayToProcess]['seldt']['min'] ?? null;
            $data['reg_filter_to'] = $this->calc[$dayToProcess]['seldt']['max'] ?? null;
            $data['reg_filter_from_origi'] = $this->calc[$dayToProcess]['seldt']['min_origi'] ?? null;
            $data['reg_filter_to_origi'] = $this->calc[$dayToProcess]['seldt']['max_origi'] ?? null;
            $data['used_work_time_day_shift'] = $this->dayTypeRows[$dayToProcess]['used_work_time_day_shift'] ?? null;
            $data['used_work_time_evening_shift'] = $this->dayTypeRows[$dayToProcess]['used_work_time_evening_shift'] ?? null;
            $data['used_work_time_night_shift'] = $this->dayTypeRows[$dayToProcess]['used_work_time_night_shift'] ?? null;
            $data['firstreg'] = $this->dayTypeRows[$dayToProcess]['firstreg'] ?? null;
            $data['lastreg'] = $this->dayTypeRows[$dayToProcess]['lastreg'] ?? null;
            $data['status'] = $status;
            $data['created_by'] = $this->createdBy;
            $data['created_on'] = date('Y-m-d H:i:s');
            $data['correct_calculation'] = $ews['used_correct_calculation'] ?? 0;
            $insertEmployeeCalcDataUsedDayType[] = $data;
        }
    }

    private function writeEmployeeCalcUsedDayType($insertEmployeeCalcDataUsedDayType): void
    {
        $stateInWhere = ($this->optionCheck('AUTOLOCK') === true) ?
            "(`status`<6 OR `created_by` = '" . $this->createdBy . "')" :
            '(`status`>1 AND `status`<5)';
        EmployeeCalcUsedDaytype::model()->deleteAll(
            "employee_contract_id = '$this->employeeContractId'
                AND day BETWEEN '$this->startDate' 
                AND '$this->endDate' 
                AND $stateInWhere"
        );
        if (count($insertEmployeeCalcDataUsedDayType) > 0) {
            $this->writeMultipleInsert('employee_calc_used_daytype', $insertEmployeeCalcDataUsedDayType);
        }
    }

    private function writeEmployeeAbsence($insertEmployeeAbsence)
    {
        //EmployeeCalc::model()->deleteAll("employee_contract_id = '$this->employeeContractId' AND day BETWEEN '$this->startDate' AND '$this->endDate' AND (status<5 OR `inside_type_id` is null)");
        if (count($insertEmployeeAbsence) > 0) {
            $this->writeMultipleInsert('employee_calc', $insertEmployeeAbsence);
        }
    }

    private function collectDailyEmployeeMessage($dayToProcess, &$insertEmployeeCalcMessage)
    {
        if (isset($this->calc[$dayToProcess]['warning'])) {
            foreach ($this->calc[$dayToProcess]['warning'] as $name => $value) {
                if ($value === true && isset($this->RULE_INDICATION[$name]) && $this->RULE_INDICATION[$name] > 0) {
                    $lockable = ($this->optionCheck('AUTOLOCK') === true
                        && (!isset($this->calc[$dayToProcess][self::MARK])
                            || $this->calc[$dayToProcess][self::MARK] >= 0
                        )
                    );
                    $status = ($lockable) ? Status::LOCKED : Status::PUBLISHED;

                    $data['employee_contract_id'] = $this->employeeContractId;
                    $data['day'] = $dayToProcess;
                    $data['message_type'] = 'warning';
                    $data['message_id'] = $name;
                    $data['dict_id'] = $name;
                    $data['status'] = $status;
                    $data['created_by'] = $this->createdBy;
                    $data['created_on'] = date('Y-m-d H:i:s');
                    $insertEmployeeCalcMessage[] = $data;
                }
            }
        }
    }

    private function writeEmployeeCalcMessage($insertEmployeeCalcMessage): void
    {
        $stateInWhere = ($this->optionCheck('AUTOLOCK') === true) ?
            "(`status`<6 OR `created_by` = '" . $this->createdBy . "')" :
            '(`status`>1 AND `status`<5)';

        EmployeeCalcMessage::model()->deleteAll(
            "employee_contract_id = '$this->employeeContractId' 
                AND day BETWEEN '$this->startDate' AND '$this->endDate' 
                AND ($stateInWhere OR `message_id` is null)"
        );
        if (count($insertEmployeeCalcMessage) > 0) {
            $this->writeMultipleInsert('employee_calc_message', $insertEmployeeCalcMessage);
        }
    }

    private function writeMultipleInsert($tableName, $insertData)
    {
        dbMultiInsert($tableName, $insertData);
    }

    public function getAllCalcData(): array
    {
        return $this->allEmployeeCalcData;
    }

    //----------------------------------------------------------------------------
    //------------- Load DayTypeRows ----------------------------------------------
    //----------------------------------------------------------------------------
    private function loadDayTypeRowsIfNotExists()
    {
        if (!empty($this->dayTypeRows)) {
            return;
        }
        $this->dayTypeRows = [];
        $nextDayTs = strtotime($this->dayToProcess . '-1 day');
        $nextDay = date(AppConfigEnum::DATE_FORMAT, $nextDayTs);
        $decadeStopNextDayTs = strtotime($this->endDate . '+1 day');
        while ($nextDayTs <= $decadeStopNextDayTs) {
            $employeeMainData = $this->employeesWithMainData->get($this->employeeContractId, $nextDay);
            if (is_null($employeeMainData)) {
                $errorDescriptor = new ErrorDefinitionsDescriptor(
                    ErrorDefinitionEnum::EMPLOYEE_NOT_FOUND,
                    ['day' => $nextDay, 'employeeContractId' => $this->employeeContractId]
                );
                if (self::DEBUG_INFO_TO_APPLICATION_LOG === self::DEBUG_LEVEL_FULL) {
                    $message = 'Log: Init TimeCardCalculation ' .
                        $this->calc[$nextDay][self::CALC_ERROR]->formatMessage();
                    Yang::log($message, Yang::LOGLEVEL_LOG, 'system.Calculation');
                }
                $this->calc[$nextDay][self::CALC_ERROR] = $errorDescriptor;
                $this->errorDescriptors[] = $errorDescriptor;
            } else {
                $employeeWorkScheduleRow = $this->employeeWorkSchedule->get($this->employeeContractId, $nextDay);
                $publicHolidayRow = $this->publicHolidays->get(
                    $nextDay,
                    $this->PAID_PUBLIC_HOLIDAY_TYPES,
                    $employeeMainData['country']
                );

                $this->dayProperties[$nextDay]['daytypeid'] = $employeeWorkScheduleRow['used_daytype_id'];
                $this->dayTypeRows[$nextDay] = $this->loadDayTypeRowOneDay(
                    $nextDay,
                    $employeeWorkScheduleRow,
                    $publicHolidayRow,
                    $employeeMainData
                );
            }
            $nextDayTs = strtotime($nextDay . '+1 day');
            $nextDay = date(AppConfigEnum::DATE_FORMAT, $nextDayTs);
        } //nex day
    }

    private function loadDayTypeRowOneDay(
        $dayToProcess,
        $employeeWorkScheduleRow,
        $publicHolidayRow,
        $employeeMainData
    ): array {
        $ews = $employeeWorkScheduleRow;
        if (empty($ews)) {
            $this->calc[$dayToProcess][self::MARK] =
                ErrorDefinitionEnum::CALC_STATUS_DAYTYPE_MISSING[ErrorDefinitionKeyEnum::CODE];
            return [];
        }
        $dayString = ($ews['used_pre_day'] === 'TRUE') ?
            date(AppConfigEnum::DATE_FORMAT, strtotime($dayToProcess . '-1 day')) :
            $dayToProcess;
        $otMgmtRestDayUseInterval = ($this->OT_MGMT_REST_DAY_USE_INTERVAL && $ews['overtime_rd_start']);

        if ($this->SHIFT_START_IN_DAYTYPE) {
            $workTimeToShift = new WorkTimeToShift(
                $this->employeeWorkSchedule->getShiftStartType($this->employeeContractId, $dayToProcess),
                $this->employeeMainData['sunday_sign_interval'] ?? '00:00:00'
            );
        } elseif ($this->SHIFT_START_IN_WORKGROUP || $otMgmtRestDayUseInterval) {
            $workTimeToShift = new WorkTimeToShift(
                $this->employeesWithMainData->getShiftStartType($this->employeeContractId, $dayToProcess),
                $this->employeeMainData['sunday_sign_interval'] ?? '00:00:00'
            );
        }

        if ($ews['used_work_time_day_shift']) {
            $this->calc[$dayToProcess]['wtde'] = (integer)$ews['used_work_time_day_shift'];
        }
        if ($ews['used_work_time_evening_shift']) {
            $this->calc[$dayToProcess]['wtdu'] = (integer)$ews['used_work_time_evening_shift'];
        }
        if ($ews['used_work_time_night_shift']) {
            $this->calc[$dayToProcess]['wtej'] = (integer)$ews['used_work_time_night_shift'];
        }

        $dtv['twworkorder'] = (integer)$employeeMainData['work_order'];

        $dtv['used_table_name'] = $ews['used_table_name'];
        $dtv['used_daytype_table_name'] = $ews['used_daytype_table_name'];
        $dtv['used_daytype_name'] = $ews['used_daytype_name'];
        $dtv['used_type_of_daytype'] = $ews['used_type_of_daytype'];
        $dtv['used_daytype_id'] = $ews['used_daytype_id'];


        if ($otMgmtRestDayUseInterval) {
            $dtv['used_work_start'] = $this->convertTimeStringToSeconds($ews['overtime_rd_start'], $dayString);
            $dtv['used_work_end'] = $this->convertTimeStringToSeconds($ews['overtime_rd_end'], $dayString);

            $dtv['orig_work_start'] = $this->convertTimeStringToSeconds($ews['used_work_start'], $dayString);
            $dtv['orig_work_end'] = $this->convertTimeStringToSeconds($ews['used_work_end'], $dayString);

            $dtv['used_pause_start'] = $this->convertTimeStringToSeconds($ews['used_pause_start'], $dayString);
            $dtv['used_pause_end'] = $this->convertTimeStringToSeconds($ews['uase_pause_end'], $dayString);

            $startMove = $dtv['used_work_start'] - $dtv['orig_work_start'];
            $endMove = $dtv['used_work_end'] - $dtv['orig_work_end'];

            $dtv['twstarteat'] = $this->convertTimeStringToSeconds($ews['used_earliest_arrival_time'], $dayString)
                + $startMove;
            $dtv['twstopldt'] = $this->convertTimeStringToSeconds($ews['used_latest_depart_time'], $dayString)
                + $endMove;
        } else {
            $dtv['used_work_start'] =
                $this->convertTimeStringToSeconds($ews['used_work_start'], $dayString, $this->DATE_TIMEZONE);
            $dtv['used_work_end'] =
                $this->convertTimeStringToSeconds($ews['used_work_end'], $dayString, $this->DATE_TIMEZONE);
            $dtv['twstarteat'] =
                $this->convertTimeStringToSeconds(
                    $ews['used_earliest_arrival_time'],
                    $dayString,
                    $this->DATE_TIMEZONE
                );
            $dtv['twstopldt'] =
                $this->convertTimeStringToSeconds(
                    $ews['used_latest_depart_time'],
                    $dayString,
                    $this->DATE_TIMEZONE
                );
            $dtv['used_pause_start'] =
                $this->convertTimeStringToSeconds($ews['used_pause_start'], $dayString, $this->DATE_TIMEZONE);
            $dtv['used_pause_end'] =
                $this->convertTimeStringToSeconds($ews['used_pause_end'], $dayString, $this->DATE_TIMEZONE);
        }

        if ($dtv['used_pause_end'] < $dtv['used_pause_start']) {
            $dayPlusOneDayString = date(AppConfigEnum::DATE_FORMAT, strtotime($dayString . ' +1 day'));
            $dtv['used_pause_end'] = $this->convertTimeStringToSeconds(
                $ews['used_pause_end'],
                $dayPlusOneDayString,
                $this->DATE_TIMEZONE
            );
        }

        if ($dtv['used_pause_end'] == $dtv['used_pause_start']) {
            $dtv['used_pause_start'] = 0;
            $dtv['used_pause_end'] = 0;
        }

        $dtv['twtolerance'] = $this->convertTimeStringToSeconds($ews['used_tolerance']);
        $dtv['used_tolerance_before'] = $this->SPLIT_DAYTYPE_TOLERANCE ?
            $this->convertTimeStringToSeconds($ews['used_tolerance_before']) :
            $this->convertTimeStringToSeconds($ews['used_tolerance']);
        $dtv['used_tolerance_after'] = $this->SPLIT_DAYTYPE_TOLERANCE ?
            $this->convertTimeStringToSeconds($ews['used_tolerance_after']) :
            $this->convertTimeStringToSeconds($ews['used_tolerance']);

        $dtv['used_work_time_day_shift'] = (integer)$ews['used_work_time_day_shift'];
        $dtv['used_work_time_evening_shift'] = (integer)$ews['used_work_time_evening_shift'];
        $dtv['used_work_time_evening_shift_1'] = (integer)$ews['used_work_time_evening_shift_1'];
        $dtv['used_work_time_evening_shift_2'] = (integer)$ews['used_work_time_evening_shift_2'];
        $dtv['used_work_time_night_shift'] = (integer)$ews['used_work_time_night_shift'];
        $dtv['towork'] = (integer)$ews['used_full_work_time'];

        $dtv['used_pre_day'] = $ews['used_pre_day'] === 'TRUE';

        $dtv['increase_worktime_value'] = (integer)$ews['increase_worktime_value'];
        $dtv['increase_worktime_greater_than'] = (integer)$ews['increase_worktime_greater_than'];
        $dtv['skip_lunchtime_after_worked_less_than'] = (integer)$ews['skip_lunchtime_after_worked_less_than'];

        $dtv['OVERTIME_AFTER_WORKTIME_OVERRIDE'] = isset($ews['OVERTIME_AFTER_WORKTIME_OVERRIDE']);

        $dtv['lunchtime'] = 0;
        $dtv['paid_lunchtime'] = 0;
        $sumBreakTimeShift = [];
        $sumBreakTimeShiftWithPrefix = [];
        for ($i = 1; $i <= 5; $i++) {
            if ((integer)$ews["used_paid_break_time_$i"] === 0) {
                $dtv['breaks'][$i]['break_time_from'] = (integer)$ews["used_break_time_duration_$i"] ? $this->convertTimeStringToSeconds(
                    $ews["used_break_time_from_$i"],
                    $dayString
                ) : 0;
                if ($dtv['breaks'][$i]['break_time_from'] > 0 and $dtv['breaks'][$i]['break_time_from'] < $dtv['used_work_start']) {
                    $dayPlusOneDayString = date(AppConfigEnum::DATE_FORMAT, strtotime($dayString . ' +1 day'));
                    $dtv['breaks'][$i]['break_time_from'] = (integer)$ews["used_break_time_duration_$i"] ? $this->convertTimeStringToSeconds(
                        $ews["used_break_time_from_$i"],
                        $dayPlusOneDayString
                    ) : 0;
                }
                $dtv['breaks'][$i]['break_time'] = (integer)$ews["used_break_time_duration_$i"];
                $dtv['lunchtime'] += $dtv['breaks'][$i]['break_time'];

                if ($otMgmtRestDayUseInterval && $startMove != 0 && $dtv['breaks'][$i]['break_time_from'] != 0) {
                    $dtv['breaks'][$i]['break_time_from'] += $startMove;
                }

                if ($this->SHIFT_START_IN_DAYTYPE || $this->SHIFT_START_IN_WORKGROUP || ($otMgmtRestDayUseInterval && $startMove != 0)) {
                    $workTimeToShift
                        ->setPublicHolidays($this->publicHolidaysThisCountry)
                        ->disableSundaySign()
                        ->setEmployeeMainData($employeeMainData);
                    $breakTimeShift = $workTimeToShift->getShift(
                        $dayString,
                        $dtv['breaks'][$i]['break_time_from'],
                        ($dtv['breaks'][$i]['break_time_from'] + $dtv['breaks'][$i]['break_time'])
                    );
                    $sumBreakTimeShift = $this->shiftArraySum($sumBreakTimeShift, $breakTimeShift);

                    $workTimeToShift
                        ->enableSundaySign()
                        ->setPublicHolidays($this->publicHolidaysThisCountry)
                        ->setEmployeeMainData($employeeMainData);
                    $breakTimeShiftWithPrefix = $workTimeToShift->getShift(
                        $dayString,
                        $dtv['breaks'][$i]['break_time_from'],
                        ($dtv['breaks'][$i]['break_time_from'] + $dtv['breaks'][$i]['break_time'])
                    );
                    $sumBreakTimeShiftWithPrefix = $this->shiftArraySum(
                        $sumBreakTimeShiftWithPrefix,
                        $breakTimeShiftWithPrefix
                    );
                }
            } else {
                $dtv['paid_lunchtime'] += (integer)$ews["used_break_time_duration_$i"];
            }
        }

        if ($this->USED_WORKTIME_FROM &&
            $dtv['used_type_of_daytype'] === WorkScheduleUsedTypeOfDayTypeSourceEnum::WORKDAY &&
            (bool)$employeeMainData['use_acs']
        ) {
            $dtv['used_work_end'] = $dtv['used_work_start'] + ((integer)$employeeMainData['daily_worktime']) * 3600 + $dtv['lunchtime'];
            $dtv['twstopldt'] = $dtv['used_work_end'];
            $dtv['towork'] = ((integer)$employeeMainData['daily_worktime']) * 3600;
        }

        if ($this->APP_SETTINGS_CALC_DAILY_WORKTIME_PLACE === 'CONTRACT' &&
            $employeeMainData['work_type'] === 'FRAMEWORK_BALANCE' &&
            $dtv['used_type_of_daytype'] === WorkScheduleUsedTypeOfDayTypeSourceEnum::WORKDAY
        ) {
            $dtv['used_work_end'] = $dtv['used_work_start'] + ((integer)$employeeMainData['daily_worktime']) * 3600;
            $dtv['twstopldt'] = $dtv['used_work_end'];
            $dtv['towork'] = ((integer)$employeeMainData['daily_worktime']) * 3600;
            //$dtv['used_work_time_night_shift'] = $dtv['towork'];
        }
        $workscheduleChangeIn96hPreviousWorkscheduleIsNull = true;
        if (isset($ews['workscheduleChangeIn96h_previousWorkscheduleIsNull'])) {
            $workscheduleChangeIn96hPreviousWorkscheduleIsNull = $ews['workscheduleChangeIn96h_previousWorkscheduleIsNull'] <> true;
        }
        if ($dtv['used_work_end'] < $dtv['used_work_start'] || $dtv['twstopldt'] < $dtv['used_work_start']) {
            $dayPlusOneDayString = date(AppConfigEnum::DATE_FORMAT, strtotime($dayString . ' +1 day'));
            if ($dtv['used_work_end'] < $dtv['used_work_start']) {
                $dtv['used_work_end'] = $this->convertTimeStringToSeconds(
                    $ews['used_work_end'],
                    $dayPlusOneDayString,
                    $this->DATE_TIMEZONE
                );
            }
            if ($dtv['twstopldt'] < $dtv['used_work_start']) {
                $dtv['twstopldt'] = $this->convertTimeStringToSeconds(
                    $ews['used_latest_depart_time'],
                    $dayPlusOneDayString,
                    $this->DATE_TIMEZONE
                );
            }
            $dtv['towork'] =
                (integer)$ews['used_full_work_time']
                + ($this->getDateTimeZoneHourDifference(
                    $ews['used_work_start'],
                    $ews['used_work_end'],
                    $dayString,
                    $dayPlusOneDayString
                ));
        } elseif ($dtv['used_work_end'] == $dtv['used_work_start'] &&
            $ews['used_type_of_daytype'] === WorkScheduleUsedTypeOfDayTypeSourceEnum::WORKDAY &&
            $workscheduleChangeIn96hPreviousWorkscheduleIsNull
        ) {
            $dayPlusOneDayString = date(AppConfigEnum::DATE_FORMAT, strtotime($dayString . ' +1 day'));
            $dtv['used_work_end'] = $this->convertTimeStringToSeconds($ews['used_work_end'], $dayPlusOneDayString);
            $dtv['twstopldt'] = $this->convertTimeStringToSeconds(
                $ews['used_latest_depart_time'],
                $dayPlusOneDayString
            );
        }

        if ($ews['used_ordinary_time_start'] === '00:00') {
            $dtv['used_ordinary_time_start'] = false;
        } else {
            $dtv['used_ordinary_time_start'] = $this->convertTimeStringToSeconds(
                $ews['used_ordinary_time_start'],
                $dayString,
                $this->DATE_TIMEZONE
            );
            if ($dtv['used_ordinary_time_start'] < $dtv['used_work_start']) {
                $dayPlusOneDayString = date(AppConfigEnum::DATE_FORMAT, strtotime($dayString . ' +1 day'));
                $dtv['used_ordinary_time_start'] = $this->convertTimeStringToSeconds(
                    $ews['used_ordinary_time_start'],
                    $dayPlusOneDayString,
                    $this->DATE_TIMEZONE
                );
            }
            if ($otMgmtRestDayUseInterval && $startMove != 0) {
                $dtv['used_ordinary_time_start'] += $startMove;
            }
        }

        if ($ews['used_ordinary_time_end'] === '00:00') {
            $dtv['used_ordinary_time_end'] = false;
        } else {
            $dtv['used_ordinary_time_end'] = $this->convertTimeStringToSeconds(
                $ews['used_ordinary_time_end'],
                $dayString,
                $this->DATE_TIMEZONE
            );
            if ($dtv['used_ordinary_time_end'] < $dtv['used_work_start']) {
                $dayPlusOneDayString = date(AppConfigEnum::DATE_FORMAT, strtotime($dayString . ' +1 day'));
                $dtv['used_ordinary_time_end'] = $this->convertTimeStringToSeconds(
                    $ews['used_ordinary_time_end'],
                    $dayPlusOneDayString,
                    $this->DATE_TIMEZONE
                );
            }
            if ($otMgmtRestDayUseInterval && $endMove != 0) {
                $dtv['used_ordinary_time_end'] += $endMove;
            }
        }

        if ($ews['overtime_type'] === WorkScheduleUsedTypeOfDayTypeSourceEnum::RESTDAY) {
            $dtv['overtimeTime'] = (int)$dtv['towork'];
        } else {
            $dtv['overtimeTime'] = (int)$ews['overtime_time'];
        }
        $dtv['overtimeType'] = $ews['overtime_type'];

        $dtv['maxovertime_before'] = $this->convertTimeStringToSeconds($ews['used_overtime_from_work_start']);
        if (!empty($dtv['overtimeType'])
            and $dtv['overtimeType'] === 'BEFOREWORK'
            and $dtv['overtimeTime'] > $dtv['maxovertime_before']) {
            $dtv['maxovertime_before'] = $dtv['overtimeTime'];
        }
        $dtv['maxovertime_after'] = $this->convertTimeStringToSeconds($ews['used_work_end_from_overtime']);
        if (!empty($dtv['overtimeType'])
            and $dtv['overtimeType'] === 'AFTERWORK'
            and $dtv['overtimeTime'] > $dtv['maxovertime_after']) {
            $dtv['maxovertime_after'] = $dtv['overtimeTime'];
        }
        $dtv['maxovertime_weekend'] = $dtv['maxovertime_before'] + $dtv['maxovertime_after'];


        $this->dayTypeBreaksTime[$dayToProcess] = $dtv['breaks'] ?? null;
        $dtv['break_time_after_worktime'] = $this->convertTimeStringToSeconds(
            $ews['used_break_time_after_worktime']
        );
        $dtv['break_time_after_worktime_from'] = (integer)$ews['used_break_time_after_worktime_duration'] ? $this->convertTimeStringToSeconds(
            $ews['used_break_time_after_worktime_from'],
            $dayString
        ) : 0;
        $dtv['break_time_after_worktime_duration'] = (integer)$ews['used_break_time_after_worktime_duration'];
        $dtv['paid_break_time_after_worktime'] = (integer)$ews['used_paid_break_time_after_worktime'];

        if (($otMgmtRestDayUseInterval && $startMove != 0) && $dtv['break_time_after_worktime_from'] != 0) {
            $dtv['break_time_after_worktime_from'] += $startMove;
        }

        $dtv['lunchtime_back_from_outside']['val'] = 0;
        $dtv['lunchtime_back_from_outside']['done'] =
            !(($this->employeeMainData['outside_breaktime'] === 'OUTSIDE_BREAKTIME_ADD_NONE_PIAD'
                || $this->employeeMainData['outside_breaktime'] === 'OUTSIDE_BREAKTIME_ADD_PAID'
                || $this->employeeMainData['outside_breaktime'] === 'OUTSIDE_BREAKTIME_ADD_ALL'
            ));

        if ($employeeMainData['work_type'] === 'FLEXIBLE_BASED_ON_CONTRACT') {
            $dtv['worktype'] = 'FLEXIBLE';
            $dtv['worktype_ext'] = $employeeMainData['work_type'];
        } else {
            $dtv['worktype'] = $employeeMainData['work_type'];
            $dtv['worktype_ext'] = 'NONE';
        }
        $dtv['balance_min'] = $this->convertTimeStringToSeconds($employeeMainData['balance_min']);
        $dtv['balance_max'] = (-1) * $this->convertTimeStringToSeconds($employeeMainData['balance_max']);
        $dtv['balance_step'] = $this->convertTimeStringToSeconds($employeeMainData['balance_step']);
        $dtv['balance_step'] = $dtv['balance_step'] === 0 ? 1 : $dtv['balance_step'];
        $dtv['worktime_interval_min'] = $this->convertTimeStringToSeconds(
            $employeeMainData['worktime_interval_min']
        );
        $dtv['worktime_interval_min'] = $dtv['worktime_interval_min'] === 0 ? 1 : $dtv['worktime_interval_min'];
        $dtv['overtime_interval_min'] = $this->convertTimeStringToSeconds(
            $employeeMainData['overtime_interval_min']
        );
        $dtv['overtime_interval_min'] = $dtv['overtime_interval_min'] === 0 ? 1 : $dtv['overtime_interval_min'];
        $dtv['cost_time_interval_min'] = $this->convertTimeStringToSeconds(
            $employeeMainData['cost_time_interval_min']
        );
        $dtv['cost_time_interval_min'] = $dtv['cost_time_interval_min'] === 0 ? 1 : $dtv['cost_time_interval_min'];
        $dtv['entryexit_tol'] = $this->convertTimeStringToSeconds(
            substr($this->employeeMainData['entry_exit_tolerance'], 1)
        );
        if (substr($employeeMainData['entry_exit_tolerance'], 0, 1) === '-') {
            $dtv['entryexit_tol'] *= (-1);
        }
        $dtv['balanceroundtolerance'] = $this->convertTimeStringToSeconds(
            substr($this->employeeMainData['balance_round_tolerance'], 1)
        );
        if (substr($employeeMainData['balance_round_tolerance'], 0, 1) === '-') {
            $dtv['balanceroundtolerance'] *= (-1);
        }

        $dtv['negativeovertime'] = (isset($employeeMainData['negative_overtime']) && $employeeMainData['negative_overtime'] === 'TRUE');
        $dtv['monitor_break'] = $this->convertTimeStringToSeconds($employeeMainData['monitor_break']);
        $dtv['time_interval_min_type'] = $employeeMainData['time_interval_min_type'] ?? ''; //csonkolas, kerekítés,
        //$dtv['dailyfulltime']			= $employeeMainData['daily_full_time']*3600;
        $dtv['allowance'] = (int)$employeeMainData['allowance'];
        $dtv['use_acs'] = !((int)$employeeMainData['use_acs'] === 0);
        $dtv['shifting'] = 1;
        $dtv['maxovertime'] = ($dtv['used_type_of_daytype'] === WorkScheduleUsedTypeOfDayTypeSourceEnum::RESTDAY) ?
            $dtv['maxovertime_weekend'] :
            $dtv['maxovertime_before'] + $dtv['maxovertime_after'];
        $dtv['filter_only_filo_regs'] = !((int)$employeeMainData['filter_only_filo_regs'] === 0);
        $dtv['daily_working_time_consolidation'] =
            !((int)$employeeMainData['daily_working_time_consolidation'] === 0);
        $dtv['paid_lunchtime_reduction'] =
            (isset($employeeMainData['paid_lunchtime_reduction'])
                && $employeeMainData['paid_lunchtime_reduction'] === 'TRUE');
        $this->setDailyConst(
            $dayString
        );    //TODO: be kellene epiteni a naptipus valtozoba es azt hasznalni, meg lehetne sporolni a napi betoltes idejet

        if ($this->SHIFT_START_IN_DAYTYPE || $this->SHIFT_START_IN_WORKGROUP || ($otMgmtRestDayUseInterval && $startMove != 0)) {
            $workTimeToShift
                ->disableSundaySign()
                ->setPublicHolidays($this->publicHolidaysThisCountry)
                ->setEmployeeMainData($employeeMainData);
            $sh = $workTimeToShift->getShift(
                $dayString,
                $dtv['used_work_start'],
                $dtv['used_work_end']
            );
            $shift = $this->shiftArraySub($sh, $sumBreakTimeShift);

            foreach ($this->SHIFT_TYPE as $key => $value) {
                $found_key = array_search($key, array_column($shift, 'insideType'));
                $dtv[$value] = ($found_key === false) ? 0 : $shift[$found_key]['value'];
            }
            $dtv['used_work_time_evening_shift'] = $dtv['used_work_time_evening_shift_1'] + $dtv['used_work_time_evening_shift_2'];
            $dtv['towork'] = $dtv['used_work_time_day_shift'] + $dtv['used_work_time_evening_shift'] + $dtv['used_work_time_night_shift'];
            $workTimeToShift
                ->enableSundaySign()
                ->setPublicHolidays($this->publicHolidaysThisCountry)
                ->setEmployeeMainData($employeeMainData);
            $sh = $workTimeToShift->getShift(
                $dayString,
                $dtv['used_work_start'],
                $dtv['used_work_end']
            );
            $shifts_with_prefix = $this->shiftArraySub($sh, $sumBreakTimeShiftWithPrefix);

            foreach ($shifts_with_prefix as $i => $shp) {
                $prefix = $shp['prefix'];
                $shift = $shp['shift'];
                switch ($dtv['twworkorder']) {
                    case 0:
                    case 1:
                        $this->InitAndAddValueToVariable($dtv['shifts_with_prefix']['de' . $prefix], $shp['value']);
                        break;
                    case 2:
                        if ($shift === 'ej') {
                            $this->InitAndAddValueToVariable(
                                $dtv['shifts_with_prefix']['du2' . $prefix],
                                $shp['value']
                            );
                        } else {
                            $this->InitAndAddValueToVariable(
                                $dtv['shifts_with_prefix'][$shift . $prefix],
                                $shp['value']
                            );
                        }
                        break;
                    default:
                        $this->InitAndAddValueToVariable(
                            $dtv['shifts_with_prefix'][$shift . $prefix],
                            $shp['value']
                        );
                }
            }
        }
        $dtv['used_work_time_night_shift'] += $this->ruleCalcModifyNightShiftDaytypeCompensatoryTime(
            $dtv['used_work_start']
        );
        $dtv['isNightShift'] = (bool)$dtv['used_work_time_night_shift']; // NIGHTSHIFTCOMPENSATORYTIME (Pl.: OBO)
        if (is_null($publicHolidayRow)) {
            $this->calc[$dayToProcess]['holiday'] = $this->calc[$dayToProcess]['holiday'] ?? 10000;
        } else {
            $this->calc[$dayToProcess]['holiday'] =
                $this->RULE_INDICATION['holidayerrorsignal'] ? 10000 : $publicHolidayRow['type'];
        }
        $dtv['founddaytype'] = true;

        switch ($dtv['twworkorder']) {
            case 0:
            case 1:
                $dtv['diff_between_daytype_and_calc']['wtde'] = $dtv['used_work_time_day_shift'] +
                    $dtv['used_work_time_evening_shift_1'] +
                    $dtv['used_work_time_evening_shift_2'] +
                    $dtv['used_work_time_night_shift'];
                $dtv['diff_between_daytype_and_calc']['wtdu1'] = 0;
                $dtv['diff_between_daytype_and_calc']['wtdu2'] = 0;
                $dtv['diff_between_daytype_and_calc']['wtej'] = 0;

                $dtv['used_work_time_day_shift'] += $dtv['used_work_time_evening_shift'] + $dtv['used_work_time_night_shift'];
                $dtv['used_work_time_evening_shift'] = 0;
                $dtv['used_work_time_evening_shift_1'] = 0;
                $dtv['used_work_time_evening_shift_2'] = 0;
                $dtv['used_work_time_night_shift'] = 0;
                break;

            case 2:
                $dtv['diff_between_daytype_and_calc']['wtde'] = $dtv['used_work_time_day_shift'];
                $dtv['diff_between_daytype_and_calc']['wtdu1'] = $dtv['used_work_time_evening_shift_1'];
                $dtv['diff_between_daytype_and_calc']['wtdu2'] = $dtv['used_work_time_evening_shift_2'] +
                    $dtv['used_work_time_night_shift'];
                $dtv['diff_between_daytype_and_calc']['wtej'] = 0;
                $dtv['used_work_time_evening_shift'] += $dtv['used_work_time_night_shift'];
                $dtv['used_work_time_evening_shift_2'] += $dtv['used_work_time_night_shift'];
                $dtv['used_work_time_night_shift'] = 0;
                break;
            default:
                $dtv['diff_between_daytype_and_calc']['wtde'] = $dtv['used_work_time_day_shift'];
                $dtv['diff_between_daytype_and_calc']['wtdu1'] = $dtv['used_work_time_evening_shift_1'];
                $dtv['diff_between_daytype_and_calc']['wtdu2'] = $dtv['used_work_time_evening_shift_2'];
                $dtv['diff_between_daytype_and_calc']['wtej'] = $dtv['used_work_time_night_shift'];
        }
        return $dtv;
    }

    private function getDateTimeZoneHourDifference($workStart, $workEnd, $dateString, $dayPlusOneDayString)
    {
        $defaultDateTimeZone = self::DEFAULT_DATE_TIMEZONE;
        $dateTimeZone = $this->DATE_TIMEZONE;
        if ($defaultDateTimeZone == $dateTimeZone) {
            return 0;
        }

        $defaultTimeZoneStartValue = strtotime("$dateString $workStart $defaultDateTimeZone");
        $defaultTimeZoneEndValue = strtotime("$dayPlusOneDayString $workEnd $defaultDateTimeZone");
        $timeZoneStartValue = strtotime("$dateString $workStart $dateTimeZone");
        $timeZoneEndValue = strtotime("$dayPlusOneDayString $workEnd $dateTimeZone");

        $diff = ($timeZoneEndValue - $timeZoneStartValue) - ($defaultTimeZoneEndValue - $defaultTimeZoneStartValue);

        return $diff;
    }

    private function convertTimeStringToSeconds($timeString, $dateString = '1970-01-01', $timezone = 'UTC')
    {
        return strtotime("$dateString $timeString $timezone");
    }

    private function getCostIdFromRegs()
    {
        switch ($this->COST_MODE) {
            case 'ACTIVITY':
                unset($tmp);
                $emd = $this->employeesWithMainData->get($this->employeeContractId, $this->dayToProcess);
                $tmp[0] = $this->getRegCosts->getCostFromRegCostId(
                    $this->regRow['cost_id'],
                    $this->dayToProcess,
                    $emd['company_id'],
                    $emd['payroll_id']
                );
                $tmp[1] = $this->regRow['cost_id'];
                $costid = implode(';', $tmp);
                break;
            case 'COST':
            case 'COSTCENTER':
            default:
                $costid = $this->regRow['cost_id'];
                break;
        }
        return $costid;
    }

    private function getDefaultCostId()
    {
        switch ($this->COST_MODE) {
            case 'COSTCENTER':
                $costid = $this->employeeMainData['def_cost_center_id'];
                break;
            case 'ACTIVITY':
                unset($tmp);
                $tmp[0] = $this->employeeMainData['def_cost_id'];
                $tmp[1] = $this->employeeMainData['def_cost_center_id'];
                $costid = implode(';', $tmp);
                break;
            case 'COST':
            default:
                $costid = $this->employeeMainData['def_cost_id'];
                break;
        }
        return $costid;
    }

    private function setRegistrationSelectValues()
    {
        $seldtminSec = $seldtmaxSec = 0;

        $prew_day = date(AppConfigEnum::DATE_FORMAT, strtotime($this->dayToProcess . ' -1 day'));
        $next_day = date(AppConfigEnum::DATE_FORMAT, strtotime($this->dayToProcess . ' +1 day'));

        if (isset($this->dayTypeRows[$prew_day]['twstopldt'])) {
            $seldtMinSecFromPrewDay = $this->dayTypeRows[$prew_day]['twstopldt'] + $this->dayTypeRows[$prew_day]['used_tolerance_before'] + $this->dayTypeRows[$prew_day]['maxovertime_after'];
        } else {
            $seldtMinSecFromPrewDay = $this->dayTypeRows[$this->dayToProcess]['twstarteat'] - $this->dayTypeRows[$this->dayToProcess]['used_tolerance_before'] - $this->dayTypeRows[$this->dayToProcess]['maxovertime_before'];
        }
        $seldtMinSecFromToDay = $this->dayTypeRows[$this->dayToProcess]['twstarteat'] - $this->dayTypeRows[$this->dayToProcess]['used_tolerance_before'] - $this->dayTypeRows[$this->dayToProcess]['maxovertime_before'];
        $seldMaxSecFromToDay = $this->dayTypeRows[$this->dayToProcess]['twstopldt'] + $this->dayTypeRows[$this->dayToProcess]['used_tolerance_after'] + $this->dayTypeRows[$this->dayToProcess]['maxovertime_after'];
        if (isset($this->dayTypeRows[$next_day]['twstarteat'])) {
            $seldMaxSecFromNextDay = $this->dayTypeRows[$next_day]['twstarteat'] - $this->dayTypeRows[$next_day]['used_tolerance_before'] - $this->dayTypeRows[$next_day]['maxovertime_before'];
        } else {
            $seldMaxSecFromNextDay = $this->dayTypeRows[$this->dayToProcess]['twstopldt'] + $this->dayTypeRows[$this->dayToProcess]['used_tolerance_after'] + $this->dayTypeRows[$this->dayToProcess]['maxovertime_after'];
        }
        if ($this->CALCULATION_TIME_BETWEEN_DAY_TYPES === 1) {
            $seldtminSec = ($seldtMinSecFromToDay > $seldtMinSecFromPrewDay) ? $seldtMinSecFromToDay : $seldtMinSecFromPrewDay;
            $seldtmaxSec = ($seldMaxSecFromNextDay > $seldMaxSecFromToDay) ? $seldMaxSecFromNextDay : $seldMaxSecFromToDay;
        } else {
            $seldtminSec = $seldtMinSecFromToDay;
            $seldtmaxSec = $seldMaxSecFromToDay;
        }

        $seldtminSecOrigi = $seldtMinSecFromToDay;
        $seldtmaxSecOrigi = $seldMaxSecFromToDay;

        if ($this->dayTypeRows[$this->dayToProcess]['lunchtime'] > 0 and $this->CALCULATION_TIME_BETWEEN_DAY_TYPES !== 1) {
            $seldtmaxSec += $this->dayTypeRows[$this->dayToProcess]['lunchtime'];
            $seldtmaxSecOrigi += $this->dayTypeRows[$this->dayToProcess]['lunchtime'];
        }
        $this->calc[$this->dayToProcess]['seldt']['min'] = date(AppConfigEnum::DATE_TIME_FORMAT, $seldtminSec);
        $this->calc[$this->dayToProcess]['seldt']['max'] = date(AppConfigEnum::DATE_TIME_FORMAT, $seldtmaxSec);

        $this->calc[$this->dayToProcess]['seldt']['min_origi'] = date(
            AppConfigEnum::DATE_TIME_FORMAT,
            $seldtminSecOrigi
        );
        $this->calc[$this->dayToProcess]['seldt']['max_origi'] = date(
            AppConfigEnum::DATE_TIME_FORMAT,
            $seldtmaxSecOrigi
        );
    }

    private function setEmployeeStateTypeIdFirstRunFromPreviuosRegs(&$employeeState): bool
    {
        if ($this->isPreCalculated && !$this->calc[$this->yesterday][self::LOCKED]) {
            return false;
        }
        $preRegTime = '';
        $regRows = [];
        if (!$this->isUseExternalRegistrationSource()) {
            $regRows = $this->previousEmployeesRegistrationsProvider->provide(
                $this->employeeContractId,
                $this->dayToProcess
            );
        }
        if (is_array($regRows)) {
            $lastRegRow = end($regRows);
            $currRegRow = prev($regRows);
            if (is_array($lastRegRow)
                and is_array($currRegRow)
                and $lastRegRow['event_type_id'] === self::NMK_EVENT_TYPE_ID
                and $currRegRow['state_type_status'] > 0
                and abs(
                    strtotime($lastRegRow['time']) - strtotime($currRegRow['time'])
                ) < $this->RULE_REGS_FILTER_TERMINAL_BEFORE_OUT_TIME
                and $this->RULE_REGS_FILTER_TERMINAL_BEFORE_OUT_TIME > 0) {
                $this->calc[$this->dayToProcess]['preeventid'] = $lastRegRow['event_type_id'];
                $preRegTime = $lastRegRow['time'];
            } else {
                $this->calc[$this->dayToProcess]['preeventid'] = $currRegRow['event_type_id'] ?? '';
                $preRegTime = $currRegRow['time'] ?? '';
            }
            unset($lastRegRow);
            unset($currRegRow);
        } else {
            $this->calc[$this->dayToProcess]['preeventid'] = self::NMK_EVENT_TYPE_ID;
        }
        $employeeState = $this->stateMachine->getPreState(
            $this->calc[$this->dayToProcess]['preeventid'],
            $this->dayToProcess
        );
        $state_type_status = $this->stateType->getStateType(
            $this->dayToProcess,
            $employeeState,
            'state_type_status'
        );
        if ($employeeState <> $this->GONEHOME_STATE_TYPE_ID && ($state_type_status == 2 || $state_type_status == 1)) {
            $this->stateType->setCalculationState($employeeState, 2);    //Ma mar ezen a napon volt
            $this->stateType->setCalculationStart(
                $employeeState,
                strtotime($preRegTime . ' ' . self::DEFAULT_DATE_TIMEZONE)
            );
        }
        return true;
    }

    private function clearEmployeeStateWhenStateStatusIsDaily(&$employeeState): void
    {
        if (!isset($employeeState)) {
            $employeeState = $this->GONEHOME_STATE_TYPE_ID;
        } elseif ($this->stateType->getStateType($this->dayToProcess, $employeeState, 'state_type_status') == 0) {
            $employeeState = $this->GONEHOME_STATE_TYPE_ID;
            $this->stateType->setCalculationStart($employeeState, 0);
            $this->stateType->setCalculationState($employeeState, 0);
        }
    }

    private function calcAutoLock(): bool
    {
        //autolock esetén újra kell számolni amit az automata zárt le
        $ecr = $this->employeeCalcResults[$this->employeeContractId][$this->dayToProcess] ?? null;
        $reCalc = $this->optionCheck('AUTOLOCK')
            && isset($ecr[0]['created_by'])
            && $ecr[0]['created_by'] == $this->createdBy
            && (!isset($ecr[0]['modified_by']) || $ecr[0]['modified_by'] == $this->createdBy);
        if ($reCalc) {
            return false;
        }
        if (isset($ecr[0]) && $ecr[0]['status'] == Status::SAVED && $this->optionCheck('AUTOLOCK')) {
            $tables = ['employee_calc', 'employee_calc_used_daytype', 'employee_calc_message'];
            $set = '`status`=' . Status::LOCKED;
            $where = "`employee_contract_id`='{$this->employeeContractId}' AND `day` = '{$this->dayToProcess}'";

            foreach ($tables as $table) {
                dbExecute("UPDATE $table SET $set WHERE $where");
            }
        }
        $ews = $this->employeeWorkSchedule->get($this->employeeContractId, $this->dayToProcess);
        if (isset($ecr[0]['inside_type_id']) || isset($ews['employee_calc_daytype_id'])) {
            if (self::DEBUG_INFO_TO_APPLICATION_LOG === self::DEBUG_LEVEL_FULL) {
                $this->calc[$this->dayToProcess]['messageString'] =
                    "For the day {$this->dayToProcess}, the employee with contract number " .
                    "{$this->employeeContractId} has locked data!";
                $message = 'Log: Init TimeCardCalculation ' . $this->calc[$this->dayToProcess]['messageString'];
                Yang::log($message, Yang::LOGLEVEL_LOG, 'system.Calculation');
            }
            $this->calc[$this->dayToProcess][self::LOCKED] = true;
            return true;
        }
        return false;
    }

    private function setNexDayToProcess(): void
    {
        $this->yesterday = $this->dayToProcess;
        $this->dayToProcessTs = strtotime($this->dayToProcess . '+1 day');
        $this->dayToProcess = date(AppConfigEnum::DATE_FORMAT, $this->dayToProcessTs);
    }

    //----------------------------------------------------------------------------
    //------------- Calculation Modify Rules -------------------------------------
    //----------------------------------------------------------------------------
    private function ruleCalcModifyRegFilter($rowcount, $tmpRows): bool
    {
        if ($this->RuleRegsFilterException($rowcount, $tmpRows) === true) {
            return true;
        }
        if ($this->RuleRegsFilterTerminalReaderIsCalc($rowcount, $tmpRows) === true) {
            return true;
        }
        if ($this->RuleRegsFilterTerminalBeforeOutTime($rowcount, $tmpRows) === true) {
            return true;
        }
        if ($this->RuleRegsFilterTerminalAfterOutTime($rowcount, $tmpRows) === true) {
            return true;
        }
        if ($this->RuleRegsFilterMinTimeBetweenRegs($rowcount, $tmpRows) === true) {
            return true;
        }

        return false;
    }

    private function RuleRegsFilterException($rowcount, $tmpRows): bool
    {
        if (empty($this->RULE_REGS_FILTER_EXCEPTION)) {
            return false;
        }
        return in_array($tmpRows[$rowcount]['event_type_id'], explode(';', $this->RULE_REGS_FILTER_EXCEPTION), true);
    }

    private function RuleRegsFilterTerminalReaderIsCalc($rowcount, $tmpRows): bool
    {
        if (
            $this->RULE_REGS_FILTER_ONLY_FIRST_IN_LAST_OUT !== 'DISABLED' // DISABLED / ENABLEDALLTERMINAL / ENABLEWHERISCALCTRUEINTERMINAL
            and
            (
                $this->RuleRegsFilterOnlyFirstInLastOut($rowcount, $tmpRows) === true
                or
                (
                    $this->RULE_REGS_FILTER_ONLY_FIRST_IN_LAST_OUT === 'ENABLEDALLTERMINAL'
                    and
                    isset($tmpRows[$rowcount]['terminal_is_calc']) and (int)$tmpRows[$rowcount]['terminal_is_calc'] === 0

                )
            )
            and $this->RuleRegsFilterFirstInLastOutExeptDefCause($rowcount, $tmpRows)
        ) {
            return true;
        }
        return false;
    }

    private function RuleRegsFilterFirstInLastOutExeptDefCause($rowcount, $tmpRows): bool
    {
        if ($this->RULE_REGS_FILTER_ONLY_FIRST_IN_LAST_OUT_EXCEPT_EVENT_TYPE_ID[0] === 'DISABLED') {
            return true;
        }
        return array_search(
                $tmpRows[$rowcount]['event_type_id'],
                $this->RULE_REGS_FILTER_ONLY_FIRST_IN_LAST_OUT_EXCEPT_EVENT_TYPE_ID
            ) === false;
    }

    private function RuleRegsFilterOnlyFirstInLastOut($rowcount, $tmpRows)
    {
        if ($this->RULE_REGS_FILTER_ONLY_FIRST_IN_LAST_OUT !== 'DISABLED' and $rowcount > 0)//nem az első elem
        {
            $lastid = (sizeof($tmpRows) - 1);
            if ($rowcount < $lastid) {//Nem az utolsó elem
                return true;
            }
        }
        return false;
    }

    private function RuleRegsFilterTerminalBeforeOutTime($rowcount, $tmpRows)
    {
        if ($this->RULE_REGS_FILTER_TERMINAL_BEFORE_OUT_TIME > 0
            and $this->regRow['event_type_id'] == self::NMK_EVENT_TYPE_ID
            and $rowcount > 0
            and isset($tmpRows[($rowcount - 1)]['event_type_id'])
            and ($tmpRows[($rowcount - 1)]['event_type_id'] == 'KMK'
                or $tmpRows[($rowcount - 1)]['event_type_id'] == 'MGK'
                or $tmpRows[($rowcount - 1)]['event_type_id'] == 'EGK'
            )
        ) {
            if ((strtotime($this->regRow['reg_time']) - strtotime(
                        $tmpRows[($rowcount - 1)]['reg_time']
                    )) <= $this->RULE_REGS_FILTER_TERMINAL_BEFORE_OUT_TIME) {
                return true;
            }
        }
        return false;
    }

    private function RuleRegsFilterTerminalAfterOutTime($rowcount, $tmpRows)
    {
        if ($this->RULE_REGS_FILTER_TERMINAL_AFTER_OUT_TIME > 0
            and $this->regRow['event_type_id'] == self::NMK_EVENT_TYPE_ID
            and $rowcount > 0
            and isset($tmpRows[($rowcount + 1)]['event_type_id'])
            and ($tmpRows[($rowcount + 1)]['event_type_id'] == 'KMK'
                or $tmpRows[($rowcount + 1)]['event_type_id'] == 'MGK'
                or $tmpRows[($rowcount + 1)]['event_type_id'] == 'EGK'
            )
        ) {
            if ((strtotime($tmpRows[($rowcount + 1)]['reg_time']) - strtotime(
                        $this->regRow['reg_time']
                    )) <= $this->RULE_REGS_FILTER_TERMINAL_AFTER_OUT_TIME) {
                return true;
            }
        }
        return false;
    }

    private function RuleRegsFilterMinTimeBetweenRegs($rowcount, $tmpRows)
    {
        if ($this->RULE_REGS_FILTER_MIN_TIME_BETWEEN_REGS > 0) {
            $regTimeBetweenInRules = isset($tmpRows[($rowcount + 1)]['reg_time'])
                && (strtotime($tmpRows[($rowcount + 1)]['reg_time']) - strtotime(
                        $this->regRow['reg_time']
                    )) <= $this->RULE_REGS_FILTER_MIN_TIME_BETWEEN_REGS;
            $regEventTypeNoChange = isset($tmpRows[($rowcount + 1)]['event_type_id'])
                && $tmpRows[($rowcount + 1)]['event_type_id'] === $this->regRow['event_type_id'];

            if ($regTimeBetweenInRules && $regEventTypeNoChange) {
                return true;
            }
        }
        return false;
    }

    private function ruleCalcModifyEntryExitMoveInSeconds(): int
    {
        if ($this->ENTRY_MOVE_IN_SECONDS <> 0 and $this->regRow['event_type_id'] === self::NMB_EVENT_TYPE_ID) {
            return $this->ENTRY_MOVE_IN_SECONDS;
        } elseif ($this->EXIT_MOVE_IN_SECONDS <> 0 and $this->regRow['event_type_id'] === self::NMK_EVENT_TYPE_ID) {
            return ($this->EXIT_MOVE_IN_SECONDS * (-1));
        }
        return 0;
    }

    private function ruleCalcModifyRegTimeToEatOrLdt()
    {
        if ($this->regRow['reg_time_ts'] < $this->dayTypeRows[$this->dayToProcess]['twstarteat']) {
            return $this->dayTypeRows[$this->dayToProcess]['twstarteat'];
        } elseif ($this->regRow['reg_time_ts'] > $this->dayTypeRows[$this->dayToProcess]['twstopldt']) {
            return $this->dayTypeRows[$this->dayToProcess]['twstopldt'];
        }
        return $this->regRow['reg_time_ts'];
    }

    private function ruleCalcModifyRegTimeToWorkStartStop($tmpRows)
    {
        $regRow = $this->regRow;
        $dayTypeRows = $this->dayTypeRows[$this->dayToProcess];
        $calc = &$this->calc[$this->dayToProcess];

        reset($tmpRows);
        $lastRow = end($tmpRows);
        $tmpDate = new DateTime($lastRow['reg_time'], new DateTimeZone(self::DEFAULT_DATE_TIMEZONE));
        $lastRow['reg_time_ts'] = $tmpDate->format('U');
        reset($tmpRows);
        $firstRow = current($tmpRows);
        $tmpDate = new DateTime($firstRow['reg_time'], new DateTimeZone(self::DEFAULT_DATE_TIMEZONE));
        $firstRow['reg_time_ts'] = $tmpDate->format('U');

        $firstRegOutWithinTolerance =
            ($firstRow['reg_time_ts'] > ($dayTypeRows['used_work_start'] - $dayTypeRows['used_tolerance_before'])
                and $firstRow['reg_time_ts'] <= $dayTypeRows['used_work_start']
            );
        $lastRegOutWithinTolerance =
            ($lastRow['reg_time_ts'] < ($dayTypeRows['used_work_end'] + $dayTypeRows['used_tolerance_after'])
                and $lastRow['reg_time_ts'] >= $dayTypeRows['used_work_end']
            );
        $regInWithinTolerance =
            (
                $regRow['reg_time_ts'] > ($dayTypeRows['used_work_start'] - $dayTypeRows['used_tolerance_before'])
                and $regRow['reg_time_ts'] < $dayTypeRows['used_work_start']
            );
        $regOutWithinTolerance =
            (
                $regRow['reg_time_ts'] < ($dayTypeRows['used_work_end'] + $dayTypeRows['used_tolerance_after'])
                and $regRow['reg_time_ts'] >= $dayTypeRows['used_work_end']
            );

        if ($regInWithinTolerance
            && ($regRow['event_type_id'] === self::NMB_EVENT_TYPE_ID ||
                isset($calc['moveRegTimeToWorkStartStop']['start'])
            )
            && $firstRegOutWithinTolerance
        ) {
            $calc['moveRegTimeToWorkStartStop']['start'] = true;
            return $dayTypeRows['used_work_start'];
        }
        if ($regOutWithinTolerance
            // AND !isset($calc['moveRegTimeToWorkStartStop']['stop']) //TODO: Úgy tűnik ezt túlgondoltam, de lehet, hogy valamilyen esetre nem gondolok. 200818
            and $lastRegOutWithinTolerance
            and $regRow['event_type_id'] !== 'NMKLater'
        ) {
            // if($regRow["event_type_id"] === "NMK"){
            // 	$calc['moveRegTimeToWorkStartStop']['stop'] = \true;
            // }
            return $dayTypeRows['used_work_end'];
        }
        return $regRow['reg_time_ts'];
    }

    private function ruleCalcModifyEntyExitTolerance()
    {
        if ($this->dayTypeRows[$this->dayToProcess]['entryexit_tol'] > 0) {
            if (($this->regRow['reg_time_ts'] > $this->dayTypeRows[$this->dayToProcess]['used_work_start'])
                and ($this->regRow['reg_time_ts'] <
                    ($this->dayTypeRows[$this->dayToProcess]['used_work_start'] +
                        $this->dayTypeRows[$this->dayToProcess]['entryexit_tol']
                    )
                )
            ) {    // belépéseknél elfogad $entryexit_tol kesest
                return $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
            } elseif ($this->regRow['reg_time_ts'] < $this->dayTypeRows[$this->dayToProcess]['used_work_end']
                and ($this->regRow['reg_time_ts'] >
                    ($this->dayTypeRows[$this->dayToProcess]['used_work_end'] -
                        $this->dayTypeRows[$this->dayToProcess]['entryexit_tol']
                    )
                )
            ) {    // kilépéseknél elfogad $entryexit_tol kesest
                return $this->dayTypeRows[$this->dayToProcess]['used_work_end'];
            }
        } elseif ($this->dayTypeRows[$this->dayToProcess]['entryexit_tol'] < 0) {
            if (($this->regRow['reg_time_ts'] < $this->dayTypeRows[$this->dayToProcess]['used_work_start'])
                and ($this->regRow['reg_time_ts'] >
                    ($this->dayTypeRows[$this->dayToProcess]['used_work_start'] +
                        $this->dayTypeRows[$this->dayToProcess]['entryexit_tol']
                    )
                )
            ) {    // belépéseknél ha nem ért be a $entryexit_tol hamarabb akkor annyi kesest ad neki
                return ($this->dayTypeRows[$this->dayToProcess]['used_work_start'] - $this->dayTypeRows[$this->dayToProcess]['entryexit_tol']);
            } elseif ($this->regRow['reg_time_ts'] > $this->dayTypeRows[$this->dayToProcess]['used_work_end']
                and ($this->regRow['reg_time_ts'] <
                    ($this->dayTypeRows[$this->dayToProcess]['used_work_end'] -
                        $this->dayTypeRows[$this->dayToProcess]['entryexit_tol']
                    )
                )
            ) {    // kilépéseknél ha hamarabb megy el mint a $entryexit_tol akkor annyi kesest ad neki
                return ($this->dayTypeRows[$this->dayToProcess]['used_work_end'] + $this->dayTypeRows[$this->dayToProcess]['entryexit_tol']);
            }
        }
        return $this->regRow['reg_time_ts'];
    }

    private function ruleCalcModifyNightShiftCompensatoryTime($firstregofday, $costid)
    {
        if ($firstregofday
            and isset($this->NIGHT_SHIFT_COMPENSATORY_TIME) and $this->NIGHT_SHIFT_COMPENSATORY_TIME > 0
            and $this->dayTypeRows[$this->dayToProcess]['isNightShift']
            and $this->dayTypeRows[$this->dayToProcess]['used_work_start'] >= $this->EJ_START
            and $this->isAbsenceExists === false) {
            $state = (((integer)$this->employeeMainData['work_order']) === 1) ? 'wtde' : 'wtej';
            $this->setWorkTimetoCalc($costid, $state, 0, 0, $this->NIGHT_SHIFT_COMPENSATORY_TIME);
        }
    }

    private function ruleCalcModifyNightShiftDaytypeCompensatoryTime($used_work_start)
    {
        if (isset($this->NIGHT_SHIFT_COMPENSATORY_TIME) and $this->NIGHT_SHIFT_COMPENSATORY_TIME > 0
            and $used_work_start >= $this->EJ_START) {
            return $this->NIGHT_SHIFT_COMPENSATORY_TIME;
        }
        return 0;
    }

    private function ruleCalcModifyPaidLunchtimeAndWorkStart($employeestate)
    {
        if ($this->dayTypeRows[$this->dayToProcess]['paid_lunchtime_reduction'] === true
            and $employeestate == $this->GONEHOME_STATE_TYPE_ID
            and !$this->stateType->getCalculationStart($employeestate)
            and $this->regRow['reg_time_ts'] > $this->dayTypeRows[$this->dayToProcess]['used_work_start']
        ) {
            $maxDelay = $this->dayTypeRows[$this->dayToProcess]['paid_lunchtime'] + $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
            if ($maxDelay < $this->regRow['reg_time_ts']) {
                $this->regRow['reg_time_ts'] -= $this->dayTypeRows[$this->dayToProcess]['paid_lunchtime'];
                $this->dayTypeRows[$this->dayToProcess]['paid_lunchtime'] = 0;
            } else {
                $this->dayTypeRows[$this->dayToProcess]['paid_lunchtime'] = $this->regRow['reg_time_ts'] - $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
                $this->regRow['reg_time_ts'] = $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
            }
        }
        return;
    }

    private function increaseWorktimeByRules($totalWorkTime, &$workstop, &$work)
    {
        if (
            isset($this->dayTypeRows[$this->dayToProcess]['increase_worktime_value'])
            and $this->dayTypeRows[$this->dayToProcess]['increase_worktime_value'] > 0
            and isset($this->dayTypeRows[$this->dayToProcess]['increase_worktime_greater_than'])
            and $totalWorkTime >= $this->dayTypeRows[$this->dayToProcess]['increase_worktime_greater_than']
        ) {
            $workstop += $this->dayTypeRows[$this->dayToProcess]['increase_worktime_value'];
            $work += $this->dayTypeRows[$this->dayToProcess]['increase_worktime_value'];

            $this->dayTypeRows[$this->dayToProcess]['increase_worktime_value'] = 0; //csak egyszer novelje
        }
    }

    // ebédidő hozzáadása ha kevesebbet dolgozott mint...
    private function increaseToWorkByLunchtime($totalWorkTime, $workstop, &$towork)
    {
        //Yang::log($totalWorkTime, Yang::LOGLEVEL_LOG, 'sys.CALC');
        // TODO: Hibas mivel ha tobb kis szakaszbol all a napja, tobbszor megy ki-be akkor az elso szakasznal
        // 			hozzaadja, pedig az utolso szakasszal meg lesz a munkaideje, tehat nem kellene hozzaadni,
        //			persze ha meg nem adjuk hozza csak a vegen akkor meg tuo-ba mehet.
        //
        //	Nem tudom mikor van ennek ertelme....
        //
        //	Megoldas:
        //			$this->dayTypeRows[$this->dayToProcess]['used_work_end'] < $workstop ???
        if (
            isset($this->dayTypeRows[$this->dayToProcess]['skip_lunchtime_after_worked_less_than'])
            and $this->dayTypeRows[$this->dayToProcess]['skip_lunchtime_after_worked_less_than'] > 0
            and $this->dayTypeRows[$this->dayToProcess]['skip_lunchtime_after_worked_less_than'] >= $totalWorkTime
            and $this->dayTypeRows[$this->dayToProcess]['worktype'] !== 'FLEXIBLE'
            and $this->dayTypeRows[$this->dayToProcess]['used_work_end'] < $workstop
        ) {
            // 6 órásoknál kell pl, hogy az egy órás növelést ne túlórába adja oda...
            $towork += $this->dayTypeRows[$this->dayToProcess]['lunchtime'];
            $workstop += $this->dayTypeRows[$this->dayToProcess]['increase_worktime_value'];

            $this->dayTypeRows[$this->dayToProcess]['skip_lunchtime_after_worked_less_than'] = 0; //csak egyszer novelje
        }
    }

    // ebédidő hozzáadása ha kevesebbet dolgozott mint...
    private function increaseCalculatedCostCalcByLunchtime($totalWorkTime)
    {
        if (
            isset($this->dayTypeRows[$this->dayToProcess]['skip_lunchtime_after_worked_less_than'])
            and $this->dayTypeRows[$this->dayToProcess]['skip_lunchtime_after_worked_less_than'] > 0
            and $this->dayTypeRows[$this->dayToProcess]['skip_lunchtime_after_worked_less_than'] >= $totalWorkTime
            and (!$this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['done']
                or $this->employeeMainData['outside_breaktime'] === 'NONE')
        ) {
            $outSide = $this->dayTypeRows[$this->dayToProcess]['lunchtime_back_from_outside']['val'] ?? 0;
            $luncTime = $this->dayTypeRows[$this->dayToProcess]['lunchtime'];
            if (($luncTime - $outSide) <= 0) {
                return;
            }
            $back = $luncTime - $outSide;
            $this->writeLunchBackValueToCalculatedCostCalc($back);
        }
    }

    //TODO: Nem végleges megoldás. Csak a FLEX esetén csak 1 típusú probléma ad megoldást. DEV-10285
    private function overtimeAfterWorktimeFLEX($regTime)
    {
        //TODO: ha van elrendelt túlóra
        $ltdOvertimeTime = $this->dayTypeRows[$this->dayToProcess]['twstopldt'] + $this->dayTypeRows[$this->dayToProcess]['overtimeTime'];
        $overTimeType = $this->dayTypeRows[$this->dayToProcess]['overtimeType'] == 'AFTERWORK' ? 1 : 0;
        $overTimeTime = $this->dayTypeRows[$this->dayToProcess]['overtimeTime'] == 14400 ? 1 : 0;
        $workType = $this->dayTypeRows[$this->dayToProcess]['worktype'] == 'FRAMEWORK' ? 1 : 0;
        if ($this->OVERTIME_BEFORE_WORKTIME_FLEX
            && $regTime > $ltdOvertimeTime
            && $overTimeType
            && $overTimeTime
            && $workType) {
            $regTime = $this->dayTypeRows[$this->dayToProcess]['twstopldt'] + $this->dayTypeRows[$this->dayToProcess]['overtimeTime'];
        }

        return $regTime;
    }

//TODO: Nem végleges megoldás. Csak a FLEX esetén csak 1 típusú probléma ad megoldást. DEV-10285
    private function overtimeBeforeWorkTimeFLEX()
    {
        if ($this->OVERTIME_BEFORE_WORKTIME_FLEX && $this->USING_LOST_TIME_TYPE) {
            $breakTimeAfterWorkTime = $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime'] > 0 ? 1 : 0;
            $breakTimeAfterWorkTimeDuration = $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime_duration'] > 0 ? 1 : 0;
            $overTimeType = $this->dayTypeRows[$this->dayToProcess]['overtimeType'] == 'BEFOREWORK' ? 1 : 0;
            $overTimeTime = $this->dayTypeRows[$this->dayToProcess]['overtimeTime'] == 14400 ? 1 : 0;
            $workType = $this->dayTypeRows[$this->dayToProcess]['worktype'] == 'FRAMEWORK' ? 1 : 0;
            $notInsideDe = 0;

            if (isset($this->calc[$this->dayToProcess]['notInside']['de'])) {
                $notInsideDe = $this->calc[$this->dayToProcess]['notInside']['de'];
            } elseif (isset($this->calc[$this->dayToProcess]['notInside']['desun'])) {
                $notInsideDe = $this->calc[$this->dayToProcess]['notInside']['desun'];
            } elseif (isset($this->calc[$this->dayToProcess]['notInside']['du2'])) {
                $notInsideDe = $this->calc[$this->dayToProcess]['notInside']['du2'];
            } elseif (isset($this->calc[$this->dayToProcess]['notInside']['du2sun'])) {
                $notInsideDe = $this->calc[$this->dayToProcess]['notInside']['du2sun'];
            }

            if ($breakTimeAfterWorkTime
                && $breakTimeAfterWorkTimeDuration
                && $overTimeType
                && $overTimeTime
                && $workType
            ) {
                $breakTimeMax = $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime_duration'] - $notInsideDe;
                $breakTimeMax = $breakTimeMax < 0 ? 0 : $breakTimeMax;
                $this->dayTypeRows[$this->dayToProcess]['break_time_after_worktime_duration'] -= $breakTimeMax;
            }
        }
    }

    private function InsertNMBIfNMBPreviousExists(array $tmpRows): array
    {
        if (empty($tmpRows) || count($tmpRows) === 1) {
            return $tmpRows;
        }

        if (!in_array('NMBPrevious', array_column($tmpRows, 'event_type_id'), true)) {
            return $tmpRows;
        }

        foreach ($tmpRows as $position => $row) {
            if ($row['event_type_id'] !== 'NMBPrevious') {
                continue;
            }
            if (!isset($tmpRows[($position + 1)]['reg_time'])) {
                continue;
            }
            $workStart = $this->workScheduleIsWorkDay() ?
                $this->dayTypeRows[$this->dayToProcess]['used_work_start']
                : strtotime($row['reg_time']);

            $nextRegTime = $this->convertTimeStringToSeconds(
                substr($tmpRows[($position + 1)]['reg_time'], 0, 10),
                substr($tmpRows[($position + 1)]['reg_time'], -8, 8)
            );

            if ($workStart >= $nextRegTime) {
                if ($tmpRows[($position + 1)]['event_type_id'] === self::NMB_EVENT_TYPE_ID) {
                    $tmpRows[($position + 1)]['event_type_id'] = 'NMBPrevious';
                }
                continue;
            }
            $insert[1]['event_type_id'] = self::NMB_EVENT_TYPE_ID;
            $insert[1]['reg_time'] = $this->convertTimestampToString($workStart);
            $insert[1]['calc_status'] = 0;
            $insert[1]['from_work_start'] = 0;
            $insert[1]['to_work_end'] = 0;
            array_splice($tmpRows, ($position + 1), 0, $insert);
            break;
        }
        return $tmpRows;
    }

    private function InsertNMKIfNMKLaterExists(array $tmpRows): array
    {
        return $tmpRows;
    }

    private function ruleCalcModifyNoCause($rowCount, $regRowsCount)
    {
        if ($this->RULE_REGS_NO_CAUSE === 'ON_WITHOUTCOST'
            or ($this->RULE_REGS_NO_CAUSE_FLEXIBLE === 'ON_WITHOUTCOST'
                and $this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FLEXIBLE'
            )
        ) {
            $this->regRow['event_type_id'] = ($rowCount % 2) ? self::NMK_EVENT_TYPE_ID : self::NMB_EVENT_TYPE_ID;
        } elseif ($this->RULE_REGS_NO_CAUSE === 'ON_WITHCOST_WITHCOSTEND'
            or ($this->RULE_REGS_NO_CAUSE_FLEXIBLE === 'ON_WITHCOST_WITHCOSTEND'
                and $this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FLEXIBLE'
            )
        ) {
            $regRowsCount -= 1; // $rowCount base 0
            if ($rowCount === 0) {
                $this->regRow['event_type_id'] = self::NMB_EVENT_TYPE_ID;
            } elseif ($rowCount === $regRowsCount) {
                $this->regRow['event_type_id'] = self::NMK_EVENT_TYPE_ID;
            } else {
                $this->regRow['event_type_id'] = ($rowCount % 2) ? 'COSTEND' : 'COST';
                if ($this->regRow['event_type_id'] === 'COST' && $this->getRegCosts != null) {
                    $this->regRow['cost_id'] = $this->getRegCosts->getCostFromTerminalId(
                        $this->regRow['terminal_id'] . $this->regRow['reader_id'] ?? '',
                        $this->dayToProcess,
                        $this->employeeMainData['company_id'],
                        $this->employeeMainData['payroll_id']
                    );
                }
            }
        } elseif ($this->RULE_REGS_NO_CAUSE === 'ON_WITHCOST_WITHOUTCOSTEND'
            or ($this->RULE_REGS_NO_CAUSE_FLEXIBLE === 'ON_WITHCOST_WITHOUTCOSTEND'
                and $this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FLEXIBLE'
            )
        ) {
            $regRowsCount -= 1; // $rowCount base 0
            if ($rowCount === 0) {
                $this->regRow['event_type_id'] = self::NMB_EVENT_TYPE_ID;
            } elseif ($rowCount === $regRowsCount) {
                $this->regRow['event_type_id'] = self::NMK_EVENT_TYPE_ID;
            } else {
                $this->regRow['event_type_id'] = 'COST';
                $this->regRow['cost_id'] = $this->getRegCosts->getCostFromTerminalId(
                    $this->regRow['terminal_id'] . (is_null(
                        $this->regRow['reader_id']
                    ) ? '' : $this->regRow['reader_id']),
                    $this->dayToProcess,
                    $this->employeeMainData['company_id'],
                    $this->employeeMainData['payroll_id']
                );
            }
        }
        return;
    }
    //----------------------------------------------------------------------------
    //------------- Indication Rules ---------------------------------------------
    //----------------------------------------------------------------------------
    private function ruleIndicationMaxLate($dailyregscount)
    {
        if ($this->RULE_INDICATION['maxLate'] != 0 and $this->regRow['reg_time_ts'] >= $this->dayTypeRows[$this->dayToProcess]['used_work_start'] + $this->RULE_INDICATION['maxLate'] and $dailyregscount === 1) {
            $this->calc[$this->dayToProcess]['warning']['maxLate'] = true;
        }
    }

    private function ruleIndicationCostEndMissing($coststarted)
    {
        if ($this->RULE_INDICATION['costEndMissing']) {
            if (($coststarted['start'] == 1 && $coststarted['end'] == 0)
                || ($coststarted['start'] == 0 and $coststarted['end'] == 1)
            ) {
                $this->calc[$this->dayToProcess]['warning']['costEndMissing'] = true;
                if ($this->RULE_INDICATION['costEndMissing'] === 2) {
                    $this->calc[$this->dayToProcess][self::MARK] =
                        ErrorDefinitionEnum::CALC_STATUS_COST_END_MISSING[ErrorDefinitionKeyEnum::CODE];
                }
            }
        }
    }

    private function ruleIndicationCostChangeMissing()
    {
        if (!isset($this->calc[$this->dayToProcess][self::COST_CHANGE]) && $this->RULE_INDICATION['costChangeMissing']) {
            $this->calc[$this->dayToProcess]['warning']['costChangeMissing'] = true;
            if ($this->RULE_INDICATION['costChangeMissing'] === 2) {
                $this->calc[$this->dayToProcess][self::MARK] =
                    ErrorDefinitionEnum::CALC_STATUS_COST_CHANGE_MISSING[ErrorDefinitionKeyEnum::CODE];
            }
        }
    }

    private function ruleIndicationCameBack($employeestate)
    {
        if ($this->regRow['reg_time_ts'] >= $this->dayTypeRows[$this->dayToProcess]['twstopldt']
            and $employeestate === $this->GONEHOME_STATE_TYPE_ID and $this->CAME_BACK
        ) {
            $this->calc[$this->dayToProcess]['warning']['cameback'] = true;
        }
    }

    private function ruleIndicationFirstStateStart($firstRegOfDay, $stateTypeId, $newStateTypeId, $eventTypeId)
    {
        if ($this->RULE_INDICATION['MissingFirstStateStart'] > 0
            and $firstRegOfDay === 0
            and !$this->stateMachine->isStateStart($stateTypeId, $newStateTypeId, $eventTypeId)) {
            $this->calc[$this->dayToProcess]['warning']['MissingFirstStateStart'] = true;
        } elseif (isset($this->calc[$this->dayToProcess]['warning']['MissingFirstStateStart'])) {
            $this->calc[$this->dayToProcess]['warning']['MissingFirstStateStart'] = false;
        }
    }

    private function ruleIndicationOrdinaryTimeViolation($newemployeestate, $firstregofday)
    {
        if (isset($this->RULE_INDICATION['ordinaryTimeViolation'])
            && $this->RULE_INDICATION['ordinaryTimeViolation'] > 0
            &&
            (
                (
                    isset($this->calc[$this->dayToProcess]['warning']['ordinaryTimeViolation'])
                    && $this->calc[$this->dayToProcess]['warning']['ordinaryTimeViolation'] == false
                )
                ||
                !isset($this->calc[$this->dayToProcess]['warning']['ordinaryTimeViolation'])
            )
        ) {
            if ($this->stateType->getStateType(
                    $this->dayToProcess,
                    $newemployeestate,
                    'state_type_status'
                ) < 2) {  /*Nem egész napos távollét*/
                if (in_array(
                        $this->regRow['event_type_id'],
                        $this->ORDINARY_TIME_NOT_VIOLATION_EVENT_TYPE_ID
                    ) === false
                    and
                    (
                        $this->dayTypeRows[$this->dayToProcess]['used_ordinary_time_start'] <> false
                        or $this->dayTypeRows[$this->dayToProcess]['used_ordinary_time_end'] <> false
                    )
                ) {
                    if (!$this->stateType->getStateType(
                        $this->dayToProcess,
                        $newemployeestate,
                        GStateType::WORKTIME
                    )
                    ) {
                        if ((
                            $this->regRow['reg_time_ts'] >
                            $this->dayTypeRows[$this->dayToProcess]['used_ordinary_time_start']
                            && $this->regRow['reg_time_ts'] <
                            $this->dayTypeRows[$this->dayToProcess]['used_ordinary_time_end']
                        )
                        ) {
                            $this->calc[$this->dayToProcess]['warning']['ordinaryTimeViolation'] = true;
                            if ($this->RULE_INDICATION['ordinaryTimeViolation'] === 2) {
                                $this->calc[$this->dayToProcess][self::MARK] =
                                    ErrorDefinitionEnum::CALC_STATUS_ORDINARY_TIME_VIOLATION[ErrorDefinitionKeyEnum::CODE];
                            }
                        } else {
                            $this->calc[$this->dayToProcess]['warning']['ordinaryTimeViolation'] = false;
                        }//Egyéb lehetőségek????
                    } else {
                        if (
                            (int)$firstregofday === $this->regRow['reg_time_ts']
                            && $this->regRow['reg_time_ts'] >
                            $this->dayTypeRows[$this->dayToProcess]['used_ordinary_time_start']
                        ) {
                            $this->calc[$this->dayToProcess]['warning']['ordinaryTimeViolation'] = true;
                            if ($this->RULE_INDICATION['ordinaryTimeViolation'] === 2) {
                                $this->calc[$this->dayToProcess][self::MARK] =
                                    ErrorDefinitionEnum::CALC_STATUS_ORDINARY_TIME_VIOLATION[ErrorDefinitionKeyEnum::CODE];
                            }
                        } else {
                            $this->calc[$this->dayToProcess]['warning']['ordinaryTimeViolation'] = false;
                        }//Egyéb lehetőségek????
                    }
                }
            } else {
                $this->calc[$this->dayToProcess]['warning']['ordinaryTimeViolation'] = false;
            }
        }
    }

    private function ruleIndicationBreakTimeViolation()
    {
        if (isset($this->RULE_INDICATION['dailyBreakTimeViolation'])
            && $this->RULE_INDICATION['dailyBreakTimeViolation'] > 0
            &&
            (
                (
                    isset($this->calc[$this->dayToProcess]['warning']['dailyBreakTimeViolation'])
                    && $this->calc[$this->dayToProcess]['warning']['dailyBreakTimeViolation'] == false
                )
                ||
                !isset($this->calc[$this->dayToProcess]['warning']['dailyBreakTimeViolation'])
            )
            && isset($this->calc[$this->dayToProcess]['lunchtime']['real_lunch_time'])
            && $this->calc[$this->dayToProcess]['lunchtime']['real_lunch_time'] > $this->dayTypeRows[$this->dayToProcess]['lunchtime']

        ) {
            $this->calc[$this->dayToProcess]['warning']['dailyBreakTimeViolation'] = true;
            if ($this->RULE_INDICATION['dailyBreakTimeViolation'] === 2) {
                $this->calc[$this->dayToProcess][self::MARK] =
                    ErrorDefinitionEnum::CALC_STATUS_DAILY_BREAKTIME_VIOLATION[ErrorDefinitionKeyEnum::CODE];
            }
        }
    }

    private function ruleInicatorLatestDepartTimeViolation()
    {
        if (isset($this->RULE_INDICATION['latesDepartTimeViolation'])
            && $this->RULE_INDICATION['latesDepartTimeViolation'] > 0
            && $this->dayTypeRows[$this->dayToProcess]['worktype'] == 'FLEXIBLE'
            &&
            (
                (
                    isset($this->calc[$this->dayToProcess]['warning']['latesDepartTimeViolation'])
                    && $this->calc[$this->dayToProcess]['warning']['latesDepartTimeViolation'] == false
                )
                ||
                !isset($this->calc[$this->dayToProcess]['warning']['latesDepartTimeViolation'])
            )
            &&
            (
                isset($this->regRow['reg_time_orig_ts'])
                && isset($this->dayTypeRows[$this->dayToProcess]['twstopldt'])
                && $this->regRow['reg_time_orig_ts'] > $this->dayTypeRows[$this->dayToProcess]['twstopldt']
            )
        ) {
            $this->calc[$this->dayToProcess]['warning']['latesDepartTimeViolation'] = true;
            if ($this->RULE_INDICATION['latesDepartTimeViolation'] === 2) {
                $this->calc[$this->dayToProcess][self::MARK] =
                    ErrorDefinitionEnum::CALC_STATUS_LATEST_DEPARTMENT_TIME_VIOLATION[ErrorDefinitionKeyEnum::CODE];
            }
        }
    }

    private function ruleIndicatorAbsenceAndRegsError($firstregofday): void
    {
        if ($this->dayTypeRows[$this->dayToProcess]['worktype'] == 'ONLYWORKSCHEDULE'
            && $this->isAbsenceDraft === true
        ) {
            $this->calc[$this->dayToProcess][self::MARK] =
                ErrorDefinitionEnum::CALC_STATUS_ABSENCE_AND_REGS_ERROR[ErrorDefinitionKeyEnum::CODE];
            return;
        }

        if ($firstregofday
            && ($this->dayTypeRows[$this->dayToProcess]['worktype'] != 'INFORMAL' ||
                $this->INFORMAL_GOT_ABSENCE_AND_REGS_ERROR)
            && ($this->isAbsenceExists === true || $this->isAbsenceDraft === true)
            && (($this->ABSENCE_HOUR_MODE && $this->isAbsenceFullDay) || !$this->ABSENCE_HOUR_MODE)
        ) {
            $this->calc[$this->dayToProcess][self::MARK] =
                ErrorDefinitionEnum::CALC_STATUS_ABSENCE_AND_REGS_ERROR[ErrorDefinitionKeyEnum::CODE];
        }
    }

    private function ruleIndicationDifferenceDaytypeAndCalculation($output, $outputOrder)
    {
        if (isset($this->RULE_INDICATION['diffBetweenDaytypeAndCalc'])
            && $this->RULE_INDICATION['diffBetweenDaytypeAndCalc'] > 0) {
            foreach ($outputOrder as $id => $outputValues) {
                $state = $outputValues['state'];
                $costid = $outputValues['costid'];
                $values = $output[$costid][$state];

                $state = str_replace('otw', self::SIGN_WORKTIME, $outputValues['state']);
                if ($values['roundedvalue'] > 0
                    && isset($this->dayTypeRows[$this->dayToProcess]['diff_between_daytype_and_calc'][$state])
                    && $this->dayTypeRows[$this->dayToProcess]['diff_between_daytype_and_calc'][$state] > 0) {
                    $this->dayTypeRows[$this->dayToProcess]['diff_between_daytype_and_calc'][$state] -= $values['roundedvalue'];
                }
            }
            foreach ($this->dayTypeRows[$this->dayToProcess]['diff_between_daytype_and_calc'] as $key => $value) {
                if ($value != 0) {
                    $this->calc[$this->dayToProcess]['warning']['diffBetweenDaytypeAndCalc'] = true;
                    if ($this->RULE_INDICATION['diffBetweenDaytypeAndCalc'] === 2) {
                        $this->calc[$this->dayToProcess][self::MARK] =
                            ErrorDefinitionEnum::CALC_STATUS_DIFF_DAYTYPE_BETWEEN_CALC_VIOLATION[ErrorDefinitionKeyEnum::CODE];
                    }
                }
            }
        }
    }

    private function changeCalcTypeDependOnWorkOrder($insideType)
    {
        $insideTypePrefix = '';
        $insideTypePrefix .= (substr($insideType, -6) === 'nathol') ? 'nathol' : '';
        $insideTypePrefix .= (substr($insideType, -3) === 'sun') ? 'sun' : '';
        $insideTypePrefix .= (substr($insideType, -4) === 'wknd') ? 'wknd' : '';
        $insideTypePrefix .= (substr($insideType, -4) === 'prev') ? 'prev' : '';
        $insideTypePrefix .= (substr($insideType, -4) === 'next') ? 'next' : '';

        $allowance = ($this->dayTypeRows[$this->dayToProcess]['allowance'] === 0
            or $this->dayTypeRows[$this->dayToProcess]['worktype'] !== 'FLEXIBLE'
        );
        if ($this->dayTypeRows[$this->dayToProcess]['twworkorder'] <= 1
            and $allowance
        ) {
            return 'de' . $insideTypePrefix;
        }
        return $insideType;
    }

    // csak atvettuk, tisztitottuk Balint regi kodjat => refact
    private function CostCostEndRepair($regs)
    {
        $workstart = $this->dayTypeRows[$this->dayToProcess]['used_work_start'];
        $workstop = $this->dayTypeRows[$this->dayToProcess]['used_work_end'];
        $daytoprocess = $this->dayToProcess;

        $startDt = date(AppConfigEnum::DATE_TIME_FORMAT, $workstart);
        $stopDt = date(AppConfigEnum::DATE_TIME_FORMAT, $workstop);
        $count = count($regs);
        $cardNum = null;
        $itemFound = false;

        $costCostendFound = 0;

        for ($i = 0; $i < $count; $i++) {
            $regNo = $i;
            $prevRegNo = ($regNo - 1);
            $nextRegNo = ($regNo + 1);

            $regItem = $regs[$regNo];
            $prevRegItem = $regs[$prevRegNo] ?? false;
            $nextRegItem = $regs[$nextRegNo] ?? false;
//            $prevRegRegsDt = $prevRegItem ? strtotime($prevRegItem['reg_time']) : 0;
//            $nextRegRegsDt = $nextRegItem ? strtotime($nextRegItem['reg_time']) : 0;
//            $cardNum = $regItem['card'];

            // skip other eventtexts
            if (!in_array($regItem['event_type_id'], ['COST', 'COSTEND',])) {
                $itemFound = true;
                continue;
            }

            if (!$itemFound) {
                continue;
            }

            $itemToAdd = true;

            if ($regItem['event_type_id'] === 'COST') { // b.)
                $costCostendFound++;

                if ($nextRegItem === false || $nextRegItem['event_type_id'] !== 'COSTEND') {
                    $regItemToAdd = $regItem;
                    $regItemToAdd['event_type_id'] = 'COSTEND';
                    $regItemToAdd['eventverbal'] = 'COSTEND';
                    $regItemToAdd['modified_by'] = 'CostCostEndRepair';

                    // COST(actual item) - \COSTEND/ - COST(next item)
                    if ($nextRegItem !== false) { // next item exists
                        $genDtTime = strtotime($nextRegItem['reg_time'] . ' -1 minutes');
                        $genDt = date(AppConfigEnum::DATE_TIME_FORMAT, $genDtTime);
                        $currDtTime = strtotime($regItem['reg_time']);

                        // ha a beszúrandó előbbre kerülne, mint az aktuális
                        if ($genDtTime <= $currDtTime) {
                            $itemToAdd = false;
                        }

                        // ha van előző elem, és a előző elem nagyobb v egyenlő, mint az aktuális
                        if ($prevRegItem !== false && $currDtTime >= $genDtTime) {
                            $itemToAdd = false;
                        }

                        $regItemToAdd['reg_time'] = $genDt;
                    } else { // next item not exists => SKIP
                        $itemToAdd = false;
                    }

                    $costJSON = $regItem['cost_id'];
                    $costArr = (array)json_decode($costJSON);
                    $costArr['id'] = date(
                            'YmdHis',
                            strtotime($regItemToAdd['reg_time'])
                        ) . '_' . $regItemToAdd['card'] . '_' . 'CostCostEndRepair';
                    $costArr['id_different_operation_from_sheet'] = '0';
                    $costArr['manufactured_article_ready'] = '0';
                    $costArr['manufactured_article_count'] = '0';
                    $costArr['operation_ready'] = '0';
                    $costArr['reporting'] = 'tg';
                    $costArr['accounting'] = $costArr['accounting'] ?? '099';
                    $costJSON = json_encode($costArr);
                    $regItemToAdd['cost_id'] = $costJSON;
                    $costid = str_replace('"', '\"', $costJSON);

                    if ($itemToAdd) {
                        $regs = $this->addItemToArrayByIndex($regs, $nextRegNo, $regItemToAdd);

                        $count = count($regs);

                        $insertSQL = "
						INSERT INTO `registration`
							(`terminal_id`, `reader_id`, `card`, `time`, `event_type_id`, `cost_id`, `status`, `created_by`, `created_on`)
						VALUES
							('default','default', '" . $regItemToAdd['card'] . "', '" . $regItemToAdd['reg_time'] . "', '" . $regItemToAdd['event_type_id'] . "', '" . $costid . "', '" . Status::PUBLISHED . "', 'CostCostEndRepair', NOW() )
					";

                        dbExecute($insertSQL);
                        //$regs[$nextRegNo]["regsid"] = mysql_insert_id($conn);
                    }

                    // $msg  = '';
                    // $msg .= '1: ' . $regs[$nextRegNo]["regsid"];
                    // $msg .= '2: ' . $regs[$nextRegNo]["regsid"];
                }
            } else {
                if ($regItem['eventtext'] === 'COSTEND') { // a.)
                    $costCostendFound++;

                    if ($prevRegItem === false || $prevRegItem['eventtext'] !== 'COST') {
                        $regItemToAdd = $regItem;
                        $regItemToAdd['eventtext'] = 'COST';
                        $regItemToAdd['eventverbal'] = 'COST';
                        $regItemToAdd['modified'] = 'CostCostEndRepair';

                        $msg = '';
                        $msg .= 'LOG: ' . $regItem['regsid'] . ' ' . $prevRegItem['regsid'];

                        // COSTEND(prev item) - \COST/ - COSTEND(actual item)
                        if ($prevRegItem !== false) { // prev item exists
                            $genDtTime = strtotime($prevRegItem['reg_time'] . ' +1 minutes');
                            $genDt = date(AppConfigEnum::DATE_TIME_FORMAT, $genDtTime);

                            $currDtTime = strtotime($regItem['reg_time']);

                            // ha a beszúrandó későbbre kerülne, mint az aktuális
                            if ($genDtTime >= $currDtTime) {
                                $itemToAdd = false;
                            }

                            // ha van kövi elem, és a kövi elem kisseb v egyenlő, mint az aktuális
                            if ($nextRegItem !== false && $currDtTime <= $genDtTime) {
                                $itemToAdd = false;
                            }

                            $regItemToAdd['reg_time'] = $genDt;
                        } else {
                            $itemToAdd = false;
                        }

                        $costJSON = str_replace(['{', '}', ':', ',',],
                            ['{"', '"}', '":"', '","',],
                            $regItem['costid']);
                        $costArr = (array)json_decode($costJSON);
                        $costArr['id'] = date(
                                'YmdHis',
                                strtotime($regItemToAdd['reg_time'])
                            ) . '_' . $regItemToAdd['cardnum'] . '_' . 'CostCostEndRepair';
                        $costArr['id_different_operation_from_sheet'] = 0;
                        $costArr['manufactured_article_ready'] = 0;
                        $costArr['manufactured_article_count'] = 0;
                        $costArr['operation_ready'] = 0;
                        $costArr['reporting'] = 'tg';
                        $costArr['accounting'] = $costArr['accounting'] ?? '099';
                        $costJSON = json_encode($costArr);
                        $regItemToAdd['cost_id'] = $costJSON;
                        $costid = str_replace('"', '', $costJSON);


                        if ($itemToAdd) {
                            $regs = $this->addItemToArrayByIndex($regs, $regNo, $regItemToAdd);

                            $count = count($regs);

                            $insertSQL = "
						INSERT INTO `registration`
							(`terminal_id`, `reader_id`, `card`, `time`, `event_type_id`, `cost_id`, `status`, `created_by`, `created_on`)
						VALUES
							('default','default', '" . $regItemToAdd['card'] . "', '" . $costid . "', '" . $regItemToAdd['event_type_id'] . "', '" . $regItemToAdd['cost_id'] . "', '" . Status::PUBLISHED . "', 'CostCostEndRepair', NOW() )
					";

                            dbExecute($insertSQL);
                        }

                        // $msg  = '';
                        // $msg .= '1: ' . $regs[$nextRegNo]["regsid"];

                        // $regs[$nextRegNo]["regsid"] = mysql_insert_id($conn);
                        // $msg .= '2: ' . $regs[$nextRegNo]["regsid"];

                    }
                }
            }
        }

        if (!$costCostendFound) {
            $fullName = $this->employeeMainData['fullname'];
            $msg = 'Hiányzó COST és COSTEND. Név: ' . $fullName . ' - Dátum: ' . $daytoprocess;
        }

        return $regs;
    }

    private function setMessages(&$id)
    {
        if (!$this->optionCheck('MESSAGES')) {
            return;
        }
        $dayToProcess = $this->startDate;
        $dayToProcessTs = strtotime($dayToProcess);
        $decadeStopTs = strtotime($this->endDate);
        $errorMessage = '';

        while ($dayToProcessTs <= $decadeStopTs) {
            if (isset($this->calc[$dayToProcess]['states']['workisalldayshort'])) {
                $id++;
                $dayToProcessTs = strtotime($dayToProcess . '+1 day');
                $dayToProcess = date(AppConfigEnum::DATE_FORMAT, $dayToProcessTs);
                continue;
            }
            $employeeData = $this->employeesWithMainData->get($this->employeeContractId, $dayToProcess);
            $fullNameWithEmpId = $employeeData['fullNameWithEmpId'] . ' - ' . $employeeData['emp_id'];
            $wg_name = $employeeData['workgroup_name'];

            if ($this->calc[$dayToProcess][self::MARK] === (-1) || !isset($this->calc[$dayToProcess])) {
                $errorMessage .= "$id - Hiba a számolásban.";
            } elseif ($this->calc[$dayToProcess][self::LOCKED]) {
                $errorMessage .= "$id - Manuálisan elfogadott nap.";
            } elseif (!isset($this->calc[$dayToProcess][self::COST_CHANGE])) {
                $errorMessage .= "$id - Hiba hiányzó költséghely váltás.";
            } else {
                $errorMessage .= "$id - Sikeres mentés.";
            }
            if (!empty($errorMessage)) {
                $errorMessage .= " Dolgozó: $fullNameWithEmpId 
                    Munkacsoport: $wg_name Nap: " . $dayToProcess . '<BR>' . "\n";
            }
            $id++;
            $dayToProcessTs = strtotime($dayToProcess . '+1 day');
            $dayToProcess = date(AppConfigEnum::DATE_FORMAT, $dayToProcessTs);
        }
        $this->messages .= $errorMessage;
    }

    // csak atvettuk, tisztitottuk Balint regi kodjat => refact
    private function addItemToArrayByIndex($arr = [], $ind = 0, $val = '')
    {
        $tmpArr = $arr;
        $indFound = false;

        for ($i = 0; $i < count($arr); $i++) {
            if ($i === (int)$ind) {
                $indFound = true;
            }

            if (!$indFound) {
                $tmpArr[$i] = $arr[$i];
            } else {
                $tmpArr[$i + 1] = $arr[$i];
            }
        }

        $tmpArr[(int)$ind] = $val;
        $arr = $tmpArr;

        return $arr;
    }

    private function initVariable(&$var)
    {
        if (!isset($var)) {
            $var = 0;
        }
    }

    /*
     * Ha még nincs használatban a ($var) változó, akkor ($value) értéket ad,
     *		ha már kapott előzőleg értéket, akkor hozzáadja azt.
     *
     * A $var változó kapja meg az értéket.
     */
    private function InitAndAddValueToVariable(&$var, $value)
    {
        isset($var) ? $var += $value : $var = $value;
    }

    /**
     * @param int $workStart
     * @param int $workStop
     * @return IntervalIntersectionDescriptor[]
     */
    private function getOvertimeStandByOverlap(int $workStart, int $workStop): array
    {
        $nonCommonIntervalIntersectionDescriptor = new IntervalIntersectionDescriptor();
        $nonCommonIntervalIntersectionDescriptor->setFirstIntervalBeforeIntersectionNonCommonInterval(
            new FromToDescriptor(
                (new DateTime())->setTimestamp($workStart),
                (new DateTime())->setTimestamp($workStop)
            )
        );
        $flexibleNotUsed = (
            $this->FLEXIBLE_USE_STANDBY_IN_OVERTIME_CALC === false
            && $this->dayTypeRows[$this->dayToProcess]['worktype'] === 'FLEXIBLE'
        );
        if ($flexibleNotUsed) {
            return [$nonCommonIntervalIntersectionDescriptor];
        }

        $day = DateTime::createFromFormat(AppConfigEnum::DATE_FORMAT, $this->dayToProcess);
        $day->sub(new DateInterval('P1D'));
        $standbyDescriptors = $this->employeeExtraHours->getStandByDescriptorsForDays(
            $this->employeeContractId,
            $day,
            3
        );
        $from = (new DateTime())->setTimestamp($workStart);
        $to = (new DateTime())->setTimestamp($workStop);
        $intersectionDescriptors = [];
        $intersectionCount = 0;
        foreach ($standbyDescriptors as $standbyDescriptor) {
            $intersectionDescriptor = $this->intervalIntersectionCalculator
                ->setFrom($from)
                ->setTo($to)
                ->setFromCompare($standbyDescriptor->getStart())
                ->setToCompare($standbyDescriptor->getEnd())
                ->calculator();
            if ($intersectionDescriptor->hasIntersection()) {
                $intersectionDescriptor->getIntersection()->setSign($standbyDescriptor->getSign());
                if ($intersectionDescriptor->hasFirstIntervalAfterIntersectionNonCommonInterval()) {
                    if ($intersectionCount) {
                        $intersectionDescriptors[$intersectionCount - 1]
                            ->clearFirstIntervalAfterIntersectionNonCommonInterval();
                    }
                    $from = $intersectionDescriptor->getFirstIntervalAfterIntersectionNonCommonInterval()->getFrom();
                    $to = $intersectionDescriptor->getFirstIntervalAfterIntersectionNonCommonInterval()->getTo();
                }
                $intersectionDescriptors[$intersectionCount++] = $intersectionDescriptor;
            }
        }
        if (!empty($intersectionDescriptors)) {
            return $intersectionDescriptors;
        }
        return [$nonCommonIntervalIntersectionDescriptor];
    }

    private function SetOvertimeStandByOverlap($costid, $work, $workstart, $workstop)
    {
        $standby = $this->oldEmployeeExtraHours->getPlusMinusOneDay(
            $this->employeeContractId,
            $this->dayToProcess,
            'standby'
        );
        if (!isset($standby) || $this->FLEXIBLE_USE_STANDBY_IN_OVERTIME_CALC === false) {
            return [0 => ['workstart' => $workstart, 'workstop' => $workstop, 'work' => $work]];
        }
        foreach ($standby as $key => $standByRow) {
            if (is_null($standByRow['standby_start']) || is_null($standByRow['standby_end'])) {
                continue;
            }

            $standByRow['standby_start_ts'] = $this->convertTimeStringToSeconds(
                $standByRow['standby_start'],
                $standByRow['date']
            );
            $standByRow['standby_end_ts'] = $this->convertTimeStringToSeconds(
                $standByRow['standby_end'],
                $standByRow['date']
            );
            if ($standByRow['standby_start_ts'] === $standByRow['standby_end_ts'] && !$this->STANDBY_MODE) {
                continue;
            } elseif ($standByRow['standby_start_ts'] > $standByRow['standby_end_ts'] || ($standByRow['standby_start_ts'] === $standByRow['standby_end_ts'] && $this->STANDBY_MODE)) {
                $dayPlusOneDayString = date(AppConfigEnum::DATE_FORMAT, strtotime($standByRow['date'] . '+1 day'));
                $standByRow['standby_end_ts'] = $this->convertTimeStringToSeconds(
                    $standByRow['standby_end'],
                    $dayPlusOneDayString
                );
            }

            $overlap = $this->GetIntervalOverlap(
                $workstart,
                $workstop,
                $standByRow['standby_start_ts'],
                $standByRow['standby_end_ts']
            );
            if ($overlap !== false) {
                continue;
            }
            $this->setWorktimeOvertime($costid, $overlap['value'], $overlap['from'], $overlap['to']);
            if ($overlap['from'] > $workstart) {
                $ret[] = [
                    'workstart' => $workstart,
                    'workstop' => $overlap['from'],
                    'work' => ($overlap['from'] - $workstart)
                ];

                if ($overlap['to'] < $workstop) {
                    $ret[] = [
                        'workstart' => $overlap['to'],
                        'workstop' => $workstop,
                        'work' => ($workstop - $overlap['to'])
                    ];
                }
            } elseif ($overlap['to'] < $workstop) {
                $ret[] = [
                    'workstart' => $overlap['to'],
                    'workstop' => $workstop,
                    'work' => ($workstop - $overlap['to'])
                ];
            } else {
                $ret[] = ['workstart' => 0, 'workstop' => 0, 'work' => 0];
            }
        }
        return (isset($ret)) ? $ret : [0 => ['workstart' => $workstart, 'workstop' => $workstop, 'work' => $work]];
    }

    private function GetIntervalOverlap($from, $to, $fromCompare, $toCompare)
    {
        $intersect = min($to, $toCompare) - max($from, $fromCompare);
        if ($intersect > 0) {
            return [
                'from' => max($from, $fromCompare),
                'to' => min($to, $toCompare),
                'value' => $intersect
            ];
        }
        return false;
    }

    private function setStandbyDaytypeBreakTimeOverlap($costid, &$output, &$outputOrder): void
    {
        if (!$this->STANDBY_DAYTYPE_BREAKTIMES_OVERLAP) {
            return;
        }

        $standby = $this->oldEmployeeExtraHours->getPlusMinusOneDay(
            $this->employeeContractId,
            $this->dayToProcess,
            'standby'
        );
        if (!isset($standby)) {
            return;
        }

        $breakTimes = $this->dayTypeBreaksTime[$this->dayToProcess];
        if (!is_array($breakTimes)) {
            return;
        }

        $overlapped = 0;
        foreach ($breakTimes as $key => $value) {
            if ($value['break_time_from'] == 0 || $value['break_time'] == 0) {
                continue;
            }
            $workstart = $value['break_time_from'];
            $workstop = $value['break_time_from'] + $value['break_time'];
            foreach ($standby as $standByRow) {
                if (is_null($standByRow['standby_start']) || is_null($standByRow['standby_end'])) {
                    continue;
                }

                $standByRow['standby_start_ts'] = $this->convertTimeStringToSeconds(
                    $standByRow['standby_start'],
                    $standByRow['date']
                );
                $standByRow['standby_end_ts'] = $this->convertTimeStringToSeconds(
                    $standByRow['standby_end'],
                    $standByRow['date']
                );

                if ($standByRow['standby_start_ts'] === $standByRow['standby_end_ts'] && !$this->STANDBY_MODE) {
                    continue;
                } elseif ($standByRow['standby_start_ts'] > $standByRow['standby_end_ts'] || ($standByRow['standby_start_ts'] === $standByRow['standby_end_ts'] && $this->STANDBY_MODE)) {
                    $dayPlusOneDayString = date(AppConfigEnum::DATE_FORMAT, strtotime($standByRow['date'] . '+1 day'));
                    $standByRow['standby_end_ts'] = $this->convertTimeStringToSeconds(
                        $standByRow['standby_end'],
                        $dayPlusOneDayString
                    );
                }

                $overlap = $this->GetIntervalOverlap(
                    $workstart,
                    $workstop,
                    $standByRow['standby_start_ts'],
                    $standByRow['standby_end_ts']
                );
                if ($overlap == false) {
                    continue;
                }

                $overlapped += $overlap['value'];
                $workstart = $overlap['to'];
            }
        }
        if ($overlapped > 0) {
            $insideType = 'standbydaytypebreaktimeoverlap';
            $output[$costid][$insideType]['roundedvalue'] = $overlapped;
            $output[$costid][$insideType]['value'] = $overlapped;

            $tempOutputOrder['state'] = $insideType;
            $tempOutputOrder['costid'] = $costid;

            $outputOrder[] = $tempOutputOrder;
        }
    }

    private function shiftArraySum(array $sum, array $shift): array
    {
        $notFound = true;
        foreach ($shift as $key => $subShift) {
            foreach ($sum as $sum_key => $sumValue) {
                if ($subShift['insideType'] == $sumValue['insideType']) {
                    $sum[$sum_key]['newStateValue'] += $subShift['value'];
                    $notFound = false;
                    break;
                }
            }
            if ($notFound) {
                array_push($sum, $subShift);
            }
        }
        return $sum;
    }

    private function shiftArraySub(array $base, array $subArray): array
    {
        foreach ($base as $key => $baseValue) {
            foreach ($subArray as $sub_key => $subValue) {
                if ($baseValue['insideType'] == $subValue['insideType']) {
                    $base[$key]['value'] -= $subValue['value'];
                    break;
                }
            }
        }
        return $base;
    }

    private function optionCheck($option): bool
    {
        return in_array($option, $this->payrollCalcOption);
    }

    private function change_array_key($array, $old_key, $new_key)
    {
        if (!is_array($array)) {
            return $array;
        }
        if (!array_key_exists($old_key, $array)) {
            return $array;
        }
        if (strlen($new_key) === 0) {
            return $array;
        }

        $key_pos = array_search($old_key, array_keys($array));
        $arr_before = array_slice($array, 0, $key_pos);
        $arr_after = array_slice($array, $key_pos + 1);
        $arr_renamed = [$new_key => $array[$old_key]];

        return $arr_before + $arr_renamed + $arr_after;
    }

    private function strToArray($valueToArray, $identificationToErrorMesssage)
    {
        $str = trim($valueToArray);
        $str = trim($str, '[]');
        $ret = explode(',', $str);
        if ($ret === false) {
            $message = "Error: Can not convert to array: $identificationToErrorMesssage = $valueToArray";
            Yang::log($message, Yang::LOGLEVEL_ERROR, 'system.Calculation');
        }
        return $ret;
    }

    private function employeeMainDataCheck(?array $employeeMainData): bool
    {
        if (empty($employeeMainData)) {
            $errorDescriptor = new ErrorDefinitionsDescriptor(
                ErrorDefinitionEnum::EMPLOYEE_NOT_FOUND,
                ['day' => $this->dayToProcess, 'employeeContractId' => $this->employeeContractId]
            );
            if (self::DEBUG_INFO_TO_APPLICATION_LOG === self::DEBUG_LEVEL_FULL) {
                $message = 'Log: Init TimeCardCalculation ' . $errorDescriptor->formatMessage();
                Yang::log($message, Yang::LOGLEVEL_LOG, 'system.Calculation');
            }
            $this->calc[$this->dayToProcess][self::CALC_ERROR] = $errorDescriptor;
            $this->errorDescriptors[] = $errorDescriptor;
            return false;
        }

        if (is_null($employeeMainData['workgroup_row_id'])) {
            $errorDescriptor = new ErrorDefinitionsDescriptor(
                ErrorDefinitionEnum::WORKGROUP_NOT_FOUND,
                [
                    'day' => $this->dayToProcess,
                    'name' => $employeeMainData['fullname'],
                    'employee id' => $employeeMainData['emp_id'],
                    'employeeContractId' => $this->employeeContractId
                ]
            );
            if (self::DEBUG_INFO_TO_APPLICATION_LOG === self::DEBUG_LEVEL_FULL) {
                $message = 'Log: Init TimeCardCalculation ' . $errorDescriptor->formatMessage();
                Yang::log($message, Yang::LOGLEVEL_LOG, 'system.Calculation');
            }
            $this->calc[$this->dayToProcess][self::CALC_ERROR] = $errorDescriptor;
            $this->errorDescriptors[] = $errorDescriptor;
            return false;
        }

        if (is_null(
            $this->employeeWorkSchedule->get($this->employeeContractId, $this->dayToProcess)['used_daytype_id']
        )) {
            $errorDescriptor = new ErrorDefinitionsDescriptor(
                ErrorDefinitionEnum::WORK_SCHEDULE_NOT_FOUND,
                [
                    'day' => $this->dayToProcess,
                    'name' => $employeeMainData['fullname'],
                    'employee id' => $employeeMainData['emp_id'],
                    'employeeContractId' => $this->employeeContractId
                ]
            );
            if (self::DEBUG_INFO_TO_APPLICATION_LOG === self::DEBUG_LEVEL_FULL) {
                $message = 'Log: Init TimeCardCalculation ' . $errorDescriptor->formatMessage();
                Yang::log($message, Yang::LOGLEVEL_LOG, 'system.Calculation');
            }
            $this->calc[$this->dayToProcess][self::CALC_ERROR] = $errorDescriptor;
            $this->errorDescriptors[] = $errorDescriptor;
            return false;
        }
        if (!$this->useExternalRegistrationSource) {
            $card = $this->employeeCards->getOneEmployeeContractValidCard(
                $this->employeeContractId,
                $this->dayToProcess
            );
            if (!$card) {
                $errorDescriptor = new ErrorDefinitionsDescriptor(
                    ErrorDefinitionEnum::CARD_NOT_FOUND,
                    [
                        'day' => $this->dayToProcess,
                        'name' => $employeeMainData['fullname'],
                        'employee id' => $employeeMainData['emp_id'],
                        'employeeContractId' => $this->employeeContractId
                    ]
                );
                if (self::DEBUG_INFO_TO_APPLICATION_LOG === self::DEBUG_LEVEL_FULL) {
                    $message = 'Log: Init TimeCardCalculation ' . $errorDescriptor->formatMessage();
                    Yang::log($message, Yang::LOGLEVEL_LOG, 'system.Calculation');
                }
                $this->calc[$this->dayToProcess][self::CALC_ERROR] = $errorDescriptor;
                $this->errorDescriptors[] = $errorDescriptor;
                return false;
            }
        }
        return true;
    }

    private function containsStandByInInsideType(string $insideType): bool
    {
        //It could be filtered by validity as well, but it’s not necessary
        foreach ($this->standbyTypes as $standByType) {
            if (substr_count($insideType, $standByType->sign) > 0) {
                return true;
            }
        }
        return false;
    }

    private function eachLegacy(&$array)
    {
        if (current($array) !== false) {
            $return = [
                1 => current($array),
                'value' => current($array),
                0 => key($array),
                'key' => key($array)
            ]; // Get the current values
            next($array);
            return $return;
        }
        return false;
    }

    private function getAppSettings()
    {
        $this->WfmDateTimeFormat =
            (App::getSetting('wfm_used_seconds') === '0') ?
                AppConfigEnum::DATE_TIME_FORMAT_WITHOUT_SEC :
                AppConfigEnum::DATE_TIME_FORMAT;
        $this->DATE_TIMEZONE =
            !is_null(App::getSetting('calc_datetimezone')) ?
                App::getSetting('calc_datetimezone') :
                self::DEFAULT_DATE_TIMEZONE;

        $this->PAID_PUBLIC_HOLIDAY_TYPES =
            !is_null(App::getSetting('paidPublicHolidayTypes')) ?
                $this->strToArray(App::getSetting('paidPublicHolidayTypes'), 'paidPublicHolidayTypes') :
                [1, 2, 3, 4];

        $this->UNDISCLOSED_STATE_TYPE_ID = 'undisclosed';
        $this->GONEHOME_STATE_TYPE_ID = App::getSetting('goneHome_stateTypeId');
        $this->WORK_STATE_TYPE_ID = '51fde0fb62e4b83f25a5255b345c0ffc';
        $this->BREAKTIME_STATE_TYPE_ID = App::getSetting('summarySheet_breakTime_stateTypeId');

        //getProgSetup("entryMoveInSeconds");	//Belépés esetén levont másodpercek (kapu messze van)
        $this->ENTRY_MOVE_IN_SECONDS = (int)App::getSetting('summarySheet_entryMoveInSeconds');
        //getProgSetup("exitMoveInSeconds");	//Kilépés esetén levont másodpercek (kapu messze van)
        $this->EXIT_MOVE_IN_SECONDS = (int)App::getSetting('summarySheet_exitMoveInSeconds');
        //getProgSetup("CALCULATION_TIME_BETWEEN_DAYTYPES");
        //Munkanap a következő munkanap kezdetéig keressen regisztrációkat.
        //Es hétvégén az előző munkanap végétől keressen regisztrációkat.
        $this->CALCULATION_TIME_BETWEEN_DAY_TYPES = (int)App::getSetting('summarySheet_calculationTimeBetweenDaytypes');

        //getProgSetup("COSTMODE"); //NONE, COST, COSTCENTER, ACTIVITY
        $this->COST_MODE = App::getSetting('summarySheet_costMode');
        //A costEnd eseményben megadott érték kerüljön a kiszámolt érték költésghelyére.
        // (Normál esetben a cost eseményben megadott kerül be)
        $this->COST_END_CHANGED = ((int)App::getSetting('summarySheet_costEndChangToCalc') === 1);
        //getProgSetup("overtimeafterworkime");	//Csak munkaidő után van túlóra
        $this->OVERTIME_AFTER_WORKTIME = ((int)App::getSetting('summarySheet_overtimeAfterWorktime') === 1);
        //getProgSetup("minovertime_beforework"); //Ennyi munkarend előtti túlóre esetén mégis lesz túlóra.
        $this->MIN_OVERTIME_BEFORE_WORK = (int)App::getSetting('summarySheet_minOvertimeBeforeWork');

        $this->OVERTIME_MODE = empty(App::getSetting('overtime_mode')) ?
            'none' :
            mb_strtolower(App::getSetting('overtime_mode'), 'UTF-8');
        $this->OT_MGMT_REST_DAY_USE_INTERVAL = ((int)App::getSetting('otMgmtRestdayUseInterval') === 1);
        $this->STANDBY_MODE = ((int)App::getSetting('standbyUpgradedVersion') === 1);

        $this->APP_SETTINGS_CALC_DAILY_WORKTIME_PLACE =
            empty(App::getSetting('calc_dailyworktime_place')) ?
                'DAYTYPE' :
                mb_strtoupper(App::getSetting('calc_dailyworktime_place'), 'UTF-8');
        $this->FLEXIBLE_USE_STANDBY_IN_OVERTIME_CALC =
            !empty(App::getSetting('summarySheet_flexible_use_standby_in_overtime_calc'))
            && mb_strtolower(
                App::getSetting('summarySheet_flexible_use_standby_in_overtime_calc'),
                'UTF-8'
            ) === 'enable';
        //Naptípusban a tűrést ketté bontja előtte és utána darabokra.
        $this->SPLIT_DAYTYPE_TOLERANCE = ((int)App::getSetting('splitDaytypeTolerance') === 1);
        //Munkacsoportban ha láthatóak a kerekítés típusok, akkor használja is őket a számolásban.
        // https://innote.login.hu/n-7da20qta
        $this->WORKGROUP_SHOW_ROUND_TYPES = ((int)App::getSetting('workgroup_showRoundTypes') === 1);
        //Készelét alatti túlórák idők st jelzéset kapnap pl.: wtstdesun
        $this->STANDBY_SIGN = ((int)App::getSetting('summarySheet_calculation_standby_sign') === 1);
        //Munkacsoportonkénti műszakkezdések
        $this->SHIFT_START_IN_WORKGROUP =
            ((int)App::getSetting('summarySheet_calculation_shiftStartInWorkgroup') === 1);
        //Naptípusonkénti műszakkezdések
        $this->SHIFT_START_IN_DAYTYPE = ((int)App::getSetting('summarySheet_calculation_shiftStartInDaytype') === 1);
        //Kieső idők használata a számolásban
        $this->USING_LOST_TIME_TYPE = ((int)App::getSetting('summarySheet_calculation_usingLostTimeType') === 1);
        $this->USING_LOST_TIME_TYPE_IN_REST_DAY = false;
        //Kintlétek mentése az employee_calc táblába
        $this->SAVING_UNDISCLOSED_TO_EMPLOYEE_CALC = ((int)App::getSetting(
                'summarySheet_calculation_savingUndisclosedToEmployeeCalc'
            ) === 1);
        $this->SAVING_OTHER_STATES = ((int)App::getSetting('summarySheet_calculation_savingOtherStates') === 1);

        //szaldó jóváhagyása automatikusan, azaz minden szaldó status PUBLUSHED
        $this->BOT_AUTOMATIC_ACCEPT = ((int)App::getSetting('summarySheet_calculation_bot_automaticaly_accept') === 1);
        //szaldó jóváhagyása az elrendelt túlórával, és marad szaldó
        $this->BOT_ACCEPT_BY_OT_MANAGEMENT = ((int)App::getSetting(
                'summarySheet_calculation_bot_accept_by_overtime_manager'
            ) === 1);
        //szaldó jóváhagyása az elrendelt túlórával, és túlórára lesz
        $this->BOT_ACCEPT_BY_OT_MANAGEMENT_TO_OT = ((int)App::getSetting(
                'summarySheet_calculation_bot_accept_by_overtime_manager_to_ovetime'
            ) === 1);
        //készenlét alatti szaldő jóváhagyása és túlórává alakítása
        $this->STANDBY_BOT_ACCEPT_AND_CHANGE_TO_OT = ((int)App::getSetting(
                'summarySheet_calculation_standby_bot_accept_and_change_to_ovetime'
            ) === 1);

        $this->RULE_INDICATION['costChangeMissing'] = (int)App::getSetting(
            'summarySheet_ruleIndicator_costChangeMissing'
        );
        $this->RULE_INDICATION['costEndMissing'] = (int)App::getSetting('summarySheet_ruleIndicator_costEndMissing');
        $this->RULE_INDICATION['MissingFirstStateStart'] = (int)App::getSetting(
            'summarySheet_ruleIndicator_firstStateStart'
        );
        //Törzsidő sértés
        $this->RULE_INDICATION['ordinaryTimeViolation'] = (int)App::getSetting(
            'summarySheet_ruleIndicator_ordinaryTimeViolation'
        );
        //Rugalmasok esetén a lekésőbbi távozás után távozott.
        $this->RULE_INDICATION['latesDepartTimeViolation'] = (int)App::getSetting(
            'summarySheet_ruleIndicator_latesDepartTimeViolation'
        );
        // Improduktív ideje van a dolgozónak
        $this->RULE_INDICATION['isImproductiveTime'] = (int)App::getSetting(
            'summarySheet_ruleIndicator_isImproductiveTime'
        );

        //Nem a munkarendje szerinti időben távozott ebédre //not implemented yet
        $this->RULE_INDICATION['breakTimeViolation'] = (int)App::getSetting(
            'summarySheet_ruleIndicator_breakTimeViolation'
        );
        //Ebédidő hosszának napi sértése
        $this->RULE_INDICATION['dailyBreakTimeViolation'] = (int)App::getSetting(
            'summarySheet_ruleIndicator_dailyBreakTimeViolation'
        );
        //Nem a naptípusában megadott műszakokban dolgozott
        $this->RULE_INDICATION['diffBetweenDaytypeAndCalc'] = (int)App::getSetting(
            'summarySheet_ruleIndicator_diffBetweenDaytypeAndCalc'
        );
        //Ha ennyi időt késik akkor maxlate hibajelzés
        $this->RULE_INDICATION['maxLate'] = (int)App::getSetting('summarySheet_ruleIndicator_maxlate');
        //Ünnepnap esetén is hibajelzést ad, ha nincs regisztráció
        $this->RULE_INDICATION['holidayerrorsignal'] = (int)App::getSetting('summarySheet_holidayerrorsignal');
        //getProgSetup("cameback"); //jelzést ad ha visszajött a legkésőbbi távozás után!
        $this->CAME_BACK = ((int)App::getSetting('summarySheet_ruleIndicator_cameBack') === 1);
        //getProgSetup("camein");	//Nincs elszámolt adat, de volt bent
        $this->CAME_IN = ((int)App::getSetting('summarySheet_ruleIndicator_cameIn') === 1);
        // Ezt az időt hozzáadja az éjszakai műszakhoz.
        $this->NIGHT_SHIFT_COMPENSATORY_TIME = (int)App::getSetting('summarySheet_NightShiftCompensatoryTime');
        // 96 órás szabály (dev11484)
        $this->WORK_SCHEDULE_OUT_OF_TIME_CHANGE_IN_HOUR =
            ((int)App::getSetting('workShedule_outOfTimeChange_inHour') !== 0);
        $this->RULE_REGS_FILTER_ONLY_FIRST_IN_LAST_OUT = (new RegsFilterOnlyFirstInLastOutRuleGuesser())->guess();

        $this->WG_FILO_FILTER = (bool)App::getSetting('wg_filo_filter');
        $app_settings_tmp = App::getSetting('summarySheet_rule_regsFilterOnlyFirstStartInLastOutExeptEventTypeId');
        $this->RULE_REGS_FILTER_ONLY_FIRST_IN_LAST_OUT_EXCEPT_EVENT_TYPE_ID =
            isset($app_settings_tmp) ? explode(',', $app_settings_tmp) : [0 => 'DISABLED'];

        $this->RULE_REGS_FILTER_TERMINAL_BEFORE_OUT_TIME = (int)App::getSetting(
            'summarySheet_rule_TerminalBeforeOutTime'
        );
        $this->RULE_REGS_FILTER_TERMINAL_AFTER_OUT_TIME = (int)App::getSetting(
            'summarySheet_rule_TerminalAfterOutTime'
        );
        $this->RULE_REGS_FILTER_MIN_TIME_BETWEEN_REGS = (int)App::getSetting(
            'summarySheet_rule_regsFilterMinTimeBetweenRegs'
        );
        //Rugalmas munkarend esetén csak a eat és az ldt között fogadjon el időt.
        $this->RULE_REGS_FILTER_FLEXIBLE_BETWEEN_EAT_LDT = ((int)App::getSetting(
                'summarySheet_rule_regsFilterFlexibleBetweenEatLdt'
            ) === 1);
        //Keretidős napi balansszal munkarend esetén csak a eat és az ldt között fogadjon el időt.
        $this->RULE_REGS_FILTER_FRAMEWORK_BALANCE_BETWEEN_EAT_LDT = ((int)App::getSetting(
                'summarySheet_rule_regsFilterFrameWorkBalanceBetweenEatLdt'
            ) === 1);
        $this->RULE_REGS_FILTER_EXCEPTION = App::getSetting('summarySheet_rule_regs_filter_exception') ?? '';
        /*
         *  Akkor használjuk ha nincs megadva a mozgás oka
         * ON_WITHOUTCOST: a páratlan mozgást be a párost kilépésnek tekinti.
         * ON_WITHCOST_WITHOUTCOSTEND: az első be, az utolsó mozgás ki, a közbensőket COST nak tekinti.
         *          A cost_id-t a reg_change_terminal_cost táblából kapja meg
         * ON_WITHCOST_WITHCOSTEND: az első be, az utolsó mozgás ki, belső páratlan COSTEND
         *          míg a páros COST nak tekinti. A cost_id-t a reg_change_terminal_cost táblából kapja meg
         * OFF
         */
        $this->RULE_REGS_NO_CAUSE =
            (null !== App::getSetting('summarySheet_rule_regs_no_cause')) ?
                App::getSetting('summarySheet_rule_regs_no_cause') :
                'NOT_FOUND';
        $this->RULE_REGS_NO_CAUSE_FLEXIBLE =
            (null !== App::getSetting('summarySheet_rule_regs_no_cause_flexible')) ?
                App::getSetting('summarySheet_rule_regs_no_cause_flexible') :
                'NOT_FOUND';

        $this->RULE_DAILY_BREAK_TIME = (App::getSetting('summarySheet_rule_dailyBreakTime') != 0);
        $this->RULE_DAILY_BREAK_TIME_WITH_GONE_HOME = (App::getSetting(
                'summarySheet_rule_dailyBreakTimeWithGoneHome'
            ) != 0);
        $this->RULE_DAILY_BREAK_TIME_SUB_BACKWARDS = (App::getSetting(
                'summarySheet_rule_dailyBreakTimeSubBackwards'
            ) != 0);
        //munkaidő utáni szünetet csak a munkaidő lejárta utáni kint tartózkodásból adja vissza
        $this->RETURN_A_BREAK_AFTER_WORKTIME_ONLY_FROM_BEING_OUT_AFTER_THE_END_OF_WORKTIME =
            (App::getSetting('summarySheet_rule_returnBreakAfterWorktimeOnlyFromBeingOutAfterTheEndOfWorktime') != 0);
        $this->RULE_WORKTIME_IN_POST = !(App::getSetting('summarySheet_rule_worktimeinpost') == 0);
        $this->DEDUCTION_OF_BREAKS_OUTSIDE_WORKING_HOURS = !((App::getSetting(
                'summarySheet_deduction_of_breaks_outside_working_hours'
            ) == 0));
        $this->BALANCE_ROUND_TOLERANCE = !(App::getSetting('balanceRoundTolerance') == 0);
        $this->STANDBY_DAYTYPE_BREAKTIMES_OVERLAP = !(App::getSetting(
                'summarySheet_standby_daytype_breaktime_overlap'
            ) == 0);

        $this->OVERTIME_BEFORE_WORKTIME_FLEX = !(App::getSetting('overtimebeforeworktimeflex') == 0);
        //ünnep esetén is ad automatikus regisztrációt ha van beosztás
        $this->HOLIDAY_AS_WORKDAY = !(App::getSetting('enableHolidayAsWorkday') == 0);
        //daytype fullworktime helyett a employee_contract -> daily_work_time használja
        $this->USED_WORKTIME_FROM = !(App::getSetting('usedWorktimeFrom') == 0);
        //törtnapi távollét hozzáadása a balanszhoz rugalmasok esetén
        $this->ADD_ABSENCE_TO_BALANCE = !(App::getSetting('addAbsenceToBalance') == 0);
        //A munkaidő előtti túlórát törli, ha nem érte el a minimumot
        $this->OVERTIME_BEFORE_UNSET = !(App::getSetting('overtimebeforeunset') == 0);
        //A maradék balansz időt visszaadja -e a számolás
        $this->BALANCE_TIME_BACK = !(App::getSetting('balancetimeback') == 0);
        //Pihenőnap - pihenőnap esetén balansz helyett túlórát számoljon
        $this->REST_DAY_BALANCE_TO_OVERTIME = !(App::getSetting('restday_balance_to_overtime') == 0);

        //Automata regisztrációk törlése/eltoltása törtnapi távollét esetén, NONE, DELETE, CREATE
        $this->AUTOMATIC_REGS_MODIFY_ABSENCE_CASE = App::getSetting(
            'summarySheet_automatic_regs_modify_absence_case'
        ) ?? '';
        $this->AUTOMATIC_REGS_OTHER_REGS_RULE = !(App::getSetting(
                'summarySheet_automatic_regs_otherregs_rule'
            ) == 0);

        //calcIntervalLostTimeBack függvényben a visszaadás sorrendjét adja meg, alapesetben üres,
        // mert a legnagyobb munkaidő eltérés indisitype -nál vonja le
        $this->CALC_INTERVAL_LOST_TIME_BACK_INSIDE_TYPE_LIST =
            App::getSetting('calcintervallosttimeback_insidetypelist') ?? '';

        $this->RULE_WORK_TYPE_WITHOUT_ACS_PAYROLL = App::getSetting('worktypewithoutacs_payroll_rule') ?? '';

        $this->SETTING_4_REG_TURNED_ON = App::getSetting('summarySheet_setting_4_reg_turned_on') ?? '';
        $this->SETTING_4_REG_WORKGROUP = App::getSetting('summarySheet_setting_4_reg_workgroup') ?? '';
        $this->SETTING_4_REG_DAY_TYPES = App::getSetting('summarySheet_setting_4_reg_daytypes') ?? '';

        $this->ABSENCE_HOUR_MODE = ((int)App::getSetting('absence_calculation_hour') > 0);
        // INFORMAL munkarendű munkacsoportban lévő dolgozók is kapjanak hibát,
        // ha távolléten vannak, de van regisztrációjuk
        $this->INFORMAL_GOT_ABSENCE_AND_REGS_ERROR = ((int)App::getSetting('informalsGotAbsenceAndRegsError') === 1);
        //TODO: summarySheet_showPrevNextDayCalcText app_settings -el lehet megjeleníteni a felületen
        // a is_prev_day_calc és a is_next_day_calc esetén az Előző nap, Következő nap szöveget a jogcím előtt
    }

    private function measureInit()
    {
        $this->measuresFilterTime = 0;
        $this->measuresStartTime = MICROTIME(true);
    }

    /**
     *    Mérési eredmény mentése a megadott névre
     *
     * @param string $measureLogName - mérési eredmény letárolására: $this->measures[$measureLogName]
     * @param string $sumName - összegzett mérési eredmény letárolása $this->measures[$sumName]
     */
    private function measure($measureLogName, $sumName = null)
    {
        if (($diff = microtime(true) - $this->measuresStartTime) > $this->measuresFilterTime) {
            $this->measures[$measureLogName] = $diff;
        }
        if (!is_null($sumName)) {
            if (!isset($this->measures[$sumName])) {
                $this->measures[$sumName] = 0;
            }
            $this->measures[$sumName] += $diff;
        }
        $this->measuresStartTime = MICROTIME(true);
    }

    public function setCreatedBy($name)
    {
        $this->createdBy = $name;
    }

    public function saveToDebug($data, $fileName = 'debug.txt')
    {
        $dir = Yang::getBasePath() . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'runtime' . DIRECTORY_SEPARATOR;
        $fh = fopen($dir . $fileName, 'w') or die("can't open file");
        fwrite($fh, $data);
        fclose($fh);
    }

    public function setRegistrations(EmployeesRegistrationsDescriptor $registrations): self
    {
        $this->registrations = $registrations;
        return $this;
    }

    public function setUseExternalRegistrationSource(bool $useExternalRegistrationSource): self
    {
        $this->useExternalRegistrationSource = $useExternalRegistrationSource;
        return $this;
    }

    public function isUseExternalRegistrationSource(): bool
    {
        return $this->useExternalRegistrationSource;
    }

    /**
     * @return ErrorDefinitionsDescriptor[]
     */
    public function getErrorDescriptor(): array
    {
        return $this->errorDescriptors;
    }

    public function isErrorDescriptor(): bool
    {
        return isset($this->errorDescriptors);
    }

    public function getCalc(): array
    {
        return $this->calc ?? [];
    }

}//class
//--------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------
//--------------------------------- END CLASS ------------------------------------------------
//--------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------

function cmp($a, $b)
{
    if ($a == $b) {
        return 0;
    }
    return ($a < $b) ? -1 : 1;
}
//--------------------------------------------------------------------------------------------
