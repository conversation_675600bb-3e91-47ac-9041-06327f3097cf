<?php

trait Grid2SummarySheetActionsDialogGrid
{
	/**
	 * DATA
	 */

	protected $everyRegs=0;

	public function actionGridDataRegs() {
		$this->layout = "//layouts/ajax";

		$sumsheetParams = requestParam('sumsheetParams');
		$sessionParams = (int)App::getSetting("summarySheet_sessionParams");
		if ($sessionParams && !is_null(Yang::session('summarySheet_sumsheetParams'))) {
			$sumsheetParams = Yang::session('summarySheet_sumsheetParams');
		}

		$gridResults = $this->fetchGridDataRegs($sumsheetParams);

		$resp = $this->generateGridDataXML($gridResults);

		$results = array(
			'data' => $resp,
		);

		if (isset($gridResults['log'])) {
			$results = array_merge($results, array(
				'log' => $gridResults['log'],
			));
		}

		echo json_encode($results);
	}

	public function actionGridDataCalc() {
		$this->layout = "//layouts/ajax";

		$sumsheetParams = requestParam('sumsheetParams');
		$sessionParams = (int)App::getSetting("summarySheet_sessionParams");
		if ($sessionParams && !is_null(Yang::session('summarySheet_sumsheetParams'))) {
			$sumsheetParams = Yang::session('summarySheet_sumsheetParams');
		}

		$gridResults = $this->fetchGridDataCalc($sumsheetParams);

		$resp = $this->generateGridDataXML($gridResults);

		$results = array(
			'data' => $resp,
		);

		if (isset($gridResults['log'])) {
			$results = array_merge($results, array(
				'log' => $gridResults['log'],
			));
		}

		echo json_encode($results);
	}

	public function actionSumsheetShowEveryRegs()
	{
		if($_SESSION["summarySheet_EveryRegs"]["show"])
		{
			$_SESSION["summarySheet_EveryRegs"]["show"]=0;
		}
		else
		{
			$_SESSION["summarySheet_EveryRegs"]["show"]=1;
		}

		$status = array(
				'status'	=> 1,
			);

		echo json_encode($status);
	}

	private function createSessionEveryRegs($ec,$day)
	{
		if (!isset($_SESSION["summarySheet_EveryRegs"]) || !isset($_SESSION["summarySheet_EveryRegs"]["id"])
			|| $_SESSION["summarySheet_EveryRegs"]["id"]!=$ec."-".$day)
		{
			$_SESSION["summarySheet_EveryRegs"]["id"]=$ec."-".$day;
			$_SESSION["summarySheet_EveryRegs"]["show"]=App::getSetting("summarySheet_every_regs_default");
		}
	}

	/**
	 * ADD
	 */
	public function actionSumsheetAddRow() {
		$this->layout = "//layouts/ajax";

		$grid_id = requestParam('grid_id');
        $statusPublished = Status::PUBLISHED;

		$sumsheetParams = requestParam('sumsheetParams');
		$sessionParams = (int)App::getSetting("summarySheet_sessionParams");
		if ($sessionParams && !is_null(Yang::session('summarySheet_sumsheetParams'))) {
			$sumsheetParams = Yang::session('summarySheet_sumsheetParams');
		}

		$start_date = $sumsheetParams["startDate"];
		$employee_contract_id = $sumsheetParams["ecID"];
		$daytype_id = $sumsheetParams["daytypeID"];
		$filter_from = $sumsheetParams["regFilterFrom"];
		$filter_to = $sumsheetParams["regFilterTo"];
		$dateTimeFormat = ((App::getSetting("wfm_used_seconds") === '0')?"Y-m-d H:i":"Y-m-d H:i:s");

		$status = array(
			'status'	=> 0,
			'pkSaved'	=> null,
			'error'		=> "",
		);

		if ($grid_id === 'regsGrid' && App::hasRight($this->getControllerID(), "dialogreg_add")) {

			$daytype = WorkScheduleUsed::model()->findByAttributes(
						[], 
						['condition' => "`employee_contract_id` = '$employee_contract_id' AND `day` = '$start_date' AND `status` = $statusPublished"]
						);

			$daytype = isset($daytype) ? $daytype :
                        Daytype::model()->findByAttributes(
						[], 
						['condition' => "`daytype_id` = '$daytype_id' AND '$start_date' BETWEEN `valid_from` AND IFNULL(`valid_to`,'".App::getSetting("defaultEnd")."') AND `status` = $statusPublished"]
						);



			if ($daytype) {
				if ($daytype->type_of_daytype == 'RESTDAY' && $daytype->work_start === '00:00' && $daytype->work_end === '00:00')
				{
                    $dayTypeWorkStart = $daytype->earliest_arrival_time;
                    $dayTypeWorkEnd = $daytype->latest_depart_time;
                }
                else
                {
                    $dayTypeWorkStart = $daytype->work_start;
                    $dayTypeWorkEnd = $daytype->work_end;   
                }
				
                $daytypeWorkStartFullDate = strtotime($start_date." ".$dayTypeWorkStart);
                $daytypeWorkEndFullDate = strtotime($start_date." ".$dayTypeWorkEnd);
                $daytypeWorkStartPlusDay = strtotime($start_date." ".$dayTypeWorkEnd." +1 day");
                $daytypeWorkStartMinusDay = strtotime($start_date." ".$dayTypeWorkStart." -1 day");
                
                $work_start = date($dateTimeFormat, $daytypeWorkStartFullDate);
                $work_end = date($dateTimeFormat, $daytypeWorkEndFullDate);

                if ($daytypeWorkEndFullDate <= $daytypeWorkStartFullDate) { // éjszaka
                    if ($daytype->pre_day === "FALSE") { // az éjszakai műszak az aktuális napon indul
                        $work_end = date($dateTimeFormat, $daytypeWorkStartPlusDay); // work_end a kövi napon van
                    } else {
                        $work_start = date($dateTimeFormat, $daytypeWorkStartMinusDay); // work_start az előző napon van
                    }
                }

                /**
                 * -----|----------|----- filter_from, filter_to
                 * --|----------------|-- work_end
                 */

                if (strtotime($work_start) < strtotime($filter_from) || strtotime($work_start) > strtotime($filter_to)) {
                    $work_start = $filter_to;
                }
				
				$card = EmployeeCard::getCardByEcId($employee_contract_id,"DATE('$work_start')");

				if (!empty($card))
				{
					$r = new Registration;
					$r->setIsNewRecord(true);
					$r->terminal_id = 'default';
					$r->reader_id = 'default';
					$r->card = $card[0];
					$r->time = $work_start;
                    $r->event_type_id = "NMB";
					if(substr((string)App::getSetting("summarySheet_rule_regs_no_cause"),0,3) === "ON_")
                    {
						$r->event_type_id = "MZG";
					}

                    $checkPreviousReg = Registration::getRegs($card, ['NMB', 'NMK'], $filter_from, $filter_to);
                    if (!empty($checkPreviousReg))
                    {
                        $foundNMK = false;
                        foreach($checkPreviousReg as $previusRegs)
                        {
                            if ($previusRegs->event_type_id == 'NMK')
                            {
                                $foundNMK = true;
                                break;
                            }
                        }
                        if ($foundNMK)
                        {
                            $r->time = $work_start;
						    $r->event_type_id = "NMB";
                        }
                        else
                        {
                            $r->time = $work_end;
						    $r->event_type_id = "NMK";
                        }
                    }

					$r->status = Status::PUBLISHED;
					$r->save();

					$status = array(
						'status'	=> 1,
						'pkSaved'	=> $r->row_id,
						'error'		=> null,
					);
				} else {
					$status = array(
						'status'	=> 0,
						'pkSaved'	=> null,
						'error'		=> "",
					);
				}
			}
		} else if ($grid_id === 'calcGrid' && App::hasRight($this->getControllerID(), "dialogcalc_add")) 
        {
			EmployeeCalc::model()->deleteAll("`employee_contract_id` = '$employee_contract_id' AND `day` = '$start_date' AND `value` IS NULL");

			$ec = new EmployeeCalc;
			$ec->setIsNewRecord(true);
			$ec->employee_contract_id = $employee_contract_id;
			$ec->day = $start_date;
			$ec->inside_type_id = App::getSetting('defaultInsideTypeId');
			$ec->value = 0;
			$ec->status = Status::SAVED;
			$ec->cost_id = 'default';
			$ec->costcenter_id = 'default';
			$ec->save();

            EmployeeCalcUsedDaytype::model()->updateAll(['status' => Status::SAVED], "`employee_contract_id` = '$employee_contract_id' AND `day` = '$start_date' AND `status` = $statusPublished");
		}

		echo json_encode($status);
	}

	/**
	 * MOD
	 */

	public function actionGridEditRegs() 
    {
		$this->oboStudyContract = App::getSetting("oboStudyContractWFM");
		$this->limitOvertimeInOutRegs = App::getSetting("summarySheet_limitOtRegs");
		$this->layout = "empty";
		header("Content-Type:text/xml");

		$pk = requestParam('ids');

		$model = Registration::model()->findByPk($pk);

		$hasEventTypeRight = true;
		if ((int)App::getSetting("useEventTypeRight") == 1) {
			$eventTypeRights = Yang::session("eventTypeRights");
			$hasEventTypeRight = false;

			if (!is_null($model) && !is_null($eventTypeRights)) {
				if (in_array($model->event_type_id, $eventTypeRights)) {
					$hasEventTypeRight = true;
				}
			}
		}

		if (!App::isRootSessionEnabled() && (!App::hasRight($this->getControllerID(), "dialogreg_modify") || !$hasEventTypeRight)) {
			$msg = Dict::getModuleValue("ttwa-wfm", "summarySheet_no_right_to_save");
			echo "<?xml version='1.0' ?><data><action type='invalid' sid='" . $pk . "' tid='" . $pk . "' details='" . $msg . "' ></action></data>";
			return;
		}

        if (is_null($model))
        {
            $msg = Dict::getValue("cannot_find_registration");
            Yang::log($msg, 'log', 'Grid2SummarySheetActionsDialogGrid');
            echo "<?xml version='1.0' ?><data><action type='invalid' sid='" . $pk . "' tid='" . $pk . "' details='" . $msg . "' ></action></data>";
            return;
        }

		$sumsheetParams = requestParam('sumsheetParams');
		$sessionParams = (int)App::getSetting("summarySheet_sessionParams");
		if ($sessionParams && !is_null(Yang::session('summarySheet_sumsheetParams'))) {
			$sumsheetParams = Yang::session('summarySheet_sumsheetParams');
		}

		$filter_from = $sumsheetParams["regFilterFrom"];
		$filter_to = $sumsheetParams["regFilterTo"];

		$modelData = [];
		$i = 0;
		$columns = $this->columns();
		$columns = $columns["regsGrid"];

		$eventTypeId = "";
		foreach ($columns as $column => $attrs) {
			if ( LAGridColumnProperties::isColumnVisibleInGrid($attrs) ) {
				$modelData[$column] = requestParam($pk . "_c" . $i);
				if ($i === 1) {
					$eventTypeId = requestParam($pk . "_c" . $i);
				}
				$i++;
			}
		}

        $timeTime = strtotime($modelData["time"]);
		$filter_fromTime = strtotime($filter_from);
		$filter_toTime = strtotime($filter_to);

		if ($timeTime < $filter_fromTime || $timeTime > $filter_toTime) {
			$model->addGenericError("error_datetime_interval", array("from" => $filter_from, "to" => $filter_to));
            $msg = Dict::getValue("error_datetime_interval", array("from" => $filter_from, "to" => $filter_to));
            Yang::log($msg, 'log', 'Grid2SummarySheetActionsDialogGrid');
            echo "<?xml version='1.0' ?><data><action type='invalid' sid='" . $pk . "' tid='" . $pk . "' details='" . htmlspecialchars($msg) . "' ></action></data>";
            return;
		}

		$startDate = $sumsheetParams["startDate"];
		$employeeContractId = $sumsheetParams["ecID"];
		$oboStudyContractOK = true;
		if ($this->oboStudyContract && ($eventTypeId == "TSB" || $eventTypeId == "TSK"))
		{
			$getTanulmanyiSzerzodes = "
				SELECT
					IF(ext2.`ext2_option1` IS NULL, '0', '1') AS tszerzaktiv
				FROM
					`employee_contract` AS ec
				LEFT JOIN
					`employee_ext2` AS ext2 ON
						ext2.`employee_id` = ec.`employee_id`
					AND ('" . $startDate . "' BETWEEN ext2.`valid_from` AND ext2.`valid_to`)
					AND ext2.`status` = " . Status::PUBLISHED . "
				WHERE
						ec.`status` = " . Status::PUBLISHED . "
					AND ('" . $startDate . "' BETWEEN ec.`valid_from` AND ec.`valid_to`)
					AND ec.`employee_contract_id` = '" . $employeeContractId . "'
			";

			$res = dbFetchAll($getTanulmanyiSzerzodes);
			if (!isset($res[0]) || $res[0]["tszerzaktiv"] == "0") {
				$oboStudyContractOK = false;
			}
		}

		// Túlóra reg limitálás
		$overtimeLimitationOK = true;
		if ($this->limitOvertimeInOutRegs && ($eventTypeId == "OTB" || $eventTypeId == "OTK" || $eventTypeId == "NMB" || $eventTypeId == "NMK"))
		{
			// Időpont
			$time = $modelData["time"];
			// Beosztás
			$hasSched = false;
			$gews = new GetEmployeeWorkSchedule($startDate, $startDate, [$employeeContractId], true, "workSchedule");
			$sched = $gews->get();
			$typeOfDaytype = $sched[$employeeContractId][$startDate]["used_type_of_daytype"];
			if ($typeOfDaytype == "WORKDAY")
			{
				$start = new DateTime($startDate . " " . $sched[$employeeContractId][$startDate]["used_work_start"]);
				$end = new DateTime($startDate . " " . $sched[$employeeContractId][$startDate]["used_work_end"]);
				if (strtotime($sched[$employeeContractId][$startDate]["used_work_start"]) >= strtotime($sched[$employeeContractId][$startDate]["used_work_end"])) {
					$end->modify('+1 day');
				}
				$hasSched = true;
			} else if ($typeOfDaytype == "RESTDAY") {
				$hasSched = true;
			} else {
				// nincs munkarendje
			}

			// Túlóra elrendelés
			$SQL = "
				SELECT
					`type`,
					`break_between_wt_ot`,
					`time`,
					`restday_overtime_start`,
					`restday_overtime_end`
				FROM `work_schedule_overtime`
				WHERE
						`status` = " . Status::PUBLISHED . "
					AND `employee_contract_id` = '{$employeeContractId}'
					AND `day` = '{$startDate}'
			";
			$res = dbFetchRow($SQL);
			if (!empty($res) && !is_null($res) && $hasSched)
			{
				// le van-e fedve az elrendelés időszak, ha nincs az új regisztráció megfelelő-e
				$overtimeSQL = "
					SELECT
						SUM(`value`) AS overtime_sum
					FROM `employee_calc`
					WHERE
							`employee_contract_id` = '{$employeeContractId}'
						AND `inside_type_id` LIKE 'ot%'
						AND `status` IN (" . Status::PUBLISHED . ", " . Status::SAVED . ")
						AND `day` = '{$startDate}'
				";
				$otres = dbFetchRow($overtimeSQL);
				// Nincs meg az elrendelt túlórája
				if (empty($otres) || $otres["overtime_sum"] < $res["time"])
				{
					$regTime = strtotime($time);
					// Az új v. módosított reg bele esik-e az időszakba
					if ($res["type"] == "AFTERWORK")
					{
						$intervalEnd = strtotime($end->format("Y-m-d H:i:s")) + (int)$res["break_between_wt_ot"] + (int)$res["time"];
						$intervalStart = strtotime($end->format("Y-m-d H:i:s")) + (int)$res["break_between_wt_ot"];
						if ((strtotime($end->format("Y-m-d H:i:s")) < $regTime && $regTime < $intervalStart) || $regTime > $intervalEnd) {
							$overtimeLimitationOK = false;
						}
					} else if ($res["type"] == "BEFOREWORK") {
						$intervalEnd = strtotime($start->format("Y-m-d H:i:s")) - (int)$res["break_between_wt_ot"];
						$intervalStart = strtotime($start->format("Y-m-d H:i:s")) - (int)$res["break_between_wt_ot"] - (int)$res["time"];
						if ((strtotime($start->format("Y-m-d H:i:s")) > $regTime && $regTime > $intervalEnd) || $regTime < $intervalStart) {
							$overtimeLimitationOK = false;
						}
					} else if ($res["type"] == "RESTDAY") {
						$intervalStart = strtotime($res["restday_overtime_start"]);
						$intervalEnd = strtotime($res["restday_overtime_end"]);
						if ($regTime < $intervalStart || $regTime > $intervalEnd) {
							$overtimeLimitationOK = false;
						}
					}
				}
			}
		}

		$modelData["modified_by"] = $this->userIdToSwitch;
        $regsGridDislayTerminalId = [];
        if(AnyCache::has('summarysheetTerminals')) {
            $regsGridDislayTerminalId = AnyCache::get('summarysheetTerminals');
        }

        $modelDataTemp = [];
        if (isset($regsGridDislayTerminalId[$modelData["terminal_id"]]))
        {
			$modelDataTemp["terminal_id"] = $regsGridDislayTerminalId[$modelData["terminal_id"]]["terminal_id"];
			$modelDataTemp["reader_id"] = $regsGridDislayTerminalId[$modelData["terminal_id"]]["reader_id"];
		}
        else
        {
            $modelDataTemp["terminal_id"] = 'default';
            $modelDataTemp["reader_id"] = 'default';
        }
        $modelData["terminal_id"] = $modelDataTemp["terminal_id"];
        $modelData["reader_id"] = $modelDataTemp["reader_id"];

		$model->attributes = $modelData;
		$model->status = (int)$model->status === Status::WAIT_MODIFY ? Status::PUBLISHED : $model->status;

        $card = EmployeeCard::getCardByEcId($employeeContractId, "DATE('$model->time')");
        if (empty($card[0])) {
            $model->card = "1234"; // Üres kártya default hibaüzenet ne legyen.
            $model->addGenericError("sumsheet_error_no_employee_card");
        } elseif (!array_search($model->card, $card)) {
            $model->card = $card[0];
        }

		// nem sikeres
		if ($model->validate())
		{
			if ($overtimeLimitationOK)
			{
				if ($oboStudyContractOK)
				{
					$model->save();
					// Túlóra meghaladás vizsgálat
					if ($this->limitOvertimeInOutRegs && ($eventTypeId == "OTB" || $eventTypeId == "OTK" || $eventTypeId == "NMB" || $eventTypeId == "NMK"))
					{
						// számolás manuális futtatás
						$timeCardCalculation = new TimeCardCalculation([$employeeContractId], $startDate, $startDate);
						$timeCardCalculation->payrollcalculation();
						// túlóra meghaladja-e a 4 órát
						$overtimeSQL = "
							SELECT
								SUM(`value`) AS overtime_sum
							FROM `employee_calc`
							WHERE
									`employee_contract_id` = '{$employeeContractId}'
								AND `inside_type_id` LIKE 'ot%'
								AND `status` IN (" . Status::DRAFT . ", " . Status::PUBLISHED . ", " . Status::SAVED . ")
								AND `day` = '{$startDate}'
						";
						$otres = dbFetchRow($overtimeSQL);
						if (!empty($otres))
						{
							if ((int)$otres["overtime_sum"] > 14400)
							{
								// reg törlése (és pre_row_id visszaaktiválás ha van)
								$updateRegsSQL = "
									UPDATE `registration` r1
									LEFT JOIN `registration` r2 ON
										r2.`row_id` = r1.`pre_row_id`
									SET
										r1.`status` = " . Status::INVALID . ",
										r1.`modified_by` = '" . $this->userIdToSwitch . "',
										r1.`modified_on` = NOW(),
										r2.`status` = " . Status::PUBLISHED . ",
										r2.`modified_by` = '" . $this->userIdToSwitch . "',
										r2.`modified_on` = NOW()
									WHERE
											r1.`time` = '{$time}'
										AND r1.`event_type_id` = '{$eventTypeId}'
										AND r1.`card` = '{$model->card}'
										AND r1.`status` = " . Status::PUBLISHED . "
								";
								dbExecute($updateRegsSQL);
								// számolás manuális futtatás
								$timeCardCalculation = new TimeCardCalculation([$employeeContractId], $startDate, $startDate);
								$timeCardCalculation->payrollcalculation();
								$msg = Dict::getValue("error_summarySheet_regOvertimeLimit2");
								echo "<?xml version='1.0' ?><data><action type='invalid' sid='" . $pk . "' tid='" . $pk . "' details='" . $msg . "' ></action></data>";
							} else {
								echo "<?xml version='1.0' ?><data><action type='updated' sid='" . $pk . "' tid='" . $pk . "' ></action></data>";
							}
						} else {
							echo "<?xml version='1.0' ?><data><action type='updated' sid='" . $pk . "' tid='" . $pk . "' ></action></data>";
						}
					} else {
						echo "<?xml version='1.0' ?><data><action type='updated' sid='" . $pk . "' tid='" . $pk . "' ></action></data>";
					}
					
				} else {
					$msg = $startDate . " " . Dict::getValue("error_date_not_in_contract_interval");
					echo "<?xml version='1.0' ?><data><action type='invalid' sid='" . $pk . "' tid='" . $pk . "' details='" . $msg . "' ></action></data>";
				}
			} else {
				$msg = Dict::getValue("error_summarySheet_regOvertimeLimit1");
				echo "<?xml version='1.0' ?><data><action type='invalid' sid='" . $pk . "' tid='" . $pk . "' details='" . $msg . "' ></action></data>";
			}
		} else {
			$error = MyActiveForm::_validate($model, false);
			$arr = (array) json_decode($error);
			$msg = "";

			foreach ($arr as $value) {
				foreach ($value as $val) {
					$msg .= $val."<br/>";
				}
			}

			$msg = str_replace(array("<",">"), array("[","]"), $msg);
			$msg = strip_tags($msg);
			echo "<?xml version='1.0' ?><data><action type='invalid' sid='" . $pk . "' tid='" . $pk . "' details='" . $msg . "' ></action></data>";
		}
	}

	public function actionGridEditCalc() {
		$publishedStatus = Status::PUBLISHED;
		$savedStatus = Status::SAVED;
		$lockedStatus = Status::LOCKED;
		$draftStatus =  Status::STATUS_DRAFT;
        $deleteStatus = Status::DRAFT_DELETE;
		$summarysheetAutomatiLockStatus = App::hasRight($this->getControllerID(), "summarysheet_automaticlockstatus");
		$employeeCalcAndEmployeeUsedStatus = $summarysheetAutomatiLockStatus ? $lockedStatus : $savedStatus;

		$this->layout = "empty";
		header("Content-Type:text/xml");

		$pk = requestParam('ids');

		if (!App::hasRight($this->getControllerID(), "dialogcalc_modify")) {
			$msg = "No right to save!";
			echo "<?xml version='1.0' ?><data><action type='invalid' sid='" . $pk . "' tid='" . $pk . "' details='" . $msg . "' ></action></data>";
			return false;
		}

		$sumsheetParams = requestParam('sumsheetParams');
		$sessionParams = (int)App::getSetting("summarySheet_sessionParams");
		if ($sessionParams && !is_null(Yang::session('summarySheet_sumsheetParams'))) {
			$sumsheetParams = Yang::session('summarySheet_sumsheetParams');
		}

		$startDate = $sumsheetParams["startDate"];
		$ecID = $sumsheetParams["ecID"];

        $model = EmployeeCalc::model()->findByPk($pk);
		$i = 0;
		$columns = $this->columns();
		$columns = $columns["calcGrid"];

		foreach ($columns as $column => $attrs) {
			if ( LAGridColumnProperties::isColumnVisibleInGrid($attrs) ) {
				$colValue = requestParam($pk . "_c" . $i);

				if (!empty($colValue) && isset($model->$column)) {
					$model->$column = $colValue;
				}
				$i++;
			}
		}

        $model->modified_by = $this->userIdToSwitch;

		/**
		 * Ha a bentlét típusa absredempted-el kezdődik, akkor ellenőrizni kell,
		 * hogy van-e elég rendelkezésre álló keret.
		 */
		if (substr($model->attributes['inside_type_id'], 0, 12) === 'absredempted') {
			$absenceValuesAreTheNumberOfDecimalPlaces = (int)App::getSetting('absenceValuesAreTheNumberOfDecimalPlaces');
			$employee_contract_id = $model->attributes['employee_contract_id'];
			$year = date('Y', strtotime($model->attributes['day']));

			$frame = AHPEmployeeFunctions::getEmployeeAbsenceRedemptFrameAndUsedByEcID($employee_contract_id,$year);
			$used = AHPEmployeeFunctions::getEmployeeAbsenceRedemptUsedByEcID($employee_contract_id,$year,false);
			$used += AHPEmployeeFunctions::getEmployeeAbsenceRedemptFrameAndUsedByEcID($employee_contract_id,$year,'USED','base.`row_id` != '.$pk);

			$frame = round($frame, $absenceValuesAreTheNumberOfDecimalPlaces);
			$used = round($used, $absenceValuesAreTheNumberOfDecimalPlaces);
			if (!is_numeric($model->attributes['value'])) {
				$negative = false;

				if (substr($model->attributes['value'] , 0, 1) === "-") {
					$negative = true;
					$model->attributes['value']  = substr($model->attributes['value'] , 1); // show string from character 2
				}

				$model->value  =(is_numeric($model->attributes['value'] ))?$model->attributes['value'] :($negative?"-":"").strtotime("1970-01-01 ".$model->attributes['value'] ." UTC");
			}

			//Yang::log($model->value, 'log', 'sys.SUMSh');
			$mpl = AHPEmployeeFunctions::getEmployeeAbsenceMultiplierByEcID($employee_contract_id, $model->attributes['day']);
			$current = $model->attributes['value'] / 3600 / (8*$mpl);

			if ($current + $used > $frame) {
				$model->addGenericError("absredempted_error_no_frame", []);
			}
		}

		// nem sikeres
		if ($model->validate())
		{
			$model->save();

			$lockTypeExeption=(new GetEmployeeCalc())->getLockTypeException();

			$filter = "`employee_contract_id` = '$ecID'	AND `day` = '$startDate' AND `status` in ($publishedStatus,$savedStatus,$draftStatus) 
						AND `inside_type_id` NOT IN('".implode("','",$lockTypeExeption)."')";
			EmployeeCalc::model()->updateAll(['status' => $employeeCalcAndEmployeeUsedStatus, "employee_calc_error_type_id" => 0], $filter); // jogcímek javításakor töröljük a calc errorokat!

			$filter = "`employee_contract_id` = '$ecID' AND `day` = '$startDate' AND `status` IN ($publishedStatus,$savedStatus, $deleteStatus)";
			EmployeeCalcUsedDaytype::model()->updateAll(['status' => $employeeCalcAndEmployeeUsedStatus], $filter);

			if ($summarysheetAutomatiLockStatus)
			{
				$filter = "`employee_contract_id` = '$ecID'	AND `day` = '$startDate' AND `status` IN ($publishedStatus)";
				EmployeeCalcMessage::model()->updateAll(['status' => $employeeCalcAndEmployeeUsedStatus], $filter);
				EmployeeCalcOtherStates::model()->updateAll(['status' => $employeeCalcAndEmployeeUsedStatus], $filter);
				EmployeeExtraHours::model()->updateAll(['status' => $employeeCalcAndEmployeeUsedStatus], $filter);
				WorkScheduleOvertime::model()->updateAll(['status' => $employeeCalcAndEmployeeUsedStatus], $filter);
			}

			echo "<?xml version='1.0' ?><data><action type='updated' sid='" . $pk . "' tid='" . $pk . "' ></action></data>";
		} else {
			$error = MyActiveForm::_validate($model, false);
			$arr = (array) json_decode($error);
			$msg = "";

			foreach ($arr as $value) {
				foreach ($value as $val) {
					$msg .= $val;
				}
			}

			$msg = strip_tags($msg);

			echo "<?xml version='1.0' ?><data><action type='invalid' sid='" . $pk . "' tid='" . $pk . "' details='" . $msg . "' ></action></data>";
		}
	}

	/**
	 * DEL
	 */

	public function actionSumsheetDelRow() {
		$modelName = requestParam('modelName');

		if ($modelName === "EmployeeCalc" && App::hasRight($this->getControllerID(), "dialogcalc_del")) {
			$pk 		= requestParam('ids');
			$foundCalc 	= EmployeeCalc::model()->findByPk($pk);
			$ecID 		= null;
			$startDate 	= null;

			if ($foundCalc) {
				$ecID 		= $foundCalc->employee_contract_id;
				$startDate 	= $foundCalc->day;
			}

			if (!empty($ecID) && !empty($startDate)) {
				/**
				 * Mentett státusz legyen, ha egyet is törlünk a számolásból
				 */
				
				$savedStatus = Status::SAVED;
				$publishedStatus = Status::PUBLISHED;
				$deleteStatus = Status::DRAFT_DELETE;
				$draftStatus = Status::DRAFT;

				$filter = "`row_id` = $pk";
				EmployeeCalc::model()->updateAll(['status' => $deleteStatus,], $filter);

				$filter = "`employee_contract_id` = '$ecID' AND `day` = '$startDate' AND `status` IN ($draftStatus, $publishedStatus)";
				EmployeeCalc::model()->updateAll(['status' => $savedStatus, "employee_calc_error_type_id" => 0], $filter); // jogcímek javításakor töröljük a calc errorokat!
				EmployeeCalcUsedDaytype::model()->updateAll(['status' => $savedStatus], $filter);

				$crit = new CDbCriteria();
				$crit->condition = "`employee_contract_id` = '$ecID' AND `day` = '$startDate' AND `status` = $savedStatus";
				$count = (int)EmployeeCalc::model()->count($crit);
				
				if ($count == 0)
				{
					$filter = "`employee_contract_id` = '$ecID' AND `day` = '$startDate'";
					EmployeeCalcUsedDaytype::model()->updateAll(['status' => $deleteStatus], $filter);
				}
			}

			$resp = array(
				'status'	=> 1,
				'pkDeleted' => $pk,
			);

			echo json_encode($resp);
		} else if ($modelName === "Registration" 
				&& (App::hasRight($this->getControllerID(), "dialogreg_del") || App::hasRight($this->getControllerID(), "dialogreg_req_del"))) {
			$ids = requestParam('ids');
			$idsArray = explode(";", $ids);
				
			$deleted = false;
            $sumsheetParams = requestParam('sumsheetParams') ?? null;
            $buttonRight = $this->getSumSheetRights($sumsheetParams);
			if ( $buttonRight["dialogreg_del"]) {
				foreach ($idsArray as $id) {
					$deleted = true;

					$model = $modelName::model()->findByPk($id);

					if((int)$model->status===Status::DRAFT and !empty($model->pre_row_id))
					{
						$temporary=$modelName::model()->findByAttributes(
								array('pre_row_id' => $model->pre_row_id, 'status' => Status::TEMPORARY));
						if(!empty($temporary))
						{
							$temporary->status=Status::PUBLISHED;
							$temporary->save();
						}
					}
					if (!is_null($model->status))
					{
						$model->modified_by = $this->userIdToSwitch;
						$model->status = Status::DELETED;
						$model->save();
					}
				}
			} else if ($buttonRight["dialogreg_req_del"]) {
				$this->reqDeleteSummarySheetRegs($modelName, $idsArray);
			}

			if ($deleted) {
				$status = array(
					'status'	=> 1,
					'pkDeleted' => $ids,
				);
			} else {
				$status = array(
					'status'	=> 0,
				);
			}

			echo json_encode($status);
		}
	}
}