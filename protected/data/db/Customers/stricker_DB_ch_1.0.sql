-- # RELEASE 1.0

UPDATE `_sql_version` SET `revision`=1, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -1--2020.06.11.-10:53----------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = 'SAGE' WHERE `setting_id` = 'ptr_default_mode';
UPDATE `app_settings` SET `setting_value` = 'STRICKER' WHERE `setting_id` = 'ptr_customer';
UPDATE `app_settings` SET `setting_value` = '0' WHERE `setting_id` = 'ptrCorrectLastMonth';

UPDATE `_sql_version` SET `revision`=2, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -2--2020.08.31.-11:15----------------------------------------------------------------

INSERT INTO `state_type` (`row_id`, `state_type_id`, `name_dict_id`, `short_name_dict_id`, `payroll_calc_id`, `day_type`, `reduce_framework`, `reduce_balance`, `worktime`, `in_use`, `default`, `status`, `save`, `state_type_status`, `pre_row_id`) VALUES
	(NULL, '1eb1bbd0e8772267d22ae86f7ab63bce', 'absence_type_unpaid_leave', 'absence_type_unpaid_leave', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '3948f477432aebf15a90a051c4b07840', 'absence_type_suspension_for_punishment', 'absence_type_suspension_for_punishment', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '057385583107d23d85f397ad847fff42', 'absence_type_contract_suspension', 'absence_type_contract_suspension', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, 'a779664df6da7e203a07e21a0762c6cc', 'absence_type_vacation', 'absence_type_vacation', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, 'f54d066221bb49cc5528d59c9c20e24c', 'absence_type_previous_year_holidays', 'absence_type_previous_year_holidays', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '35b17c53f4bbacd97e741e90c811ad36', 'absence_type_leaving_in_service', 'absence_type_leaving_in_service', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, 'b4c0651d31c63c96df4f35a41ea33902', 'absence_type_missing_strike', 'absence_type_missing_strike', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '0c48b766ff7d9b6b28a89b93847ea2fe', 'absence_type_electoral_law', 'absence_type_electoral_law', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '21abd6dbf2ed7ad73e4abc2adb4e12c3', 'absence_type_union', 'absence_type_union', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, 'ec56c2ce6cf4c25d63cf147365afdd58', 'absence_type_prolonged_absence', 'absence_type_prolonged_absence', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '83fd36d8ca2599a09b7224a8c7b1e1e1', 'absence_type_illness_day', 'absence_type_illness_day', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, 'f9080b5f1412afd4e5efcb893b6ca949', 'absence_type_illness_hour', 'absence_type_illness_hour', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '84dcf298fca29a1dd246502724d826eb', 'absence_type_insurance_leave', 'absence_type_insurance_leave', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, 'c4a23d94c93c7500e3bdf97cc8cbf8b7', 'absence_type_medical_leave', 'absence_type_medical_leave', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '236ab9d5a79b89dc1b6cf832e5f3150e', 'absence_type_wedding', 'absence_type_wedding', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '4237ba7960c41f48793e7ec644a1fe35', 'absence_type_pregnancy', 'absence_type_pregnancy', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '089abfabd43313d043ac807e775128f2', 'absence_type_retirement_for_childcare', 'absence_type_retirement_for_childcare', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, 'b5152bd8472e81a666381bf99d546c87', 'absence_type_mourning', 'absence_type_mourning', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, 'd519994ed69869e8928bd98c118db46d', 'absence_type_birth', 'absence_type_birth', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '5ac088f356af3650149df3038ca8eba1', 'absence_type_exceptional_support', 'absence_type_exceptional_support', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '1e4240db0d7790479ea799944d135fec', 'absence_type_urgent_childcare', 'absence_type_urgent_childcare', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL);
INSERT INTO `state_type` (`row_id`, `bgcolor`, `state_type_id`, `name_dict_id`, `short_name_dict_id`, `payroll_calc_id`, `day_type`, `reduce_framework`, `reduce_balance`, `worktime`, `in_use`, `default`, `status`, `save`, `state_type_status`, `pre_row_id`) VALUES
	(NULL, '#4017c9', '1eb1bbd0e8772267d22ae86f7ab63bce', 'absence_type_unpaid_leave', 'absence_type_unpaid_leave', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#9cad34', '3948f477432aebf15a90a051c4b07840', 'absence_type_suspension_for_punishment', 'absence_type_suspension_for_punishment', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#287fa7', '057385583107d23d85f397ad847fff42', 'absence_type_contract_suspension', 'absence_type_contract_suspension', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#972ac9', 'a779664df6da7e203a07e21a0762c6cc', 'absence_type_vacation', 'absence_type_vacation', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#b04193', 'f54d066221bb49cc5528d59c9c20e24c', 'absence_type_previous_year_holidays', 'absence_type_previous_year_holidays', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#e50dea', '35b17c53f4bbacd97e741e90c811ad36', 'absence_type_leaving_in_service', 'absence_type_leaving_in_service', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#e3a868', 'b4c0651d31c63c96df4f35a41ea33902', 'absence_type_missing_strike', 'absence_type_missing_strike', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#2260b6', '0c48b766ff7d9b6b28a89b93847ea2fe', 'absence_type_electoral_law', 'absence_type_electoral_law', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#80d910', '21abd6dbf2ed7ad73e4abc2adb4e12c3', 'absence_type_union', 'absence_type_union', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#ddbde0', 'ec56c2ce6cf4c25d63cf147365afdd58', 'absence_type_prolonged_absence', 'absence_type_prolonged_absence', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#e6d33d', '83fd36d8ca2599a09b7224a8c7b1e1e1', 'absence_type_illness_day', 'absence_type_illness_day', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#beef53', 'f9080b5f1412afd4e5efcb893b6ca949', 'absence_type_illness_hour', 'absence_type_illness_hour', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#65d278', '84dcf298fca29a1dd246502724d826eb', 'absence_type_insurance_leave', 'absence_type_insurance_leave', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#a4f89e', 'c4a23d94c93c7500e3bdf97cc8cbf8b7', 'absence_type_medical_leave', 'absence_type_medical_leave', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#a28f3a', '236ab9d5a79b89dc1b6cf832e5f3150e', 'absence_type_wedding', 'absence_type_wedding', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#e51fa8', '4237ba7960c41f48793e7ec644a1fe35', 'absence_type_pregnancy', 'absence_type_pregnancy', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#ba218b', '089abfabd43313d043ac807e775128f2', 'absence_type_retirement_for_childcare', 'absence_type_retirement_for_childcare', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#524ca9', 'b5152bd8472e81a666381bf99d546c87', 'absence_type_mourning', 'absence_type_mourning', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#915dac', 'd519994ed69869e8928bd98c118db46d', 'absence_type_birth', 'absence_type_birth', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#718a9c', '5ac088f356af3650149df3038ca8eba1', 'absence_type_exceptional_support', 'absence_type_exceptional_support', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL),
	(NULL, '#5c0069', '1e4240db0d7790479ea799944d135fec', 'absence_type_urgent_childcare', 'absence_type_urgent_childcare', NULL, 'W', 0, 0, 1, 1, 0, 2, 1, 2, NULL);

INSERT INTO `dictionary` (`row_id`, `module`, `valid`, `lang`, `dict_id`, `dict_value`) VALUES
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_unpaid_leave', 'Unpaid leave'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_unpaid_leave', 'Unpaid leave'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_suspension_for_punishment', 'Suspension for punishment'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_suspension_for_punishment', 'Suspension for punishment'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_contract_suspension', 'Contract suspension'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_contract_suspension', 'Contract suspension'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_vacation', 'Vacation'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_vacation', 'Vacation'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_previous_year_holidays', 'Previous year holidays'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_previous_year_holidays', 'Previous year holidays'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_leaving_in_service', 'Leaving in Service'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_leaving_in_service', 'Leaving in Service'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_missing_strike', 'Missing strike'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_missing_strike', 'Missing strike'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_electoral_law', 'Electoral law'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_electoral_law', 'Electoral law'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_union', 'Union'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_union', 'Union'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_prolonged_absence', 'Prolonged absence'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_prolonged_absence', 'Prolonged absence'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_illness_day', 'Illness Day'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_illness_day', 'Illness Day'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_illness_hour', 'Illness Hour'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_illness_hour', 'Illness Hour'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_insurance_leave', 'Insurance Leave'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_insurance_leave', 'Insurance Leave'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_medical_leave', 'Medical Leave'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_medical_leave', 'Medical Leave'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_wedding', 'Wedding'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_wedding', 'Wedding'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_pregnancy', 'Pregnancy'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_pregnancy', 'Pregnancy'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_retirement_for_childcare', 'Retirement for childcare (daycare for children)'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_retirement_for_childcare', 'Retirement for childcare (daycare for children)'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_mourning', 'Mourning'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_mourning', 'Mourning'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_birth', 'Birth'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_birth', 'Birth'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_exceptional_support', 'Exceptional support Art.23'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_exceptional_support', 'Exceptional support Art.23'),
	(NULL, 'ttwa-ahp', '1', 'hu', 'absence_type_urgent_childcare', 'Urgent childcare (childcare assistance)'),
	(NULL, 'ttwa-ahp', '1', 'en', 'absence_type_urgent_childcare', 'Urgent childcare (childcare assistance)');

INSERT INTO `public_holiday` (`holidaydate`, `country`, `name_dict_id`, `name`, `type`, `chdate`, `company_id`, `status`, `created_by`, `created_on`) VALUES
	('2020-01-01', 'ru', 'public_holiday_hun_newyear', 'New Years Holiday', '3', NULL, 'ALL', 2, 'SN-DEV-6222', '2020-10-07'),
	('2020-01-02', 'ru', 'public_holiday_hun_newyear', 'New Years Holiday', '3', NULL, 'ALL', 2, 'SN-DEV-6222', '2020-10-07'),
	('2020-01-03', 'ru', 'public_holiday_hun_newyear', 'New Years Holiday', '3', NULL, 'ALL', 2, 'SN-DEV-6222', '2020-10-07'),
	('2020-01-06', 'ru', 'public_holiday_hun_newyear', 'New Years Holiday', '3', NULL, 'ALL', 2, 'SN-DEV-6222', '2020-10-07'),
	('2020-01-07', 'ru', 'public_holiday_ru_orthodox_christmas_day', 'Orthodox Christmas Day', '3', NULL, 'ALL', 2, 'SN-DEV-6222', '2020-10-07'),
	('2020-02-23', 'ru', 'public_holiday_ru_defender_of_fatherland_day', 'Defender of Fatherland Day', '3', NULL, 'ALL', 2, 'SN-DEV-6222', '2020-10-07'),
	('2020-02-24', 'ru', 'public_holiday_ru_defender_of_fatherland_holiday', 'Defender of Fatherland Holiday', '3', NULL, 'ALL', 2, 'SN-DEV-6222', '2020-10-07'),
	('2020-03-08', 'ru', 'public_holiday_ru_international_womens_day', 'International Womens Day', '3', NULL, 'ALL', 2, 'SN-DEV-6222', '2020-10-07'),
	('2020-03-09', 'ru', 'public_holiday_ru_international_womens_day_holiday', 'International Womens Day Holiday', '3', NULL, 'ALL', 2, 'SN-DEV-6222', '2020-10-07'),
	('2020-05-01', 'ru', 'public_holiday_ru_spring_and_labour_day', 'Spring and Labour Day', '3', NULL, 'ALL', 2, 'SN-DEV-6222', '2020-10-07'),
	('2020-05-09', 'ru', 'public_holiday_ru_victory_day', 'Victory Day', '3', NULL, 'ALL', 2, 'SN-DEV-6222', '2020-10-07'),
	('2020-05-11', 'ru', 'public_holiday_ru_victory_day_holiday', 'Victory Day Holiday', '3', NULL, 'ALL', 2, 'SN-DEV-6222', '2020-10-07'),
	('2020-06-12', 'ru', 'public_holiday_ru_day_of_russia', 'Day of Russia', '3', NULL, 'ALL', 2, 'SN-DEV-6222', '2020-10-07'),
	('2020-11-04', 'ru', 'public_holiday_ru_national_unity_day', 'National Unity Day', '3', NULL, 'ALL', 2, 'SN-DEV-6222', '2020-10-07');

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('ru', 'ttwa-base', 'public_holiday_hun_newyear', 'New Years Holiday', '1'),
	('en', 'ttwa-base', 'public_holiday_ru_orthodox_christmas_day', 'Orthodox Christmas Day', '1'),
	('ru', 'ttwa-base', 'public_holiday_ru_orthodox_christmas_day', 'Orthodox Christmas Day', '1'),
	('hu', 'ttwa-base', 'public_holiday_ru_orthodox_christmas_day', 'Orthodox Christmas Day', '1'),
	('en', 'ttwa-base', 'public_holiday_ru_defender_of_fatherland_day', 'Defender of Fatherland Day', '1'),
	('ru', 'ttwa-base', 'public_holiday_ru_defender_of_fatherland_day', 'Defender of Fatherland Day', '1'),
	('hu', 'ttwa-base', 'public_holiday_ru_defender_of_fatherland_day', 'Defender of Fatherland Day', '1'),
	('en', 'ttwa-base', 'public_holiday_ru_defender_of_fatherland_holiday', 'Defender of Fatherland Holiday', '1'),
	('ru', 'ttwa-base', 'public_holiday_ru_defender_of_fatherland_holiday', 'Defender of Fatherland Holiday', '1'),
	('hu', 'ttwa-base', 'public_holiday_ru_defender_of_fatherland_holiday', 'Defender of Fatherland Holiday', '1'),
	('en', 'ttwa-base', 'public_holiday_ru_international_womens_day', 'International Womens Day', '1'),
	('ru', 'ttwa-base', 'public_holiday_ru_international_womens_day', 'International Womens Day', '1'),
	('hu', 'ttwa-base', 'public_holiday_ru_international_womens_day', 'International Womens Day', '1'),
	('en', 'ttwa-base', 'public_holiday_ru_international_womens_day_holiday', 'International Womens Day Holiday', '1'),
	('ru', 'ttwa-base', 'public_holiday_ru_international_womens_day_holiday', 'International Womens Day Holiday', '1'),
	('hu', 'ttwa-base', 'public_holiday_ru_international_womens_day_holiday', 'International Womens Day Holiday', '1'),
	('en', 'ttwa-base', 'public_holiday_ru_spring_and_labour_day', 'Spring and Labour Day', '1'),
	('ru', 'ttwa-base', 'public_holiday_ru_spring_and_labour_day', 'Spring and Labour Day', '1'),
	('hu', 'ttwa-base', 'public_holiday_ru_spring_and_labour_day', 'Spring and Labour Day', '1'),
	('en', 'ttwa-base', 'public_holiday_ru_victory_day', 'Victory Day', '1'),
	('ru', 'ttwa-base', 'public_holiday_ru_victory_day', 'Victory Day', '1'),
	('hu', 'ttwa-base', 'public_holiday_ru_victory_day', 'Victory Day', '1'),
	('en', 'ttwa-base', 'public_holiday_ru_victory_day_holiday', 'Victory Day Holiday', '1'),
	('ru', 'ttwa-base', 'public_holiday_ru_victory_day_holiday', 'Victory Day Holiday', '1'),
	('hu', 'ttwa-base', 'public_holiday_ru_victory_day_holiday', 'Victory Day Holiday', '1'),
	('en', 'ttwa-base', 'public_holiday_ru_day_of_russia', 'Day of Russia', '1'),
	('ru', 'ttwa-base', 'public_holiday_ru_day_of_russia', 'Day of Russia', '1'),
	('hu', 'ttwa-base', 'public_holiday_ru_day_of_russia', 'Day of Russia', '1'),
	('en', 'ttwa-base', 'public_holiday_ru_national_unity_day', 'National Unity Day', '1'),
	('ru', 'ttwa-base', 'public_holiday_ru_national_unity_day', 'National Unity Day', '1'),
	('hu', 'ttwa-base', 'public_holiday_ru_national_unity_day', 'National Unity Day', '1');

UPDATE `_sql_version` SET `revision`=3, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -3--2020.08.14.-16:15----------------------------------------------------------------

UPDATE dictionary SET dict_value = 'Accident on the Route' WHERE dict_id = 'absence_type_travel_accident' AND lang = 'en';
UPDATE dictionary SET dict_value = 'Death' WHERE dict_id = 'absence_type_funeralleave' AND lang = 'en';
UPDATE dictionary SET dict_value = 'Óbito' WHERE dict_id = 'absence_type_funeralleave' AND lang = 'pt';
UPDATE dictionary SET dict_value = 'Student' WHERE dict_id = 'absence_type_studyleave' AND lang = 'en';
UPDATE dictionary SET dict_value = 'Estudante' WHERE dict_id = 'absence_type_studyleave' AND lang = 'pt';
UPDATE dictionary SET dict_value = 'Unpaid absences (Day)' WHERE dict_id = 'absence_type_unjustifiedabsence' AND lang = 'en';
UPDATE dictionary SET dict_value = 'Faltas não remuneradas (Dia)' WHERE dict_id = 'absence_type_unjustifiedabsence' AND lang = 'pt';
UPDATE dictionary SET dict_value = 'Paid absences (Day)' WHERE dict_id = 'absence_type_justifiedpaid' AND lang = 'en';
UPDATE dictionary SET dict_value = 'Faltas remuneradas (Dia)' WHERE dict_id = 'absence_type_justifiedpaid' AND lang = 'pt';
UPDATE dictionary SET dict_value = 'Paid illness (Day)' WHERE dict_id = 'absence_type_sickpay' AND lang = 'en';
UPDATE dictionary SET dict_value = 'Doença remuneradas (Dia)' WHERE dict_id = 'absence_type_sickpay' AND lang = 'pt';


INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES
('pt', 'ttwa-ahp', 'absence_type_travel_accident', 'Acidente no Trajeto'),
('pt', 'ttwa-ahp-core', 'absence_type_travel_accident', 'Acidente no Trajeto'),
('pt', 'ttwa-ahp', 'absence_type_illness_prev_month', 'Doença mes anterior (Dia)'),
('pt', 'ttwa-ahp-core', 'absence_type_illness_prev_month', 'Doença mes anterior (Dia)'),
('en', 'ttwa-ahp', 'absence_type_illness_prev_month', 'Illness Day - previous month'),
('en', 'ttwa-ahp-core', 'absence_type_illness_prev_month', 'Illness Day - previous month'),
('pt', 'ttwa-ahp', 'absence_type_marriage', 'Casamento'),
('pt', 'ttwa-ahp-core', 'absence_type_marriage', 'Casamento'),
('en', 'ttwa-ahp', 'absence_type_marriage', 'Marriage'),
('en', 'ttwa-ahp-core', 'absence_type_marriage', 'Marriage'),
('pt', 'ttwa-ahp', 'absence_type_cert_prof_disease', 'Doença Profissiona certificada'),
('pt', 'ttwa-ahp-core', 'absence_type_cert_prof_disease', 'Doença Profissiona certificada'),
('en', 'ttwa-ahp', 'absence_type_cert_prof_disease', 'Certified Professional Disease'),
('en', 'ttwa-ahp-core', 'absence_type_cert_prof_disease', 'Certified Professional Disease'),
('pt', 'ttwa-ahp', 'absence_type_non_prof_disease', 'Doença nao Profissional'),
('pt', 'ttwa-ahp-core', 'absence_type_non_prof_disease', 'Doença nao Profissional'),
('en', 'ttwa-ahp', 'absence_type_non_prof_disease', 'Non-Professional Disease'),
('en', 'ttwa-ahp-core', 'absence_type_non_prof_disease', 'Non-Professional Disease'),
('pt', 'ttwa-ahp', 'absence_type_notcert_prof_disease', 'Doença Profissional não certif'),
('pt', 'ttwa-ahp-core', 'absence_type_notcert_prof_disease', 'Doença Profissional não certif'),
('en', 'ttwa-ahp', 'absence_type_notcert_prof_disease', 'Professional disease not certified'),
('en', 'ttwa-ahp-core', 'absence_type_notcert_prof_disease', 'Professional disease not certified'),
('pt', 'ttwa-ahp', 'absence_type_vacation', 'Férias'),
('pt', 'ttwa-ahp-core', 'absence_type_vacation', 'Férias'),
('en', 'ttwa-ahp-core', 'absence_type_vacation', 'Vacation'),
('pt', 'ttwa-ahp', 'absence_type_paid_family_assistance', 'Assistência Familia Remunerada'),
('pt', 'ttwa-ahp', 'absence_type_non_family_assistance', 'Assistênc Familia Não Remuner'),
('pt', 'ttwa-ahp', 'absence_type_family_death', 'Óbito Familiar'),
('pt', 'ttwa-ahp', 'absence_type_formation', 'Formação'),
('pt', 'ttwa-ahp', 'absence_type_disc_suspension', 'Falta p/ suspensão disciplinar'),
('pt', 'ttwa-ahp', 'absence_type_strike', 'Greve'),
('pt', 'ttwa-ahp', 'absence_type_low_risk_pregnancy', 'Gravidez de baixo risco'),
('pt', 'ttwa-ahp', 'absence_type_maternity', 'Maternidade'),
('pt', 'ttwa-ahp', 'absence_type_prenatal_consultation', 'Consulta Pré-Natal'),
('pt', 'ttwa-ahp', 'absence_type_lactation', 'Amamentação/Aleitação'),
('pt', 'ttwa-ahp', 'absence_type_parenting', 'Parentalidade'),
('pt', 'ttwa-ahp', 'absence_type_risk_pregnancy', 'Licença por gravidez de risco'),
('pt', 'ttwa-ahp', 'absence_type_interrupt_pregnancy', 'Licença p/ interrup. gravidez'),
('pt', 'ttwa-ahp', 'absence_type_legal_reduction', 'Por redução legal actividade'),
('pt', 'ttwa-ahp', 'absence_type_food_allowance', 'Subsidio de alimentação'),
('pt', 'ttwa-ahp-core', 'absence_type_paid_family_assistance', 'Assistência Familia Remunerada'),
('pt', 'ttwa-ahp-core', 'absence_type_non_family_assistance', 'Assistênc Familia Não Remuner'),
('pt', 'ttwa-ahp-core', 'absence_type_family_death', 'Óbito Familiar'),
('pt', 'ttwa-ahp-core', 'absence_type_formation', 'Formação'),
('pt', 'ttwa-ahp-core', 'absence_type_disc_suspension', 'Falta p/ suspensão disciplinar'),
('pt', 'ttwa-ahp-core', 'absence_type_strike', 'Greve'),
('pt', 'ttwa-ahp-core', 'absence_type_low_risk_pregnancy', 'Gravidez de baixo risco'),
('pt', 'ttwa-ahp-core', 'absence_type_maternity', 'Maternidade'),
('pt', 'ttwa-ahp-core', 'absence_type_prenatal_consultation', 'Consulta Pré-Natal'),
('pt', 'ttwa-ahp-core', 'absence_type_lactation', 'Amamentação/Aleitação'),
('pt', 'ttwa-ahp-core', 'absence_type_parenting', 'Parentalidade'),
('pt', 'ttwa-ahp-core', 'absence_type_risk_pregnancy', 'Licença por gravidez de risco'),
('pt', 'ttwa-ahp-core', 'absence_type_interrupt_pregnancy', 'Licença p/ interrup. gravidez'),
('pt', 'ttwa-ahp-core', 'absence_type_legal_reduction', 'Por redução legal actividade'),
('pt', 'ttwa-ahp-core', 'absence_type_food_allowance', 'Subsidio de alimentação'),
('en', 'ttwa-ahp-core', 'absence_type_paid_family_assistance', 'Paid Family Assistance'),
('en', 'ttwa-ahp-core', 'absence_type_non_family_assistance', 'Non-Remunerated Family Assistance'),
('en', 'ttwa-ahp-core', 'absence_type_family_death', 'Family Death'),
('en', 'ttwa-ahp-core', 'absence_type_formation', 'Formation'),
('en', 'ttwa-ahp-core', 'absence_type_disc_suspension', 'Absence for disciplinary suspension'),
('en', 'ttwa-ahp-core', 'absence_type_strike', 'Strike'),
('en', 'ttwa-ahp-core', 'absence_type_low_risk_pregnancy', 'Low Risk Pregnancy'),
('en', 'ttwa-ahp-core', 'absence_type_maternity', 'Maternity'),
('en', 'ttwa-ahp-core', 'absence_type_prenatal_consultation', 'Prenatal Consultation'),
('en', 'ttwa-ahp-core', 'absence_type_lactation', 'Breastfeeding / Lactation'),
('en', 'ttwa-ahp-core', 'absence_type_parenting', 'Parenting'),
('en', 'ttwa-ahp-core', 'absence_type_risk_pregnancy', 'At-risk pregnancy leave'),
('en', 'ttwa-ahp-core', 'absence_type_interrupt_pregnancy', 'License to interrupt pregnancy'),
('en', 'ttwa-ahp-core', 'absence_type_legal_reduction', 'By legal reduction of activity'),
('en', 'ttwa-ahp-core', 'absence_type_food_allowance', 'Food allowance'),
('en', 'ttwa-ahp', 'absence_type_paid_family_assistance', 'Paid Family Assistance'),
('en', 'ttwa-ahp', 'absence_type_non_family_assistance', 'Non-Remunerated Family Assistance'),
('en', 'ttwa-ahp', 'absence_type_family_death', 'Family Death'),
('en', 'ttwa-ahp', 'absence_type_formation', 'Formation'),
('en', 'ttwa-ahp', 'absence_type_disc_suspension', 'Absence for disciplinary suspension'),
('en', 'ttwa-ahp', 'absence_type_strike', 'Strike'),
('en', 'ttwa-ahp', 'absence_type_low_risk_pregnancy', 'Low Risk Pregnancy'),
('en', 'ttwa-ahp', 'absence_type_maternity', 'Maternity'),
('en', 'ttwa-ahp', 'absence_type_prenatal_consultation', 'Prenatal Consultation'),
('en', 'ttwa-ahp', 'absence_type_lactation', 'Breastfeeding / Lactation'),
('en', 'ttwa-ahp', 'absence_type_parenting', 'Parenting'),
('en', 'ttwa-ahp', 'absence_type_risk_pregnancy', 'At-risk pregnancy leave'),
('en', 'ttwa-ahp', 'absence_type_interrupt_pregnancy', 'License to interrupt pregnancy'),
('en', 'ttwa-ahp', 'absence_type_legal_reduction', 'By legal reduction of activity'),
('en', 'ttwa-ahp', 'absence_type_food_allowance', 'Food allowance');

INSERT INTO state_type (state_type_id, name_dict_id, bgcolor, status) VALUES
('absence_type_illness_prev_month', 'absence_type_illness_prev_month', '#ff7043', 2),
('absence_type_marriage', 'absence_type_marriage', '#ff7043', 2),
('absence_type_cert_prof_disease', 'absence_type_cert_prof_disease', '#ff7043', 2),
('absence_type_non_prof_disease', 'absence_type_non_prof_disease', '#ff7043', 2),
('absence_type_notcert_prof_disease', 'absence_type_notcert_prof_disease', '#ff7043', 2),
('absence_type_paid_family_assistance', 'absence_type_paid_family_assistance', '#ff7043', 2),
('absence_type_non_family_assistance', 'absence_type_non_family_assistance', '#ff7043', 2),
('absence_type_family_death', 'absence_type_family_death', '#ff7043', 2),
('absence_type_formation', 'absence_type_formation', '#ff7043', 2),
('absence_type_disc_suspension', 'absence_type_disc_suspension', '#ff7043', 2),
('absence_type_strike', 'absence_type_strike', '#ff7043', 2),
('absence_type_low_risk_pregnancy', 'absence_type_low_risk_pregnancy', '#ff7043', 2),
('absence_type_maternity', 'absence_type_maternity', '#ff7043', 2),
('absence_type_prenatal_consultation', 'absence_type_prenatal_consultation', '#ff7043', 2),
('absence_type_lactation', 'absence_type_lactation', '#ff7043', 2),
('absence_type_parenting', 'absence_type_parenting', '#ff7043', 2),
('absence_type_risk_pregnancy', 'absence_type_risk_pregnancy', '#ff7043', 2),
('absence_type_interrupt_pregnancy', 'absence_type_interrupt_pregnancy', '#ff7043', 2),
('absence_type_legal_reduction', 'absence_type_legal_reduction', '#ff7043', 2),
('absence_type_food_allowance', 'absence_type_food_allowance', '#ff7043', 2);

UPDATE `_sql_version` SET `revision`=4, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -4--2020.09.23.-13:45----------------------------------------------------------------

INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES
('pt', 'ttwa-ahp', 'absence_type_paid_illness_hours', 'Acidente no Trajeto'),
('en', 'ttwa-ahp', 'absence_type_paid_illness_hours', 'Paid illness (hours)'),
('pt', 'ttwa-ahp-core', 'absence_type_paid_illness_hours', 'Acidente no Trajeto'),
('en', 'ttwa-ahp-core', 'absence_type_paid_illness_hours', 'Paid illness (hours)'),
('pt', 'ttwa-ahp', 'absence_type_non_rem_family_ass_hours', 'Assis.Familia ñ Remuner Horas'),
('en', 'ttwa-ahp', 'absence_type_non_rem_family_ass_hours', 'Non-Remunerated Family Assistance Hours'),
('pt', 'ttwa-ahp-core', 'absence_type_non_rem_family_ass_hours', 'Assis.Familia ñ Remuner Horas'),
('en', 'ttwa-ahp-core', 'absence_type_non_rem_family_ass_hours', 'Non-Remunerated Family Assistance Hours'),
('pt', 'ttwa-ahp-core', 'absence_type_rem_family_ass_hours', 'Assis.Familia Remuner Horas'),
('en', 'ttwa-ahp-core', 'absence_type_rem_family_ass_hours', 'Remunerated Family Assistance Hours'),
('pt', 'ttwa-ahp', 'absence_type_rem_family_ass_hours', 'Assis.Familia Remuner Horas'),
('en', 'ttwa-ahp', 'absence_type_rem_family_ass_hours', 'Remunerated Family Assistance Hours'),
('pt', 'ttwa-ahp', 'absence_type_unpaid_hours', 'Faltas não remuneradas (Horas)'),
('en', 'ttwa-ahp', 'absence_type_unpaid_hours', 'Unpaid absences (Hours)'),
('pt', 'ttwa-ahp-core', 'absence_type_unpaid_hours', 'Faltas não remuneradas (Horas)'),
('en', 'ttwa-ahp-core', 'absence_type_unpaid_hours', 'Unpaid absences (Hours)'),
('pt', 'ttwa-ahp-core', 'absence_type_paid_hours', 'Faltas remuneradas (Horas)'),
('en', 'ttwa-ahp-core', 'absence_type_paid_hours', 'Paid absences (Hours)'),
('pt', 'ttwa-ahp', 'absence_type_paid_hours', 'Faltas remuneradas (Horas)'),
('en', 'ttwa-ahp', 'absence_type_paid_hours', 'Paid absences (Hours)');

INSERT INTO state_type (state_type_id, name_dict_id, bgcolor, status, absence_hour) VALUES
('absence_type_paid_illness_h', 'absence_type_paid_illness_hours', '#ff7043', 2, 1),
('absence_type_non_rem_fam_ass_h', 'absence_type_non_rem_family_ass_hours', '#ff7043', 2, 1),
('absence_type_rem_fam_ass_h', 'absence_type_rem_family_ass_hours', '#ff7043', 2, 1),
('absence_type_unpaid_h', 'absence_type_unpaid_hours', '#ff7043', 2, 1),
('absence_type_paid_h', 'absence_type_paid_hours', '#ff7043', 2, 1);

UPDATE `_sql_version` SET `revision`=5, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -5--2020.10.01.-09:05----------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = 'ON_WITHOUTCOST', `note` = 'DEV-6055, prev value: OFF' WHERE `setting_id` = 'summarySheet_rule_regs_no_cause';
UPDATE `app_settings` SET `setting_value` = '60', `note` = 'DEV-6055, prev value: 0' WHERE `setting_id` = 'summarySheet_rule_regsFilterMinTimeBetweenRegs';

UPDATE `_sql_version` SET `revision`=6, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -6--2020.10.06.-11:20----------------------------------------------------------------

UPDATE state_type SET short_name_dict_id = 'absence_type_short_paid_illness_h' WHERE state_type_id = 'absence_type_paid_illness_h';
UPDATE state_type SET short_name_dict_id = 'absence_type_short_illness_hour' WHERE state_type_id = 'f9080b5f1412afd4e5efcb893b6ca949';
UPDATE state_type SET short_name_dict_id = 'absence_type_short_non_fam_ass_h' WHERE state_type_id = 'absence_type_non_rem_fam_ass_h';
UPDATE state_type SET short_name_dict_id = 'absence_type_short_rem_fam_ass_h' WHERE state_type_id = 'absence_type_rem_fam_ass_h';
UPDATE state_type SET short_name_dict_id = 'absence_type_short_unpaid_h' WHERE state_type_id = 'absence_type_unpaid_h';
UPDATE state_type SET short_name_dict_id = 'absence_type_short_formation' WHERE state_type_id = 'absence_type_formation';
UPDATE state_type SET short_name_dict_id = 'absence_type_short_paid_h' WHERE state_type_id = 'absence_type_paid_h';
UPDATE state_type SET short_name_dict_id = 'absence_type_short_prenatal_con' WHERE state_type_id = 'absence_type_prenatal_consultation';
UPDATE state_type SET short_name_dict_id = 'absence_type_short_lactation' WHERE state_type_id = 'absence_type_lactation';

INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES
('pt', 'ttwa-ahp', 'absence_type_short_paid_illness_h', 'DOERH'),
('pt', 'ttwa-ahp-core', 'absence_type_short_paid_illness_h', 'DOERH'),
('en', 'ttwa-ahp', 'absence_type_short_paid_illness_h', 'DOERH'),
('en', 'ttwa-ahp-core', 'absence_type_short_paid_illness_h', 'DOERH'),
('pt', 'ttwa-ahp', 'absence_type_short_illness_hour', 'DOMAH'),
('pt', 'ttwa-ahp-core', 'absence_type_short_illness_hour', 'DOMAH'),
('en', 'ttwa-ahp', 'absence_type_short_illness_hour', 'DOMAH'),
('en', 'ttwa-ahp-core', 'absence_type_short_illness_hour', 'DOMAH'),
('pt', 'ttwa-ahp', 'absence_type_short_non_fam_ass_h', 'FAFNH'),
('pt', 'ttwa-ahp-core', 'absence_type_short_non_fam_ass_h', 'FAFNH'),
('en', 'ttwa-ahp', 'absence_type_short_non_fam_ass_h', 'FAFNH'),
('en', 'ttwa-ahp-core', 'absence_type_short_non_fam_ass_h', 'FAFNH'),
('pt', 'ttwa-ahp', 'absence_type_short_rem_fam_ass_h', 'FAFRH'),
('pt', 'ttwa-ahp-core', 'absence_type_short_rem_fam_ass_h', 'FAFRH'),
('en', 'ttwa-ahp', 'absence_type_short_rem_fam_ass_h', 'FAFRH'),
('en', 'ttwa-ahp-core', 'absence_type_short_rem_fam_ass_h', 'FAFRH'),
('pt', 'ttwa-ahp', 'absence_type_short_unpaid_h', 'FNRH'),
('pt', 'ttwa-ahp-core', 'absence_type_short_unpaid_h', 'FNRH'),
('en', 'ttwa-ahp', 'absence_type_short_unpaid_h', 'FNRH'),
('en', 'ttwa-ahp-core', 'absence_type_short_unpaid_h', 'FNRH'),
('pt', 'ttwa-ahp', 'absence_type_short_formation', 'FOR'),
('pt', 'ttwa-ahp-core', 'absence_type_short_formation', 'FOR'),
('en', 'ttwa-ahp', 'absence_type_short_formation', 'FOR'),
('en', 'ttwa-ahp-core', 'absence_type_short_formation', 'FOR'),
('pt', 'ttwa-ahp', 'absence_type_short_paid_h', 'FRH'),
('pt', 'ttwa-ahp-core', 'absence_type_short_paid_h', 'FRH'),
('en', 'ttwa-ahp', 'absence_type_short_paid_h', 'FRH'),
('en', 'ttwa-ahp-core', 'absence_type_short_paid_h', 'FRH'),
('pt', 'ttwa-ahp', 'absence_type_short_prenatal_con', 'PACN'),
('pt', 'ttwa-ahp-core', 'absence_type_short_prenatal_con', 'PACN'),
('en', 'ttwa-ahp', 'absence_type_short_prenatal_con', 'PACN'),
('en', 'ttwa-ahp-core', 'absence_type_short_prenatal_con', 'PACN'),
('pt', 'ttwa-ahp', 'absence_type_short_lactation', 'PAMA'),
('pt', 'ttwa-ahp-core', 'absence_type_short_lactation', 'PAMA'),
('en', 'ttwa-ahp', 'absence_type_short_lactation', 'PAMA'),
('en', 'ttwa-ahp-core', 'absence_type_short_lactation', 'PAMA');

UPDATE app_settings SET setting_value = '1' WHERE setting_id = 'absence_calculation_hour';
UPDATE app_settings SET setting_value = '1' WHERE setting_id = 'showAbsencesInHours';

UPDATE `_sql_version` SET `revision`=7, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -7--2020.10.12.-09:20----------------------------------------------------------------

UPDATE dictionary SET dict_value = REPLACE(dict_value, 'csoportositas3', 'kozvetlen_vezeto');
UPDATE dictionary SET dict_value = REPLACE(dict_value, 'Csoportosítás 3', 'Közvetlen vezető');
UPDATE dictionary SET dict_value = REPLACE(dict_value, 'csoport 3-at', 'Közvetlen vezető-t');
UPDATE dictionary SET dict_value = REPLACE(dict_value, 'Csoport 3', 'Közvetlen vezető');

UPDATE dictionary SET dict_value = REPLACE(dict_value, 'Group 3', 'Direct Manager');
UPDATE dictionary SET dict_value = REPLACE(dict_value, 'Grouping 3', 'Direct Manager');
UPDATE dictionary SET dict_value = REPLACE(dict_value, 'grouping3', 'direct_manager');
UPDATE dictionary SET dict_value = REPLACE(dict_value, 'group 3', 'Direct Manager');

UPDATE dictionary SET dict_value = 'Direct Manager' WHERE lang = 'en' AND dict_id IN('menu_item_company_org_group3_upload', 'page_title_company_org_group3_upload');

UPDATE `_sql_version` SET `revision`=8, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -8--2020.10.13.-15:58----------------------------------------------------------------

UPDATE dictionary SET dict_value = 'Többszöri hibás bejelentkezés miatt egy órára blokkoltuk a felhasználót.'
	WHERE lang = 'hu' AND dict_id = 'error_user_disabled';
UPDATE dictionary SET dict_value = 'Too many bad login attempts. Please try again in 60 minutes.'
	WHERE lang = 'en' AND dict_id = 'error_user_disabled';

INSERT INTO dictionary (lang, module, dict_id, dict_value, valid) VALUES
('pt', 'ttwa-base', 'error_user_disabled', 'Muitas tentativas de login, tente novamente em 60 minutos.', 1);

UPDATE `_sql_version` SET `revision`=9, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -9--2020.10.14.-13:30----------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Direct Manager' WHERE `dict_value` = 'Direct Manager.';
UPDATE `dictionary` SET `dict_value` = 'Közvetlen vezető' WHERE `dict_value` = 'Közvetlen vezető.';

UPDATE `_sql_version` SET `revision`=10, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -10--2020.10.21.-14:20----------------------------------------------------------------

INSERT INTO `public_holiday` (`holidaydate`, `country`, `name_dict_id`, `name`, `type`, `company_id`, `status`, `created_by`, `created_on`) VALUES
	("2020-01-01", "nl", "public_holiday_new_years_day_nl", "New Year's Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-04-10", "nl", "public_holiday_good_friday_nl", "Good Friday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-04-12", "nl", "public_holiday_easter_nl", "Easter", "3", "ALL", "2", "Sándor", NOW()),
	("2020-04-13", "nl", "public_holiday_easter_monday_nl", "Easter Monday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-04-25", "nl", "public_holiday_kings_birthday_nl", "King's Birthday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-05", "nl", "public_holiday_liberation_day_nl", "Liberation Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-21", "nl", "public_holiday_ascension_day_nl", "Ascension Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-31", "nl", "public_holiday_pentecost_sunday_nl", "Pentecost Sunday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-06-01", "nl", "public_holiday_whit_monday_nl", "Whit Monday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-12-25", "nl", "public_holiday_christmas_day_nl", "Christmas Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-12-26", "nl", "public_holiday_st_stephens_day_nl", "St. Stephen's Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-01-01", "ukr", "public_holiday_new_years_day_ukr", "New Year's Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-01-07", "ukr", "public_holiday_orthodox_christmas_day_ukr", "Orthodox Christmas Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-03-08", "ukr", "public_holiday_womens_day_ukr", "Women's Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-03-09", "ukr", "public_holiday_womens_day_holiday_ukr", "Women's Day Holiday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-04-19", "ukr", "public_holiday_orthodox_easter_sunday_ukr", "Orthodox Easter Sunday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-04-20", "ukr", "public_holiday_orthodox_easter_monday_ukr", "Orthodox Easter Monday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-01", "ukr", "public_holiday_labor_day_ukr", "Labor Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-09", "ukr", "public_holiday_victory_day_ukr", "Victory Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-11", "ukr", "public_holiday_victory_day_holiday_ukr", "Victory Day Holiday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-06-07", "ukr", "public_holiday_orthodox_whit_sunday_ukr", "Orthodox Whit Sunday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-06-08", "ukr", "public_holiday_orthodox_whit_monday_ukr", "Orthodox Whit Monday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-06-28", "ukr", "public_holiday_constitution_day_ukr", "Constitution Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-06-29", "ukr", "public_holiday_constitution_day_holiday_ukr", "Constitution Day Holiday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-08-24", "ukr", "public_holiday_independence_day_ukr", "Independence Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-10-14", "ukr", "public_holiday_defenders_day_ukr", "Defender's Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-12-25", "ukr", "public_holiday_catholic_christmas_day_ukr", "Catholic Christmas Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-01-01", "mold", "public_holiday_new_years_day_mold", "New Year's Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-01-07", "mold", "public_holiday_orthodox_christmas_day_mold", "Orthodox Christmas Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-01-08", "mold", "public_holiday_orthodox_christmas_holiday_mold", "Orthodox Christmas Holiday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-03-08", "mold", "public_holiday_international_womens_day_mold", "International Women's Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-04-19", "mold", "public_holiday_orthodox_easter_day_mold", "Orthodox Easter Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-04-20", "mold", "public_holiday_orthodox_easter_monday_mold", "Orthodox Easter Monday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-04-27", "mold", "public_holiday_memorial_easter_mold", "Memorial Easter", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-01", "mold", "public_holiday_labor_day_mold", "Labor Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-09", "mold", "public_holiday_victory_day_mold", "Victory Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-06-01", "mold", "public_holiday_international_childrens_day_mold", "International Children's Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-08-27", "mold", "public_holiday_independence_day_mold", "Independence Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-08-31", "mold", "public_holiday_national_language_day_mold", "National Language Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-10-14", "mold", "public_holiday_chisinau_city_day_mold", "Chisinau City Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-12-25", "mold", "public_holiday_christmas_day_mold", "Christmas Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-01-01", "azer", "public_holiday_new_year_holiday_1_azer", "New Year Holiday 1", "3", "ALL", "2", "Sándor", NOW()),
	("2020-01-02", "azer", "public_holiday_new_year_holiday_2_azer", "New Year Holiday 2", "3", "ALL", "2", "Sándor", NOW()),
	("2020-03-08", "azer", "public_holiday_international_womens_day_azer", "International Women's Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-03-09", "azer", "public_holiday_international_womens_day_holiday_azer", "International Women's Day Holiday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-03-20", "azer", "public_holiday_novruz_holiday_1_azer", "Novruz Holiday 1", "3", "ALL", "2", "Sándor", NOW()),
	("2020-03-21", "azer", "public_holiday_novruz_holiday_2_azer", "Novruz Holiday 2", "3", "ALL", "2", "Sándor", NOW()),
	("2020-03-22", "azer", "public_holiday_novruz_holiday_3_azer", "Novruz Holiday 3", "3", "ALL", "2", "Sándor", NOW()),
	("2020-03-23", "azer", "public_holiday_novruz_holiday_4_azer", "Novruz Holiday 4", "3", "ALL", "2", "Sándor", NOW()),
	("2020-03-24", "azer", "public_holiday_novruz_holiday_5_azer", "Novruz Holiday 5", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-09", "azer", "public_holiday_victory_day_azer", "Victory Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-11", "azer", "public_holiday_victory_day_holiday_azer", "Victory Day Holiday", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-24", "azer", "public_holiday_ramazan_bayram_holiday_1_azer", "Ramazan Bayram Holiday 1", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-25", "azer", "public_holiday_ramazan_bayram_holiday_2_azer", "Ramazan Bayram Holiday 2", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-26", "azer", "public_holiday_ramazan_bayram_holiday_3_azer", "Ramazan Bayram Holiday 3", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-28", "azer", "public_holiday_republic_day_azer", "Republic Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-06-15", "azer", "public_holiday_salvation_day_azer", "Salvation Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-06-26", "azer", "public_holiday_armed_forces_day_azer", "Armed Forces Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-07-31", "azer", "public_holiday_gurban_bayram_holiday_1_azer", "Gurban Bayram Holiday 1", "3", "ALL", "2", "Sándor", NOW()),
	("2020-08-01", "azer", "public_holiday_gurban_bayram_holiday_2_azer", "Gurban Bayram Holiday 2", "3", "ALL", "2", "Sándor", NOW()),
	("2020-11-09", "azer", "public_holiday_flag_day_azer", "Flag Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-12-31", "azer", "public_holiday_solidarity_day_of_azerbaijanis_azer", "Solidarity Day of Azerbaijanis", "3", "ALL", "2", "Sándor", NOW()),
	("2020-01-01", "biel", "public_holiday_new_years_day_biel", "New Year's Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-01-07", "biel", "public_holiday_orthodox_christmas_day_biel", "Orthodox Christmas Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-03-08", "biel", "public_holiday_womens_day_biel", "Women's Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-03-15", "biel", "public_holiday_constitution_day_biel", "Constitution Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-04-02", "biel", "public_holiday_union_day_of_belarus_and_russia_biel", "Union Day of Belarus and Russia", "3", "ALL", "2", "Sándor", NOW()),
	("2020-04-28", "biel", "public_holiday_radonitsa_biel", "Radonitsa", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-01", "biel", "public_holiday_labour_day_biel", "Labour Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-05-09", "biel", "public_holiday_victory_day_biel", "Victory Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-07-03", "biel", "public_holiday_independence_day_of_the_republic_of_belarus_biel", "Independence Day of the Republic of Belarus", "3", "ALL", "2", "Sándor", NOW()),
	("2020-11-07", "biel", "public_holiday_october_revolution_day_biel", "October Revolution Day", "3", "ALL", "2", "Sándor", NOW()),
	("2020-12-25", "biel", "public_holiday_catholic_christmas_day_biel", "Catholic Christmas Day", "3", "ALL", "2", "Sándor", NOW());

INSERT INTO `dictionary` (`row_id`, `lang`, `module`, `dict_id`, `dict_value`) VALUES
	(NULL, "nl", "ttwa-base", "public_holiday_new_years_day_nl", "New Year's Day"),
	(NULL, "nl", "ttwa-base", "public_holiday_good_friday_nl", "Good Friday"),
	(NULL, "nl", "ttwa-base", "public_holiday_easter_nl", "Easter"),
	(NULL, "nl", "ttwa-base", "public_holiday_easter_monday_nl", "Easter Monday"),
	(NULL, "nl", "ttwa-base", "public_holiday_kings_birthday_nl", "King's Birthday"),
	(NULL, "nl", "ttwa-base", "public_holiday_liberation_day_nl", "Liberation Day"),
	(NULL, "nl", "ttwa-base", "public_holiday_ascension_day_nl", "Ascension Day"),
	(NULL, "nl", "ttwa-base", "public_holiday_pentecost_sunday_nl", "Pentecost Sunday"),
	(NULL, "nl", "ttwa-base", "public_holiday_whit_monday_nl", "Whit Monday"),
	(NULL, "nl", "ttwa-base", "public_holiday_christmas_day_nl", "Christmas Day"),
	(NULL, "nl", "ttwa-base", "public_holiday_st_stephens_day_nl", "St. Stephen's Day"),
	(NULL, "ukr", "ttwa-base", "public_holiday_new_years_day_ukr", "New Year's Day"),
	(NULL, "ukr", "ttwa-base", "public_holiday_orthodox_christmas_day_ukr", "Orthodox Christmas Day"),
	(NULL, "ukr", "ttwa-base", "public_holiday_womens_day_ukr", "Women's Day"),
	(NULL, "ukr", "ttwa-base", "public_holiday_womens_day_holiday_ukr", "Women's Day Holiday"),
	(NULL, "ukr", "ttwa-base", "public_holiday_orthodox_easter_sunday_ukr", "Orthodox Easter Sunday"),
	(NULL, "ukr", "ttwa-base", "public_holiday_orthodox_easter_monday_ukr", "Orthodox Easter Monday"),
	(NULL, "ukr", "ttwa-base", "public_holiday_labor_day_ukr", "Labor Day"),
	(NULL, "ukr", "ttwa-base", "public_holiday_victory_day_ukr", "Victory Day"),
	(NULL, "ukr", "ttwa-base", "public_holiday_victory_day_holiday_ukr", "Victory Day Holiday"),
	(NULL, "ukr", "ttwa-base", "public_holiday_orthodox_whit_sunday_ukr", "Orthodox Whit Sunday"),
	(NULL, "ukr", "ttwa-base", "public_holiday_orthodox_whit_monday_ukr", "Orthodox Whit Monday"),
	(NULL, "ukr", "ttwa-base", "public_holiday_constitution_day_ukr", "Constitution Day"),
	(NULL, "ukr", "ttwa-base", "public_holiday_constitution_day_holiday_ukr", "Constitution Day Holiday"),
	(NULL, "ukr", "ttwa-base", "public_holiday_independence_day_ukr", "Independence Day"),
	(NULL, "ukr", "ttwa-base", "public_holiday_defenders_day_ukr", "Defender's Day"),
	(NULL, "ukr", "ttwa-base", "public_holiday_catholic_christmas_day_ukr", "Catholic Christmas Day"),
	(NULL, "mold", "ttwa-base", "public_holiday_new_years_day_mold", "New Year's Day"),
	(NULL, "mold", "ttwa-base", "public_holiday_orthodox_christmas_day_mold", "Orthodox Christmas Day"),
	(NULL, "mold", "ttwa-base", "public_holiday_orthodox_christmas_holiday_mold", "Orthodox Christmas Holiday"),
	(NULL, "mold", "ttwa-base", "public_holiday_international_womens_day_mold", "International Women's Day"),
	(NULL, "mold", "ttwa-base", "public_holiday_orthodox_easter_day_mold", "Orthodox Easter Day"),
	(NULL, "mold", "ttwa-base", "public_holiday_orthodox_easter_monday_mold", "Orthodox Easter Monday"),
	(NULL, "mold", "ttwa-base", "public_holiday_memorial_easter_mold", "Memorial Easter"),
	(NULL, "mold", "ttwa-base", "public_holiday_labor_day_mold", "Labor Day"),
	(NULL, "mold", "ttwa-base", "public_holiday_victory_day_mold", "Victory Day"),
	(NULL, "mold", "ttwa-base", "public_holiday_international_childrens_day_mold", "International Children's Day"),
	(NULL, "mold", "ttwa-base", "public_holiday_independence_day_mold", "Independence Day"),
	(NULL, "mold", "ttwa-base", "public_holiday_national_language_day_mold", "National Language Day"),
	(NULL, "mold", "ttwa-base", "public_holiday_chisinau_city_day_mold", "Chisinau City Day"),
	(NULL, "mold", "ttwa-base", "public_holiday_christmas_day_mold", "Christmas Day"),
	(NULL, "azer", "ttwa-base", "public_holiday_new_year_holiday_1_azer", "New Year Holiday 1"),
	(NULL, "azer", "ttwa-base", "public_holiday_new_year_holiday_2_azer", "New Year Holiday 2"),
	(NULL, "azer", "ttwa-base", "public_holiday_international_womens_day_azer", "International Women's Day"),
	(NULL, "azer", "ttwa-base", "public_holiday_international_womens_day_holiday_azer", "International Women's Day Holiday"),
	(NULL, "azer", "ttwa-base", "public_holiday_novruz_holiday_1_azer", "Novruz Holiday 1"),
	(NULL, "azer", "ttwa-base", "public_holiday_novruz_holiday_2_azer", "Novruz Holiday 2"),
	(NULL, "azer", "ttwa-base", "public_holiday_novruz_holiday_3_azer", "Novruz Holiday 3"),
	(NULL, "azer", "ttwa-base", "public_holiday_novruz_holiday_4_azer", "Novruz Holiday 4"),
	(NULL, "azer", "ttwa-base", "public_holiday_novruz_holiday_5_azer", "Novruz Holiday 5"),
	(NULL, "azer", "ttwa-base", "public_holiday_victory_day_azer", "Victory Day"),
	(NULL, "azer", "ttwa-base", "public_holiday_victory_day_holiday_azer", "Victory Day Holiday"),
	(NULL, "azer", "ttwa-base", "public_holiday_ramazan_bayram_holiday_1_azer", "Ramazan Bayram Holiday 1"),
	(NULL, "azer", "ttwa-base", "public_holiday_ramazan_bayram_holiday_2_azer", "Ramazan Bayram Holiday 2"),
	(NULL, "azer", "ttwa-base", "public_holiday_ramazan_bayram_holiday_3_azer", "Ramazan Bayram Holiday 3"),
	(NULL, "azer", "ttwa-base", "public_holiday_republic_day_azer", "Republic Day"),
	(NULL, "azer", "ttwa-base", "public_holiday_salvation_day_azer", "Salvation Day"),
	(NULL, "azer", "ttwa-base", "public_holiday_armed_forces_day_azer", "Armed Forces Day"),
	(NULL, "azer", "ttwa-base", "public_holiday_gurban_bayram_holiday_1_azer", "Gurban Bayram Holiday 1"),
	(NULL, "azer", "ttwa-base", "public_holiday_gurban_bayram_holiday_2_azer", "Gurban Bayram Holiday 2"),
	(NULL, "azer", "ttwa-base", "public_holiday_flag_day_azer", "Flag Day"),
	(NULL, "azer", "ttwa-base", "public_holiday_solidarity_day_of_azerbaijanis_azer", "Solidarity Day of Azerbaijanis"),
	(NULL, "biel", "ttwa-base", "public_holiday_new_years_day_biel", "New Year's Day"),
	(NULL, "biel", "ttwa-base", "public_holiday_orthodox_christmas_day_biel", "Orthodox Christmas Day"),
	(NULL, "biel", "ttwa-base", "public_holiday_womens_day_biel", "Women's Day"),
	(NULL, "biel", "ttwa-base", "public_holiday_constitution_day_biel", "Constitution Day"),
	(NULL, "biel", "ttwa-base", "public_holiday_union_day_of_belarus_and_russia_biel", "Union Day of Belarus and Russia"),
	(NULL, "biel", "ttwa-base", "public_holiday_radonitsa_biel", "Radonitsa"),
	(NULL, "biel", "ttwa-base", "public_holiday_labour_day_biel", "Labour Day"),
	(NULL, "biel", "ttwa-base", "public_holiday_victory_day_biel", "Victory Day"),
	(NULL, "biel", "ttwa-base", "public_holiday_independence_day_of_the_republic_of_belarus_biel", "Independence Day of the Republic of Belarus"),
	(NULL, "biel", "ttwa-base", "public_holiday_october_revolution_day_biel", "October Revolution Day"),
	(NULL, "biel", "ttwa-base", "public_holiday_catholic_christmas_day_biel", "Catholic Christmas Day"),
	(NULL, "en", "ttwa-base", "public_holiday_new_years_day_en", "New Year's Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_new_years_day_hu", "New Year's Day"),
	(NULL, "en", "ttwa-base", "public_holiday_good_friday_en", "Good Friday"),
	(NULL, "hu", "ttwa-base", "public_holiday_good_friday_hu", "Good Friday"),
	(NULL, "en", "ttwa-base", "public_holiday_easter_en", "Easter"),
	(NULL, "hu", "ttwa-base", "public_holiday_easter_hu", "Easter"),
	(NULL, "en", "ttwa-base", "public_holiday_easter_monday_en", "Easter Monday"),
	(NULL, "hu", "ttwa-base", "public_holiday_easter_monday_hu", "Easter Monday"),
	(NULL, "en", "ttwa-base", "public_holiday_kings_birthday_en", "King's Birthday"),
	(NULL, "hu", "ttwa-base", "public_holiday_kings_birthday_hu", "King's Birthday"),
	(NULL, "en", "ttwa-base", "public_holiday_liberation_day_en", "Liberation Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_liberation_day_hu", "Liberation Day"),
	(NULL, "en", "ttwa-base", "public_holiday_ascension_day_en", "Ascension Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_ascension_day_hu", "Ascension Day"),
	(NULL, "en", "ttwa-base", "public_holiday_pentecost_sunday_en", "Pentecost Sunday"),
	(NULL, "hu", "ttwa-base", "public_holiday_pentecost_sunday_hu", "Pentecost Sunday"),
	(NULL, "en", "ttwa-base", "public_holiday_whit_monday_en", "Whit Monday"),
	(NULL, "hu", "ttwa-base", "public_holiday_whit_monday_hu", "Whit Monday"),
	(NULL, "en", "ttwa-base", "public_holiday_christmas_day_en", "Christmas Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_christmas_day_hu", "Christmas Day"),
	(NULL, "en", "ttwa-base", "public_holiday_st_stephens_day_en", "St. Stephen's Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_st_stephens_day_hu", "St. Stephen's Day"),
	(NULL, "en", "ttwa-base", "public_holiday_orthodox_christmas_day_en", "Orthodox Christmas Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_orthodox_christmas_day_hu", "Orthodox Christmas Day"),
	(NULL, "en", "ttwa-base", "public_holiday_womens_day_en", "Women's Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_womens_day_hu", "Women's Day"),
	(NULL, "en", "ttwa-base", "public_holiday_womens_day_holiday_en", "Women's Day Holiday"),
	(NULL, "hu", "ttwa-base", "public_holiday_womens_day_holiday_hu", "Women's Day Holiday"),
	(NULL, "en", "ttwa-base", "public_holiday_orthodox_easter_sunday_en", "Orthodox Easter Sunday"),
	(NULL, "hu", "ttwa-base", "public_holiday_orthodox_easter_sunday_hu", "Orthodox Easter Sunday"),
	(NULL, "en", "ttwa-base", "public_holiday_orthodox_easter_monday_en", "Orthodox Easter Monday"),
	(NULL, "hu", "ttwa-base", "public_holiday_orthodox_easter_monday_hu", "Orthodox Easter Monday"),
	(NULL, "en", "ttwa-base", "public_holiday_labor_day_en", "Labor Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_labor_day_hu", "Labor Day"),
	(NULL, "en", "ttwa-base", "public_holiday_victory_day_en", "Victory Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_victory_day_hu", "Victory Day"),
	(NULL, "en", "ttwa-base", "public_holiday_victory_day_holiday_en", "Victory Day Holiday"),
	(NULL, "hu", "ttwa-base", "public_holiday_victory_day_holiday_hu", "Victory Day Holiday"),
	(NULL, "en", "ttwa-base", "public_holiday_orthodox_whit_sunday_en", "Orthodox Whit Sunday"),
	(NULL, "hu", "ttwa-base", "public_holiday_orthodox_whit_sunday_hu", "Orthodox Whit Sunday"),
	(NULL, "en", "ttwa-base", "public_holiday_orthodox_whit_monday_en", "Orthodox Whit Monday"),
	(NULL, "hu", "ttwa-base", "public_holiday_orthodox_whit_monday_hu", "Orthodox Whit Monday"),
	(NULL, "en", "ttwa-base", "public_holiday_constitution_day_en", "Constitution Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_constitution_day_hu", "Constitution Day"),
	(NULL, "en", "ttwa-base", "public_holiday_constitution_day_holiday_en", "Constitution Day Holiday"),
	(NULL, "hu", "ttwa-base", "public_holiday_constitution_day_holiday_hu", "Constitution Day Holiday"),
	(NULL, "en", "ttwa-base", "public_holiday_independence_day_en", "Independence Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_independence_day_hu", "Independence Day"),
	(NULL, "en", "ttwa-base", "public_holiday_defenders_day_en", "Defender's Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_defenders_day_hu", "Defender's Day"),
	(NULL, "en", "ttwa-base", "public_holiday_catholic_christmas_day_en", "Catholic Christmas Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_catholic_christmas_day_hu", "Catholic Christmas Day"),
	(NULL, "en", "ttwa-base", "public_holiday_orthodox_christmas_holiday_en", "Orthodox Christmas Holiday"),
	(NULL, "hu", "ttwa-base", "public_holiday_orthodox_christmas_holiday_hu", "Orthodox Christmas Holiday"),
	(NULL, "en", "ttwa-base", "public_holiday_international_womens_day_en", "International Women's Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_international_womens_day_hu", "International Women's Day"),
	(NULL, "en", "ttwa-base", "public_holiday_orthodox_easter_day_en", "Orthodox Easter Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_orthodox_easter_day_hu", "Orthodox Easter Day"),
	(NULL, "en", "ttwa-base", "public_holiday_memorial_easter_en", "Memorial Easter"),
	(NULL, "hu", "ttwa-base", "public_holiday_memorial_easter_hu", "Memorial Easter"),
	(NULL, "en", "ttwa-base", "public_holiday_international_childrens_day_en", "International Children's Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_international_childrens_day_hu", "International Children's Day"),
	(NULL, "en", "ttwa-base", "public_holiday_national_language_day_en", "National Language Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_national_language_day_hu", "National Language Day"),
	(NULL, "en", "ttwa-base", "public_holiday_chisinau_city_day_en", "Chisinau City Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_chisinau_city_day_hu", "Chisinau City Day"),
	(NULL, "en", "ttwa-base", "public_holiday_new_year_holiday_1_en", "New Year Holiday 1"),
	(NULL, "hu", "ttwa-base", "public_holiday_new_year_holiday_1_hu", "New Year Holiday 1"),
	(NULL, "en", "ttwa-base", "public_holiday_new_year_holiday_2_en", "New Year Holiday 2"),
	(NULL, "hu", "ttwa-base", "public_holiday_new_year_holiday_2_hu", "New Year Holiday 2"),
	(NULL, "en", "ttwa-base", "public_holiday_international_womens_day_holiday_en", "International Women's Day Holiday"),
	(NULL, "hu", "ttwa-base", "public_holiday_international_womens_day_holiday_hu", "International Women's Day Holiday"),
	(NULL, "en", "ttwa-base", "public_holiday_novruz_holiday_1_en", "Novruz Holiday 1"),
	(NULL, "hu", "ttwa-base", "public_holiday_novruz_holiday_1_hu", "Novruz Holiday 1"),
	(NULL, "en", "ttwa-base", "public_holiday_novruz_holiday_2_en", "Novruz Holiday 2"),
	(NULL, "hu", "ttwa-base", "public_holiday_novruz_holiday_2_hu", "Novruz Holiday 2"),
	(NULL, "en", "ttwa-base", "public_holiday_novruz_holiday_3_en", "Novruz Holiday 3"),
	(NULL, "hu", "ttwa-base", "public_holiday_novruz_holiday_3_hu", "Novruz Holiday 3"),
	(NULL, "en", "ttwa-base", "public_holiday_novruz_holiday_4_en", "Novruz Holiday 4"),
	(NULL, "hu", "ttwa-base", "public_holiday_novruz_holiday_4_hu", "Novruz Holiday 4"),
	(NULL, "en", "ttwa-base", "public_holiday_novruz_holiday_5_en", "Novruz Holiday 5"),
	(NULL, "hu", "ttwa-base", "public_holiday_novruz_holiday_5_hu", "Novruz Holiday 5"),
	(NULL, "en", "ttwa-base", "public_holiday_ramazan_bayram_holiday_1_en", "Ramazan Bayram Holiday 1"),
	(NULL, "hu", "ttwa-base", "public_holiday_ramazan_bayram_holiday_1_hu", "Ramazan Bayram Holiday 1"),
	(NULL, "en", "ttwa-base", "public_holiday_ramazan_bayram_holiday_2_en", "Ramazan Bayram Holiday 2"),
	(NULL, "hu", "ttwa-base", "public_holiday_ramazan_bayram_holiday_2_hu", "Ramazan Bayram Holiday 2"),
	(NULL, "en", "ttwa-base", "public_holiday_ramazan_bayram_holiday_3_en", "Ramazan Bayram Holiday 3"),
	(NULL, "hu", "ttwa-base", "public_holiday_ramazan_bayram_holiday_3_hu", "Ramazan Bayram Holiday 3"),
	(NULL, "en", "ttwa-base", "public_holiday_republic_day_en", "Republic Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_republic_day_hu", "Republic Day"),
	(NULL, "en", "ttwa-base", "public_holiday_salvation_day_en", "Salvation Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_salvation_day_hu", "Salvation Day"),
	(NULL, "en", "ttwa-base", "public_holiday_armed_forces_day_en", "Armed Forces Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_armed_forces_day_hu", "Armed Forces Day"),
	(NULL, "en", "ttwa-base", "public_holiday_gurban_bayram_holiday_1_en", "Gurban Bayram Holiday 1"),
	(NULL, "hu", "ttwa-base", "public_holiday_gurban_bayram_holiday_1_hu", "Gurban Bayram Holiday 1"),
	(NULL, "en", "ttwa-base", "public_holiday_gurban_bayram_holiday_2_en", "Gurban Bayram Holiday 2"),
	(NULL, "hu", "ttwa-base", "public_holiday_gurban_bayram_holiday_2_hu", "Gurban Bayram Holiday 2"),
	(NULL, "en", "ttwa-base", "public_holiday_flag_day_en", "Flag Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_flag_day_hu", "Flag Day"),
	(NULL, "en", "ttwa-base", "public_holiday_solidarity_day_of_azerbaijanis_en", "Solidarity Day of Azerbaijanis"),
	(NULL, "hu", "ttwa-base", "public_holiday_solidarity_day_of_azerbaijanis_hu", "Solidarity Day of Azerbaijanis"),
	(NULL, "en", "ttwa-base", "public_holiday_union_day_of_belarus_and_russia_en", "Union Day of Belarus and Russia"),
	(NULL, "hu", "ttwa-base", "public_holiday_union_day_of_belarus_and_russia_hu", "Union Day of Belarus and Russia"),
	(NULL, "en", "ttwa-base", "public_holiday_radonitsa_en", "Radonitsa"),
	(NULL, "hu", "ttwa-base", "public_holiday_radonitsa_hu", "Radonitsa"),
	(NULL, "en", "ttwa-base", "public_holiday_labour_day_en", "Labour Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_labour_day_hu", "Labour Day"),
	(NULL, "en", "ttwa-base", "public_holiday_independence_day_of_the_republic_of_belarus_en", "Independence Day of the Republic of Belarus"),
	(NULL, "hu", "ttwa-base", "public_holiday_independence_day_of_the_republic_of_belarus_hu", "Independence Day of the Republic of Belarus"),
	(NULL, "en", "ttwa-base", "public_holiday_october_revolution_day_en", "October Revolution Day"),
	(NULL, "hu", "ttwa-base", "public_holiday_october_revolution_day_hu", "October Revolution Day");

UPDATE `_sql_version` SET `revision`=11, `updated_on` = NOW() WHERE `module` = 'c_stricker';

-- VERSION -11--2020.10.28.-12:45----------------------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'PAINH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"next_balancetr\" AND data.`value` > 0', 'Egyenleg', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NOT', 'inside_type_id', NULL, NULL, 0, 'data.`dt__total_wtej_sum` + data.`dt__total_otej_sum` + data.`dt__total_otwej_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`dt__total_wtej_sum` + data.`dt__total_otej_sum` + data.`dt__total_otwej_sum`) > 0 AND (data.`workgroup_name` LIKE \"PRT%PROD%\" OR data.`workgroup_name` LIKE \"PRT%OTHER%\")', 'Éjszakai órák PROD-s és OTHER-s mcs.', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'W', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_worktime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`dt__base_worktime_sum` > 0', 'Ledolgozott munkaidő', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS751', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum` - 3600', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"PRT%OTHER%\" AND data.`dt__base_overtime_sum` > 3600 AND data.`is_restday` <> 1', 'OTHER-s munkacsoport túlóra hétköznap 1 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS501', 'inside_type_id', NULL, NULL, 0, 'IF(data.`dt__base_overtime_sum` > 3600, 3600, data.`dt__base_overtime_sum`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"PRT%OTHER%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` <> 1', 'OTHER-s munkacsoport túlóra hétköznap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS101', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"PRT%OTHER%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` = 1', 'OTHER-s munkacsoport túlóra hétvége', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS501', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum` - 7200', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"PRT%PROD%\" AND data.`dt__base_overtime_sum` > 7200 AND data.`is_restday` <> 1', 'PROD-s munkacsoport túlóra hétköznap 2 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAINH', 'inside_type_id', NULL, NULL, 0, 'IF(data.`dt__base_overtime_sum` > 7200, 7200, data.`dt__base_overtime_sum`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"PRT%PROD%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` <> 1', 'PROD-s munkacsoport túlóra hétköznap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS101', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum` - 7200', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"PRT%PROD%\" AND data.`dt__base_overtime_sum` > 7200 AND data.`is_restday` = 1', 'PROD-s munkacsoport túlóra hétvége 2 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAINH', 'inside_type_id', NULL, NULL, 0, 'IF(data.`dt__base_overtime_sum` > 7200, 7200, data.`dt__base_overtime_sum`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"PRT%PROD%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` = 1', 'PROD-s munkacsoport túlóra hétvége', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS751', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum` - 7200', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"PRT%WH%\" AND data.`dt__base_overtime_sum` > 7200 AND data.`is_restday` <> 1', 'WH-s munkacsoport túlóra hétköznap 2 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAINH', 'inside_type_id', NULL, NULL, 0, 'IF(data.`dt__base_overtime_sum` > 7200, 7200, data.`dt__base_overtime_sum`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"PRT%WH%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` <> 1', 'WH-s munkacsoport túlóra hétköznap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS101', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum` - 7200', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"PRT%WH%\" AND data.`dt__base_overtime_sum` > 7200 AND data.`is_restday` = 1', 'WH-s munkacsoport túlóra hétvége 2 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAINH', 'inside_type_id', NULL, NULL, 0, 'IF(data.`dt__base_overtime_sum` > 7200, 7200, data.`dt__base_overtime_sum`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"PRT%WH%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` = 1', 'WH-s munkacsoport túlóra hétvége', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_value`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
	('SAGE', 'DOERH', 'state_type_id', 'absence_type_paid_illness_h', NULL, 0, 0, NULL, NULL, 'Paid illness hours', 2),
	('SAGE', 'DOEMA', 'state_type_id', 'absence_type_illness_prev_month', NULL, 0, 0, NULL, NULL, 'Illness day', 2),
	('SAGE', 'DOMAH', 'state_type_id', 'f9080b5f1412afd4e5efcb893b6ca949', NULL, 0, 0, NULL, NULL, 'Illness hour', 2),
	('SAGE', 'FAFNH', 'state_type_id', 'absence_type_non_rem_fam_ass_h', NULL, 0, 0, NULL, NULL, 'Non remunerated family assistance hours', 2),
	('SAGE', 'FAFRH', 'state_type_id', 'absence_type_rem_fam_ass_h', NULL, 0, 0, NULL, NULL, 'Remunerated family assistance', 2),
	('SAGE', 'AT', 'state_type_id', '945e3618f487efc4325204521e49c5f3', NULL, 0, 0, NULL, NULL, 'Work Accident', 2),
	('SAGE', 'ATJ', 'state_type_id', 'absence_type_travel_accident', NULL, 0, 0, NULL, NULL, 'Accident on the route', 2),
	('SAGE', 'CASAM', 'state_type_id', 'absence_type_marriage', NULL, 0, 0, NULL, NULL, 'Marriage', 2),
	('SAGE', 'DECES', 'state_type_id', 'cffaabff749860f85840377da9f315cf', NULL, 0, 0, NULL, NULL, 'Death', 2),
	('SAGE', 'DOC', 'state_type_id', 'absence_type_cert_prof_disease', NULL, 0, 0, NULL, NULL, 'Certified professional disease', 2),
	('SAGE', 'DOE', 'state_type_id', 'absence_type_non_prof_disease', NULL, 0, 0, NULL, NULL, 'Non-professional disease', 2),
	('SAGE', 'DOP', 'state_type_id', 'absence_type_notcert_prof_dise', NULL, 0, 0, NULL, NULL, 'Professional disease not certified', 2),
	('SAGE', 'EST', 'state_type_id', '5382dfe7cad1651991076b4e0af903ba', NULL, 0, 0, NULL, NULL, 'Student', 2),
	('SAGE', 'FAF', 'state_type_id', 'absence_type_paid_family_assis', NULL, 0, 0, NULL, NULL, 'Paid family assistance', 2),
	('SAGE', 'FAFNR', 'state_type_id', 'absence_type_non_family_assist', NULL, 0, 0, NULL, NULL, 'Non-remunerated family assistance', 2),
	('SAGE', 'FAL', 'state_type_id', 'absence_type_family_death', NULL, 0, 0, NULL, NULL, 'Family Death', 2),
	('SAGE', 'FNRD', 'state_type_id', 'def32968390fb987c823da0cbf7d3bd8', NULL, 0, 0, NULL, NULL, 'Unpaid absences', 2),
	('SAGE', 'FNRH', 'state_type_id', 'absence_type_unpaid_h', NULL, 0, 0, NULL, NULL, 'Unpaid absences hours', 2),
	('SAGE', 'FOR', 'state_type_id', 'absence_type_formation', NULL, 0, 0, NULL, NULL, 'Formation', 2),
	('SAGE', 'FRD', 'state_type_id', 'f4f63ae4dd65cd97ee1f409d8b620806', NULL, 0, 0, NULL, NULL, 'Paid absences', 2),
	('SAGE', 'FSD', 'state_type_id', 'absence_type_disc_suspension', NULL, 0, 0, NULL, NULL, 'Absence for disciplinary suspension', 2),
	('SAGE', 'GRE', 'state_type_id', 'absence_type_strike', NULL, 0, 0, NULL, NULL, 'Strike', 2),
	('SAGE', 'HC', 'state_type_id', '29337d9204baca9588942e162d229087', NULL, 0, 0, NULL, NULL, 'Concentrated schedule enjoyment', 2),
	('SAGE', 'LGR', 'state_type_id', 'absence_type_low_risk_pregnancy', NULL, 0, 0, NULL, NULL, 'Low risk pregnancy', 2),
	('SAGE', 'MAT', 'state_type_id', 'absence_type_maternity', NULL, 0, 0, NULL, NULL, 'Maternity', 2),
	('SAGE', 'PACN', 'state_type_id', 'absence_type_prenatal_consul', NULL, 0, 0, NULL, NULL, 'Prenatal consultation', 2),
	('SAGE', 'PAMA', 'state_type_id', 'ebf4ac30e7dc238fd2f4bc86332e0675', NULL, 0, 0, NULL, NULL, 'Breastfeeding - lactation', 2),
	('SAGE', 'PAR', 'state_type_id', 'absence_type_parenting', NULL, 0, 0, NULL, NULL, 'Parenting', 2),
	('SAGE', 'PATHM', 'state_type_id', 'absence_type_risk_pregnancy', NULL, 0, 0, NULL, NULL, 'At-risk pregnancy leave', 2),
	('SAGE', 'PATHO', 'state_type_id', 'absence_type_interrupt_pregn', NULL, 0, 0, NULL, NULL, 'Licence to interrupt pregnancy', 2),
	('SAGE', 'RDL', 'state_type_id', 'absence_type_legal_reduction', NULL, 0, 0, NULL, NULL, 'By legal reduction of activity', 2),
	('SAGE', 'SA', 'state_type_id', 'absence_type_food_allowance', NULL, 0, 0, NULL, NULL, 'Food allowance', 2),
	('SAGE', 'DOER', 'state_type_id', '7b3cf78f9fd2a404567fe572fcb0eaf9', NULL, 0, 0, NULL, NULL, 'Paid illness day', 2);

UPDATE `_sql_version` SET `revision`=12, `updated_on` = NOW() WHERE `module` = 'c_stricker';

-- VERSION -12--2020.11.02.-11:15----------------------------------------------------------------

INSERT INTO state_type (state_type_id, name_dict_id, short_name_dict_id, bgcolor, day_type, status, state_type_status) VALUES
('absence_type_disc_susp', 'absence_type_disc_susp', 'absence_type_short_disc_susp', '#e84e40', 'C', 2, 2);

INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES
('pt', 'ttwa-ahp', 'absence_type_disc_susp', 'Suspenção Disciplinar'),
('en', 'ttwa-ahp', 'absence_type_disc_susp', 'Disciplinary Suspension'),
('hu', 'ttwa-ahp', 'absence_type_disc_susp', 'Fegyelmi felfüggesztés'),
('pt', 'ttwa-ahp-core', 'absence_type_disc_susp', 'Suspenção Disciplinar'),
('en', 'ttwa-ahp-core', 'absence_type_disc_susp', 'Disciplinary Suspension'),
('hu', 'ttwa-ahp-core', 'absence_type_disc_susp', 'Fegyelmi felfüggesztés'),
('pt', 'ttwa-ahp', 'absence_type_short_disc_susp', 'SD'),
('en', 'ttwa-ahp', 'absence_type_short_disc_susp', 'SD'),
('hu', 'ttwa-ahp', 'absence_type_short_disc_susp', 'SD'),
('pt', 'ttwa-ahp-core', 'absence_type_short_disc_susp', 'SD'),
('en', 'ttwa-ahp-core', 'absence_type_short_disc_susp', 'SD'),
('hu', 'ttwa-ahp-core', 'absence_type_short_disc_susp', 'SD');

UPDATE `_sql_version` SET `revision`=13, `updated_on` = NOW() WHERE `module` = 'c_stricker';

-- VERSION -13--2020.11.04.-15:15----------------------------------------------------------------

UPDATE `search_filter` SET `filter_id` = 'EMPLOYEE_WITH_FROM_TO', `modified_by` = 'SN-DEV-6452', `modified_by` = '2020-11-09 00:00:00' WHERE `controller_id` = 'payrollTransfer' AND `status` = 2 AND `filter_type` = 'date';

UPDATE `_sql_version` SET `revision`=14, `updated_on` = NOW() WHERE `module` = 'c_stricker';

-- VERSION -14--2020.11.09.-15:15----------------------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_value`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
	('SAGE', 'SD', 'state_type_id', 'absence_type_disc_susp', NULL, 0, 0, NULL, NULL, 'Disciplinary Suspension', 2);

UPDATE `_sql_version` SET `revision`=15, `updated_on` = NOW() WHERE `module` = 'c_stricker';

-- VERSION -15--2020.11.10.-08:45----------------------------------------------------------------

UPDATE `user` SET `lang` = 'pt';

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('ATJ', 'DECES', 'DOEMA', 'DOMAH', 'EST', 'LGR', 'SA');
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_value`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
	('SAGE', 'AE', 'state_type_id', 'absence_type_school_acc_h', NULL, 0, 0, NULL, NULL, 'School accompaniment (hours)', 2),
	('SAGE', 'ESTH', 'state_type_id', 'absence_type_studyleave_h', NULL, 0, 0, NULL, NULL, 'Student (h)', 2),
	('SAGE', 'FAEF', 'state_type_id', '5ac088f356af3650149df3038ca8eba1', NULL, 0, 0, NULL, NULL, 'Exceptional support Art.23', 2),
	('SAGE', 'FJNRD', 'state_type_id', '269b59b4efbbb4ef1d527492dc06cb60', NULL, 0, 0, NULL, NULL, 'Unpaid justfied absence (Day)', 2),
	('SAGE', 'FJNRH', 'state_type_id', 'absence_type_justifiednotpaid_h', NULL, 0, 0, NULL, NULL, 'Unpaid justfied absence (Hour)', 2),
	('SAGE', 'IP', 'state_type_id', 'absence_type_proplact_iso', NULL, 0, 0, NULL, NULL, 'Prophylactic isolation', 2),
	('SAGE', 'OL', 'state_type_id', 'absence_type_legal_obl_h', NULL, 0, 0, NULL, NULL, 'Legal obligations', 2),
	('SAGE', 'PAINH', 'state_type_id', 'absence_type_indiv_adapt_h', NULL, 0, 0, NULL, NULL, 'Individual Adaptability (hours)', 2);


UPDATE `_sql_version` SET `revision`=16, `updated_on` = NOW() WHERE `module` = 'c_stricker';

-- VERSION -16--2020.11.12.-15:00----------------------------------------------------------------

UPDATE app_settings SET setting_value =  "'NMB', 'OTB', 'KKB', 'HOB', 'MZG'", note = "prev: 'NMB', 'OTB', 'KKB', 'HOB' default: 'NMB', 'OTB', 'KKB', 'HOB'" WHERE setting_id = "reg_in";
UPDATE app_settings SET setting_value = "'NMK', 'OTK', 'HOK', 'MZG'", note = "prev: 'NMK', 'OTK', 'HOK' default: 'NMK', 'OTK', 'HOK'" WHERE setting_id = "reg_out";

INSERT INTO `auth_acl` (`role_id`, `controller_id`, `column_name`, `operation_id`, `usergroup_id`, `access_right`,
						`login_need`, `created_by`, `created_on`)
VALUES
	('strickerPtAttendanceSheet', 'customers/stricker/attendanceSheet', NULL, 'view', NULL, '1', '1', 'DEV-6649','2020-11-13');

INSERT INTO `auth_role` (`role_id`, `role_name`, `description`, `created_by`, `created_on`)
VALUES
	('strickerPtAttendanceSheet', 'customers/stricker/attendanceSheet --- view', 'Munkaidő-nyilvántartási lap', 'DEV-6649','2020-11-13');

INSERT INTO `auth_controller` (`controller_id`, `controller_name`, `controller_dict_id`, `created_by`, `created_on`)
VALUES
	('customers/stricker/attendanceSheet', 'AttendanceSheet', 'menu_item_attendancesheet_stricker_pt', 'DEV-6649','2020-11-13');

INSERT INTO `menu_item_table` (`menu_item_id`,`menu_item_name`,`menu_modul`,`menu_label`,`menu_item_css_class`,`menu_url`,
			`menu_visible`,`menu_visible_operation`,`menu_item_parent_id`,`menu_order`)
VALUES
	('menu_item_attendancesheet_stricker_pt', 'menu_item_attendancesheet_stricker_pt', 'ttwa-wfm', 'menu_item_attendancesheet_stricker_pt',
	'sub', '/customers/stricker/attendanceSheet/index', 'customers/stricker/attendanceSheet', 'view', '82', '158');

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`)
VALUES
    ('hu', 'ttwa-wfm', 'page_title_attendancesheet_stricker_pt', 'Munkaidő-nyilvántartási lap', '1'),
    ('en', 'ttwa-wfm', 'page_title_attendancesheet_stricker_pt', 'Timesheet', '1'),
	('hu', 'ttwa-wfm', 'menu_item_attendancesheet_stricker_pt', 'Munkaidő-nyilvántartási lap', '1'),
    ('en', 'ttwa-wfm', 'menu_item_attendancesheet_stricker_pt', 'Timesheet', '1'),
	('hu', 'ttwa-wfm', 'worktime_and_paid_abs', 'Munkaidő és fizetett távollét', '1'),
    ('en', 'ttwa-wfm', 'worktime_and_paid_abs', 'Worktime and paid absence', '1');

INSERT INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
('customers/stricker/attendanceSheet', 'date', 'EMPLOYEE_WITH_YEARMONTH', 'combo', '0', '2','DEV-6649','2020-11-13'),
('customers/stricker/attendanceSheet', 'group', 'DEFAULT_GROUP_FILTER', 'combo', '0', '2', 'DEV-6649','2020-11-13');

UPDATE `_sql_version` SET `revision`=17, `updated_on` = NOW() WHERE `module` = 'c_stricker';

-- VERSION -17--2020.11.13.-14:00----------------------------------------------------------------

UPDATE dictionary SET dict_value = 'Family Death (Mourning)' WHERE dict_id = 'base_absence_type_funeralleave' AND lang = 'en';
UPDATE dictionary SET dict_value = 'Óbito Familiar (Luto)' WHERE dict_id = 'base_absence_type_funeralleave' AND lang = 'pt';
UPDATE dictionary SET dict_value = 'Marriage' WHERE dict_id = 'base_absence_type_otherleave' AND lang = 'en';
UPDATE dictionary SET dict_value = 'Casamento' WHERE dict_id = 'base_absence_type_otherleave' AND lang = 'pt';

INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES
('hu', 'ttwa-base', 'base_absence_type_parenting', 'Szülői szabadság'),
('en', 'ttwa-base', 'base_absence_type_parenting', 'Parenting'),
('pt', 'ttwa-base', 'base_absence_type_parenting', 'Parentalidade');

INSERT INTO link_at_to_bat (state_type_id, base_absence_type_id) VALUES
('absence_type_family_death', 'e35b75ed4f59c57f5601951dc0080ad6'),
('absence_type_marriage', 'e9bb81654931b76510b0118fb2ee183a'),
('absence_type_parenting', 'base_absence_type_parenting');

UPDATE `_sql_version` SET `revision`=18, `updated_on` = NOW() WHERE `module` = 'c_stricker';

-- VERSION -18--2020.11.23.-11:00----------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'DEV-6648 prev: 0'  WHERE `setting_id` = 'justFulldayAbsences';

UPDATE `_sql_version` SET `revision`=19, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -19--2020.11.12.-15:00----------------------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'DOERH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_paid_illness_h\"', 'Paid illness hours - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAFNH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_non_rem_fam_ass_h\"', 'Non remunerated family assistance hours - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAFRH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_rem_fam_ass_h\"', 'Remunerated family assistance - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FNRH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_unpaid_h\"', 'Unpaid absences hours - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'PAINH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_indiv_adapt_h\"', 'Individual Adaptability (hours) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'AE', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_school_acc_h\"', 'School accompaniment (hours) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'ESTH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_studyleave_h\"', 'Student (h) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FJNRH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_justifiednotpaid_h\"', 'Unpaid justfied absence (Hour) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'OL', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_legal_obl_h\"', 'Legal obligations - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_value`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
	('SAGE', 'DOE', 'state_type_id', 'c4a23d94c93c7500e3bdf97cc8cbf8b7', NULL, 0, 0, NULL, NULL, 'Medical leave', 2),
	('SAGE', 'DOP', 'state_type_id', 'absence_type_uncert_occ_ill', NULL, 0, 0, NULL, NULL, 'Uncertified Occupational illness', 2),
	('SAGE', 'FAFNR', 'state_type_id', 'absence_type_unpaid_fam_ass', NULL, 0, 0, NULL, NULL, 'Unpaid Family Assistance (Days)', 2),
	('SAGE', 'FER', 'state_type_id', 'a272f564576d443e7832587126b070aa', NULL, 0, 0, NULL, NULL, 'Vacation', 2),
	('SAGE', 'FRH', 'state_type_id', 'absence_type_paid_illness_h', NULL, 0, 0, NULL, NULL, 'Paid justified absences', 2),
	('SAGE', 'FRH', 'state_type_id', 'absence_type_justifiedpaid', NULL, 0, 0, NULL, NULL, 'Paid justified absences', 2);

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` = 'DOERH';

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'FAFNH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_unpaid_fam_ass_h\"', 'Unpaid Family Assistance (Hours) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAFRH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_paid_fam_ass_h\"', 'Paid Family Assistance (Hours) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FOR', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_trl\"', 'Training - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FRH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_paid_illness_h\"', 'Paid justified absences - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'PAMA', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_lactation\"', 'Breastfeeding / Lactation - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'PACN', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_prenatal_app_h\"', 'Prenatal Consultation - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `state_type` (`state_type_id`, `name_dict_id`, `bgcolor`, `status`, `absence_hour`, `worktime`) VALUES
	('absence_type_paid_illness_real_h', 'absence_type_paid_illness_real_hours', '#ff7016', 2, 1, 1);

UPDATE `state_type` SET `short_name_dict_id` = 'absence_type_short_paid_illness_real_hours' WHERE `state_type_id` = 'absence_type_paid_illness_real_h';

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`) VALUES
	('pt', 'ttwa-ahp', 'absence_type_short_paid_illness_real_hours', 'DOERH'),
	('pt', 'ttwa-ahp-core', 'absence_type_short_paid_illness_real_hours', 'DOERH'),
	('en', 'ttwa-ahp', 'absence_type_short_paid_illness_real_hours', 'DOERH'),
	('en', 'ttwa-ahp-core', 'absence_type_short_paid_illness_real_hours', 'DOERH');

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`) VALUES
	('pt', 'ttwa-ahp', 'absence_type_paid_illness_real_hours', 'Acidente no Trajeto'),
	('en', 'ttwa-ahp', 'absence_type_paid_illness_real_hours', 'Paid illness (hours)'),
	('pt', 'ttwa-ahp-core', 'absence_type_paid_illness_real_hours', 'Acidente no Trajeto'),
	('en', 'ttwa-ahp-core', 'absence_type_paid_illness_real_hours', 'Paid illness (hours)');

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_value`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
	('SAGE', 'DOERH', 'state_type_id', 'absence_type_paid_illness_real_h', NULL, 0, 0, NULL, NULL, 'Paid illness (hours)', 2);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'DOERH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_paid_illness_real_h\"', 'Paid illness (hours) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=20, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -20--2020.12.04.-14:00----------------------------------------------------------------

UPDATE `inside_type` SET `status` = 2, `note` = 'DEV-6055 Prev val: 4'
WHERE `inside_type_id` like 'bot%' AND `inside_type_id` NOT IN ('botdu', 'botwdu', 'botdusun', 'botwdusun');

UPDATE `_sql_version` SET `revision`=21, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -21--2020.11.26.-14:30----------------------------------------------------------------

UPDATE dictionary SET dict_value = 'Osztály' WHERE dict_id = 'menu_item_companyorggroup1' AND lang = 'hu';
UPDATE dictionary SET dict_value = 'Department' WHERE dict_id = 'menu_item_companyorggroup1' AND lang = 'en';
INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES ('pt', 'ttwa-base', 'menu_item_companyorggroup1', 'Departamento');

UPDATE dictionary SET dict_value = 'Szekció' WHERE dict_id = 'menu_item_companyorggroup2' AND lang = 'hu';
UPDATE dictionary SET dict_value = 'Section' WHERE dict_id = 'menu_item_companyorggroup2' AND lang = 'en';
INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES ('pt', 'ttwa-base', 'menu_item_companyorggroup2', 'Secção');

INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES ('pt', 'ttwa-base', 'menu_item_companyorggroup3', 'Gestor de Tempos');

UPDATE dictionary SET dict_value = 'Osztály' WHERE dict_id = 'company_org_group1' AND lang = 'hu';
UPDATE dictionary SET dict_value = 'Department' WHERE dict_id = 'company_org_group1' AND lang = 'en';
INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES ('pt', 'ttwa-base', 'company_org_group1', 'Departamento');

UPDATE dictionary SET dict_value = 'Szekció' WHERE dict_id = 'company_org_group2' AND lang = 'hu';
UPDATE dictionary SET dict_value = 'Section' WHERE dict_id = 'company_org_group2' AND lang = 'en';
INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES ('pt', 'ttwa-base', 'company_org_group2', 'Secção');

INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES ('pt', 'ttwa-base', 'company_org_group3', 'Gestor de Tempos');

UPDATE dictionary SET dict_value = 'Osztály betöltés' WHERE dict_id = 'menu_item_company_org_group1_upload' AND lang = 'hu';
UPDATE dictionary SET dict_value = 'Department uploader' WHERE dict_id = 'menu_item_company_org_group1_upload' AND lang = 'en';
INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES ('pt', 'ttwa-base', 'menu_item_company_org_group1_upload', 'Carregamento do Departamento');

UPDATE dictionary SET dict_value = 'Szekció betöltés' WHERE dict_id = 'menu_item_company_org_group2_upload' AND lang = 'hu';
UPDATE dictionary SET dict_value = 'Section uploader' WHERE dict_id = 'menu_item_company_org_group2_upload' AND lang = 'en';
INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES ('pt', 'ttwa-base', 'menu_item_company_org_group2_upload', 'Carregamento do Secçã');

UPDATE dictionary SET dict_value = 'Közvetlen vezető betöltés' WHERE dict_id = 'menu_item_company_org_group3_upload' AND lang = 'hu';
UPDATE dictionary SET dict_value = 'Direct Manager uploader' WHERE dict_id = 'menu_item_company_org_group3_upload' AND lang = 'en';
INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES ('pt', 'ttwa-base', 'menu_item_company_org_group3_upload', 'Carregamento do Gestor de Tempos');

UPDATE dictionary SET dict_value = 'Osztály betöltés' WHERE dict_id = 'page_title_company_org_group1_upload' AND lang = 'hu';
UPDATE dictionary SET dict_value = 'Department uploader' WHERE dict_id = 'page_title_company_org_group1_upload' AND lang = 'en';
INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES ('pt', 'ttwa-base', 'page_title_company_org_group1_upload', 'Carregamento do Departamento');

UPDATE dictionary SET dict_value = 'Szekció betöltés' WHERE dict_id = 'page_title_company_org_group2_upload' AND lang = 'hu';
UPDATE dictionary SET dict_value = 'Section uploader' WHERE dict_id = 'page_title_company_org_group2_upload' AND lang = 'en';
INSERT INTO dictionary (lang, module, dict_id, dict_value) VALUES ('pt', 'ttwa-base', 'page_title_company_org_group2_upload', 'Carregamento do Secçã');

UPDATE dictionary SET dict_value = 'Közvetlen vezető betöltés' WHERE dict_id = 'page_title_company_org_group3_upload' AND lang = 'hu';
UPDATE dictionary SET dict_value = 'Direct Manager uploader' WHERE dict_id = 'page_title_company_org_group3_upload' AND lang = 'en';
UPDATE dictionary SET dict_value = 'Carregamento do Gestor de Tempos' WHERE dict_id = 'page_title_company_org_group3_upload' AND lang = 'pt';

UPDATE `_sql_version` SET `revision`=22, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -22--2020.12.15.-12:00----------------------------------------------------------------

INSERT
	INTO
	`public_holiday` ( `holidaydate`, `country`, `name_dict_id`, `name`, `type`, `company_id`, `status`, `created_by`, `created_on` )
VALUES ( "2021-01-01", "nl", "public_holiday_new_years_day_nl", "New Year's Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-04-02", "nl", "public_holiday_good_friday_nl", "Good Friday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-04-04", "nl", "public_holiday_easter_nl", "Easter", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-04-05", "nl", "public_holiday_easter_monday_nl", "Easter Monday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-04-27", "nl", "public_holiday_kings_birthday_nl", "King's Birthday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-05", "nl", "public_holiday_liberation_day_nl", "Liberation Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-13", "nl", "public_holiday_ascension_day_nl", "Ascension Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-23", "nl", "public_holiday_whit_sunday_nl", "Whit Sunday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-24", "nl", "public_holiday_whit_monday_nl", "Whit Monday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-12-25", "nl", "public_holiday_christmas_day_nl", "Christmas Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-12-26", "nl", "public_holiday_st_stephens_day_nl", "St. Stephen's Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-01-01", "en", "public_holiday_new_years_day_en", "New Year's Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-01-07", "en", "public_holiday_orthodox_christmas_day_en", "Orthodox Christmas Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-03-08", "en", "public_holiday_womens_day_en", "Women's Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-01", "en", "public_holiday_labor_day_en", "Labor Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-02", "en", "public_holiday_orthodox_easter_sunday_en", "Orthodox Easter Sunday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-03", "en", "public_holiday_orthodox_easter_monday_en", "Orthodox Easter Monday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-09", "en", "public_holiday_victory_day_en", "Victory Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-10", "en", "public_holiday_victory_day_holiday_en", "Victory Day Holiday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-06-20", "en", "public_holiday_orthodox_whit_sunday_en", "Orthodox Whit Sunday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-06-21", "en", "public_holiday_orthodox_whit_monday_en", "Orthodox Whit Monday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-06-28", "en", "public_holiday_constitution_day_en", "Constitution Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-08-24", "en", "public_holiday_independence_day_en", "Independence Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-10-14", "en", "public_holiday_defenders_day_en", "Defender's Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-12-25", "en", "public_holiday_catholic_christmas_day_en", "Catholic Christmas Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-01-01", "md", "public_holiday_new_years_day_md", "New Year's Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-01-07", "md", "public_holiday_orthodox_christmas_day_md", "Orthodox Christmas Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-01-08", "md", "public_holiday_orthodox_christmas_holiday_md", "Orthodox Christmas Holiday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-03-08", "md", "public_holiday_international_womens_day_md", "International Women's Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-02", "md", "public_holiday_orthodox_easter_day_md", "Orthodox Easter Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-03", "md", "public_holiday_orthodox_easter_monday_md", "Orthodox Easter Monday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-01", "md", "public_holiday_labor_day_md", "Labor Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-09", "md", "public_holiday_victory_day_md", "Victory Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-10", "md", "public_holiday_easter_of_blajini_md", "Easter of Blajini", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-06-01", "md", "public_holiday_international_childrens_day_md", "International Children's Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-08-27", "md", "public_holiday_independence_day_md", "Independence Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-08-31", "md", "public_holiday_national_language_day_md", "National Language Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-12-25", "md", "public_holiday_christmas_day_md", "Christmas Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-01-01", "az", "public_holiday_new_year_holiday_1_az", "New Year Holiday 1", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-01-02", "az", "public_holiday_new_year_holiday_2_az", "New Year Holiday 2", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-01-03", "az", "public_holiday_new_year_holiday_3_az", "New Year Holiday 3", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-01-04", "az", "public_holiday_new_year_holiday_4_az", "New Year Holiday 4", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-03-08", "az", "public_holiday_international_womens_day_az", "International Women's Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-03-20", "az", "public_holiday_novruz_holiday_1_az", "Novruz Holiday 1", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-03-21", "az", "public_holiday_novruz_holiday_2_az", "Novruz Holiday 2", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-03-22", "az", "public_holiday_novruz_holiday_3_az", "Novruz Holiday 3", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-03-23", "az", "public_holiday_novruz_holiday_4_az", "Novruz Holiday 4", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-03-24", "az", "public_holiday_novruz_holiday_5_az", "Novruz Holiday 5", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-09", "az", "public_holiday_victory_day_az", "Victory Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-11", "az", "public_holiday_victory_day_holiday_az", "Victory Day Holiday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-13", "az", "ramazan_bayram_holiday", "Ramazan Bayram Holiday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-14", "az", "ramazan_bayram_holiday", "Ramazan Bayram Holiday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-28", "az", "public_holiday_republic_day_az", "Republic Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-06-15", "az", "public_holiday_salvation_day_az", "Salvation Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-06-26", "az", "public_holiday_armed_forces_day_az", "Armed Forces Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-06-28", "az", "public_holiday_armed_forces_day_azer_holiday", "Armed Forces Day Holiday", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-07-20", "az", "public_holiday_gurban_bayram_holiday_1_az", "Gurban Bayram Holiday 1", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-07-21", "az", "public_holiday_gurban_bayram_holiday_2_az", "Gurban Bayram Holiday 2", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-11-09", "az", "public_holiday_flag_day_az", "Flag Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-12-31", "az", "public_holiday_solidarity_day_of_azerbaijanis_az", "Solidarity Day of Azerbaijanis", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-01-01", "biel", "public_holiday_new_years_day_biel", "New Year's Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-01-07", "biel", "public_holiday_orthodox_christmas_day_biel", "Orthodox Christmas Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-03-08", "biel", "public_holiday_womens_day_biel", "Women's Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-11", "biel", "public_holiday_radonitsa_biel", "Radonitsa", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-01", "biel", "public_holiday_labour_day_biel", "Labour Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-05-09", "biel", "public_holiday_victory_day_biel", "Victory Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-06-03", "biel", "public_holiday_independence_day_of_the_republic_of_belarus_biel", "Independence Day of the Republic of Belarus", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-11-07", "biel", "public_holiday_october_revolution_day_biel", "October Revolution Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( "2021-12-25", "biel", "public_holiday_catholic_christmas_day_biel", "Catholic Christmas Day", "3", "ALL", "2", "DEV-6639", '2020-12-22 16:00:00' ),
( '2021-01-01', 'ru', 'public_holiday_hun_newyear', 'New Years Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-02', 'ru', 'public_holiday_hun_newyear', 'New Years Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-03', 'ru', 'public_holiday_hun_newyear', 'New Years Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-04', 'ru', 'public_holiday_hun_newyear', 'New Years Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-05', 'ru', 'public_holiday_hun_newyear', 'New Years Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-06', 'ru', 'public_holiday_hun_newyear', 'New Years Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-07', 'ru', 'public_holiday_ru_orthodox_christmas_day', 'Orthodox Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-08', 'ru', 'public_holiday_hun_newyear', 'New Years Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-02-23', 'ru', 'public_holiday_ru_defender_of_fatherland_day', 'Defender of Fatherland Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-03-08', 'ru', 'public_holiday_ru_international_womens_day', 'International Womens Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'ru', 'public_holiday_ru_spring_and_labour_day', 'Spring and Labour Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-02', 'ru', 'public_holiday_ru_spring_and_labour_day_holiday', 'Spring and Labour Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-09', 'ru', 'public_holiday_ru_victory_day', 'Victory Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-10', 'ru', 'publazeric_holiday_ru_victory_day_holiday', 'Victory Day Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-12', 'ru', 'public_holiday_ru_day_of_russia', 'Day of Russia', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-14', 'ru', 'public_holiday_ru_day_of_russia_holiday', 'Day of Russia', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-04', 'ru', 'public_holiday_ru_national_unity_day', 'National Unity Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'bin', 'public_holiday_new_years_day_bin', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-02', 'bin', 'public_holiday_new_years_day_holiday_bin', 'New Year Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-06', 'bin', 'public_holiday_ortodox_christmas_eve_bin', 'Orthodox Christmas Eve', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-07', 'bin', 'public_holiday_ortodox_christmas_day_bin', 'Orthodox Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-09', 'bin', 'public_holiday_republic_day_bin', 'Republic Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-14', 'bin', 'public_holiday_orthodox_new_year_bin', 'Orthodox New Year', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-03-01', 'bin', 'public_holiday_independence_day_bin', 'Independence Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-04', 'bin', 'public_holiday_catholic_easter_sunday_bin', 'Catholic Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'bin', 'public_holiday_catholic_easter_monday_bin', 'Catholic Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-30', 'bin', 'public_holiday_orthodox_good_friday_bin', 'Orthodox Good Friday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'bin', 'public_holiday_labour_day_bin', 'Labour Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-02', 'bin', 'public_holiday_orthodox_easter_sunday_bin', 'Orthodox Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-03', 'bin', 'public_holiday_orthodoy_easter_monday_bin', 'Orthodox Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-09', 'bin', 'public_holiday_victory_day_bin', 'Victory Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-13', 'bin', 'public_holiday_ramadan_bajram_bin', 'Ramadan Bajram', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-28', 'bin', 'public_holiday_st_vitus_day_bin', 'St Vitus Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-07-20', 'bin', 'public_holiday_kurban_bajram_bin', 'Kurban Bajram', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-01', 'bin', 'public_holiday_all_saints_day_bin', 'All Saints Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-21', 'bin', 'public_holiday_dayton_agreement_day_bin', 'Dayton Agreement Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-25', 'bin', 'public_holiday_dayton_agreement_day_bin', 'Dayton Agreement Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'bin', 'public_holiday_catholic_christmas_day_bin', 'Catholic Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'be', 'public_holiday_new_years_day_be', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'be', 'public_holiday_easter_monday_be', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'be', 'public_holiday_labour_day_be', 'Labour Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'be', 'public_holiday_ascension_day_be', 'Ascension Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'be', 'public_holiday_whit_monday_be', 'Whit Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'be', 'public_holiday_national_day_be', 'National Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'be', 'public_holiday_assumption_day_be', 'Assumption Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'be', 'public_holiday_all_saints_day_be', 'All Saints Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'be', 'public_holiday_armistice_day_be', 'Armistice Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'be', 'public_holiday_christmas_day_be', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'bg', 'public_holiday_new_years_day_bg', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-03-03', 'bg', 'public_holiday_liberation_day_bg', 'Liberation Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-30', 'bg', 'public_holiday_ortodox_good_friday_bg', 'Orthodox Good Friday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'bg', 'public_holiday_ortodox_easter_saturday_bg', 'Orthodox Easter Saturday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-02', 'bg', 'public_holiday_ortodox_easter_sunday_bg', 'Orthodox Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-03', 'bg', 'public_holiday_ortodox_easter_monday_bg', 'Orthodox Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-06', 'bg', 'public_holiday_saint_georges_day_bg', 'Saint Georges Day / Army Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-24', 'bg', 'public_holiday_culture_and_literacy_day_bg', 'Culture and Literacy Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-09-06', 'bg', 'public_holiday_unification_day_bg', 'Unification Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-09-22', 'bg', 'public_holiday_independence_day_bg', 'Independence Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-01', 'bg', 'public_holiday_day_of_the_bulgarion_enlighteneres_bg', 'Day of the Bulgarian Enlighteners *', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-24', 'bg', 'public_holiday_christmas_eve_bg', 'Christmas Eve', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'bg', 'public_holiday_christmas_day_bg', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'bg', 'public_holiday_christmas_day_2_bg', '2nd Day of Christmas', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'de', 'public_holiday_new_years_day_de', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-02', 'de', 'public_holiday_good_friday_de', 'Good Friday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'de', 'public_holiday_easter_monday_de', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'de', 'public_holiday__labour_day_de', 'Labour Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-13', 'de', 'public_holiday_ascension_day_de', 'Ascension Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-24', 'de', 'public_holiday_whit_monday_de', 'Whit Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-10-03', 'de', 'public_holiday_day_of_german_unity_de', 'Day of German Unity', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'de', 'public_holiday_christmas_day_de', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'de', 'public_holiday_christmas_day_2_de', '2nd Day of Christmas', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'dk', 'public_holiday_new_years_day_dk', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-01', 'dk', 'public_holiday_maundy_thursday_de', 'Maundy Thursday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-02', 'dk', 'public_holiday_good_friday_de', 'Good Friday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-04', 'dk', 'public_holiday_easter_sunday_de', 'Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'dk', 'public_holiday_easter_monday_de', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-30', 'dk', 'public_holiday_prayer_day_de', 'Prayer Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-13', 'dk', 'public_holiday_ascension_day_de', 'Ascension Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-23', 'dk', 'public_holiday_whit_sunday_de', 'Whit Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-24', 'dk', 'public_holiday_whit_monday_de', 'Whit Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'dk', 'public_holiday_christmas_day_de', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'dk', 'public_holiday_christmas_day_de', '2nd Day of Christmas', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'en', 'public_holiday_new_years_day_en', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-02', 'en', 'public_holiday_good_friday_en', 'Good Friday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'en', 'public_holiday_easter_monday_en', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-03', 'en', 'public_holiday_may_day_en', 'May Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-31', 'en', 'public_holiday_late_may_bank_holiday_en', 'Late May Bank Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-08-30', 'en', 'public_holiday_august_bank_holiday_en', 'August Bank Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'en', 'public_holiday_christmas_day_en', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'en', 'public_holiday_boxing_day_en', 'Boxing Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-27', 'en', 'public_holiday_christmas_holiday_en', 'Christmas Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-28', 'en', 'public_holiday_boxing_day_en', 'Boxing Day Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'est', 'public_holiday_new_years_day_est', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-02-24', 'est', 'public_holiday_independence_day_est', 'Independence Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-02', 'est', 'public_holiday_good_fridday_est', 'Good Friday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-04', 'est', 'public_holiday_easter_sunday_est', 'Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'est', 'public_holiday_spring_day_est', 'Spring Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-23', 'est', 'public_holiday_white_sunday_est', 'Whit Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-23', 'est', 'public_holiday_victory_day_est', 'Victory Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-24', 'est', 'public_holiday_midsummer_day_est', 'Midsummer Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-08-20', 'est', 'public_holiday_independence_restoration_day_est', 'Independence Restoration Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-24', 'est', 'public_holiday_christmas_eve_est', 'Christmas Eve', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'est', 'public_holiday_christmas_day_est', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'est', 'public_holiday_christmas_day_2_est', '2nd Day of Christmas', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'fi', 'public_holiday_new_years_day_fi', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-06', 'fi', 'public_holiday_epiphay_fi', 'Epiphany', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-02', 'fi', 'public_holiday_good_friday_fi', 'Good Friday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-04', 'fi', 'public_holiday_easter_sunday_fi', 'Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'fi', 'public_holiday_easter_monday_fi', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'fi', 'public_holiday_may_day_fi', 'May Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-13', 'fi', 'public_holiday_ascension_day_fi', 'Ascension Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-23', 'fi', 'public_holiday_whit_sunday_fi', 'Whit Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-25', 'fi', 'public_holiday_midsummer_day_fi', 'Midsummer Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-06', 'fi', 'public_holiday_all_saints_day_fi', 'All Saints Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-06', 'fi', 'public_holiday_independence_day_fi', 'Independence Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-24', 'fi', 'public_holiday_christmas_eve_fi', 'Christmas Eve', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'fi', 'public_holiday_christmas_day_fi', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'fi', 'public_holiday_christmas_day_2_fi', '2nd Day of Christmas', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'fr', 'public_holiday_new_years_day_fr', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'fr', 'public_holiday_easter_monday_fr', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'fr', 'public_holiday_labour_day_fr', 'Labour Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-08', 'fr', 'public_holiday_victory_day_fr', 'Victory Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-13', 'fr', 'public_holiday_ascension_day_fr', 'Ascension Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-23', 'fr', 'public_holiday_whit_sunday_fr', 'Whit Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-24', 'fr', 'public_holiday_whit_monday_fr', 'Whit Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-14', 'fr', 'public_holiday_bastiller_day_fr', 'Bastille Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-08-15', 'fr', 'public_holiday_assumption_day_fr', 'Assumption Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-01', 'fr', 'public_holiday_all_saints_day_fr', 'All Saints Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-11', 'fr', 'public_holiday_armistice_day_fr', 'Armistice Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'fr', 'public_holiday_christmas_day_fr', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'gr', 'public_holiday_new_years_day_gr', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-06', 'gr', 'public_holiday_epiphany_gr', 'Epiphany', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-03-15', 'gr', 'public_holiday_orthodox_ash_monday_gr', 'Orthodox Ash Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-03-25', 'gr', 'public_holiday_independence_day_gr', 'Independence Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-30', 'gr', 'public_holiday_orthodox_good_friday_gr', 'Orthodox Good Friday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'gr', 'public_holiday_labour_day_gr', 'Labour Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-02', 'gr', 'public_holiday_orthodox_easter_sunday_gr', 'Orthodox Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-03', 'gr', 'public_holiday_orthodox_eeaster_monday_gr', 'Orthodox Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-20', 'gr', 'public_holiday_orthodox_whit_sunday_gr', 'Orthodox Whit Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-21', 'gr', 'public_holiday_orthodox_whit_monday_gr', 'Orthodox Whit Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-08-15', 'gr', 'public_holiday_assumption_day_gr', 'Assumption Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-10-28', 'gr', 'public_holiday_ochi_day_gr', 'Ochi Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'gr', 'public_holiday_christmas_day_gr', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'gr', 'public_holiday_christmas_day_2_gr', '2nd Day of Christmas', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'hr', 'public_holiday_new_years_day_hr', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-06', 'hr', 'public_holiday_epiphany_hr', 'Epiphany', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'hr', 'public_holiday_easter_monday_hr', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'hr', 'public_holiday_labour_day_hr', 'Labour Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-30', 'hr', 'public_holiday_statehood_day_hr', 'Statehood Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-03', 'hr', 'public_holiday_corpus_chrisit_hr', 'Corpus Christi', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-22', 'hr', 'public_holiday_anti_fascist_resistance_day_hr', 'Anti-Fascist Resistance Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-08-05', 'hr', 'public_holiday_v_and_h_t_day_hr', 'Victory and Homeland Thanksgiving Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-08-15', 'hr', 'public_holiday_assumption_day_hr', 'Assumption Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-10-08', 'hr', 'public_holiday_independence_day_hr', 'Independence Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-01', 'hr', 'public_holiday_all_saints_day_hr', 'All Saints Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-18', 'hr', 'public_holiday_remembrance_day_hr', 'Remembrance Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'hr', 'public_holiday_christmas_day_hr', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'hr', 'public_holiday_st_stephens_day_hr', 'St Stephens Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'hu', 'public_holiday_new_years_day_hu', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-03-15', 'hu', 'public_holiday_revolution_day_hu', 'Revolution Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-02', 'hu', 'public_holiday_good_friday_hu', 'Good Friday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'hu', 'public_holiday_easter_monday_hu', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'hu', 'public_holiday_labour_day_hu', 'Labour Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-08-20', 'hu', 'public_holiday_saint_stephens_day_hu', 'Saint Stephens Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-10-23', 'hu', 'public_holiday_republic_day_hu', 'Republic Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-01', 'hu', 'public_holiday_all_saints_day_hu', 'All Saints Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'hu', 'public_holiday_christmas_day_hu', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'hu', 'public_holiday_christmas_day_2_hu', '2nd Day of Christmas', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'it', 'public_holiday_new_years_day_it', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-06', 'it', 'public_holiday_epiphany_it', 'Epiphany', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-04', 'it', 'public_holiday_easter_sunday_it', 'Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'it', 'public_holiday_easter_monday_it', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-25', 'it', 'public_holiday_liberation_day_it', 'Liberation Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'it', 'public_holiday_international_workers_day_it', 'International Workers Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-02', 'it', 'public_holiday_republic_day_it', 'Republic Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-08-15', 'it', 'public_holiday_assumption_day_it', 'Assumption Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-01', 'it', 'public_holiday_all_saints_day_it', 'All Saints Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-08', 'it', 'public_holiday_immaculate_conception_it', 'Immaculate Conception', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'it', 'public_holiday_christmas_day_it', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'it', 'public_holiday_st_stephens_day_it', 'St Stephens Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'lt', 'public_holiday_new_years_day_lt', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-02-16', 'lt', 'public_holiday_independence_day_lt', 'Independence Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-03-11', 'lt', 'public_holiday_independence_lt', 'Independence Restoration Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-04', 'lt', 'public_holiday_easter_sunday_lt', 'Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'lt', 'public_holiday_easter_monday_lt', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'lt', 'public_holiday_labour_day_lt', 'Labour Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-02', 'lt', 'public_holiday_mothers_day_lt', 'Mothers Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-06', 'lt', 'public_holiday_fathers_day_lt', 'Fathers Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-24', 'lt', 'public_holiday_st_johns_day_lt', 'St Johns Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-07-06', 'lt', 'public_holiday_king_mindaugas_day_lt', 'King Mindaugas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-08-15', 'lt', 'public_holiday_assumption_day_lt', 'Assumption Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-01', 'lt', 'public_holiday_all_saints_day_lt', 'All Saints Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-02', 'lt', 'public_holiday_all_souls_day_lt', 'All Souls Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-24', 'lt', 'public_holiday_christmas_eve_lt', 'Christmas Eve', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'lt', 'public_holiday_christmas_day_lt', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'lt', 'public_holiday_christmas_day_2_lt', '2nd Day of Christmas', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'lv', 'public_holiday_new_years_day_lv', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-02', 'lv', 'public_holiday_good_friday_lv', 'Good Friday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'lv', 'public_holiday_easter_monday_lv', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'lv', 'public_holiday_labour_day_lv', 'Labour Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-04', 'lv', 'public_holiday_independence_restoration_day_lv', 'Independence Restoration Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-23', 'lv', 'public_holiday_midsummers_eve_lv', 'Midsummers Eve', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-24', 'lv', 'public_holiday_st_johns_day_lv', 'St Johns Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-18', 'lv', 'public_holiday__independence_day_lv', 'Independence Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-24', 'lv', 'public_holiday_christmas_eve_lv', 'Christmas Eve', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'lv', 'public_holiday_christmas_day_lv', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'lv', 'public_holiday_christmas_day_2_lv', '2nd Day of Christmas', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-31', 'lv', 'public_holiday_new_years_eve_lv', 'New Years Eve', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'no', 'public_holiday_new_years_day_no', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-01', 'no', 'public_holiday_maundy_thursday_no', 'Maundy Thursday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-02', 'no', 'public_holiday_good_friday_no', 'Good Friday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-04', 'no', 'public_holiday_easter_sunday_no', 'Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'no', 'public_holiday_easter_monday_no', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'no', 'public_holiday_labour_day_no', 'Labour Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-13', 'no', 'public_holiday_ascension_day_no', 'Ascension Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-17', 'no', 'public_holiday_constitution_day_no', 'Constitution Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-23', 'no', 'public_holiday_whit_sunday_no', 'Whit Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-24', 'no', 'public_holiday_whit_monday_no', 'Whit Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'no', 'public_holiday_christmas_day_no', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'no', 'public_holiday_christmas_day_2_no', '2nd Day of Christmas', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'pl', 'public_holiday_new_years_day_pl', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-06', 'pl', 'public_holiday_epiphany_pl', 'Epiphany', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-04', 'pl', 'public_holiday_easter_sunday_pl', 'Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'pl', 'public_holiday_easter_monday_pl', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'pl', 'public_holiday_labour_day_pl', 'Labour Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-03', 'pl', 'public_holiday_constituation_day_pl', 'Constitution Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-23', 'pl', 'public_holiday_white_sunday_pl', 'Whit Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-03', 'pl', 'public_holiday_corpus_christi_pl', 'Corpus Christi', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-08-15', 'pl', 'public_holiday_assumption_day_pl', 'Assumption Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-01', 'pl', 'public_holiday_all_saints_day_pl', 'All Saints Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-11', 'pl', 'public_holiday_independence_day_pl', 'Independence Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'pl', 'public_holiday_christmas_day_pl', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'pl', 'public_holiday_christmas_day_2_pl', '2nd Day of Christmas', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'pt', 'public_holiday_new_years_day_pt', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-02', 'pt', 'public_holiday_goog_friday_pt', 'Good Friday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-04', 'pt', 'public_holiday_easter_sunday_pt', 'Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-25', 'pt', 'public_holiday_liberation_day_pt', 'Liberation Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'pt', 'public_holiday_labour_day_pt', 'Labour Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-03', 'pt', 'public_holiday_corpus_christi_pt', 'Corpus Christi', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-10', 'pt', 'public_holiday_national_day_pt', 'National Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-08-15', 'pt', 'public_holiday_assumption_day', 'Assumption Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-10-05', 'pt', 'public_holiday_republic_day_pt', 'Republic Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-01', 'pt', 'public_holiday_all_saints_day_pt', 'All Saints Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-01', 'pt', 'public_holiday_independence_restoration_day_pt', 'Independence Restoration Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-08', 'pt', 'public_holiday_immaculate_conception_pt', 'Immaculate Conception', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'pt', 'public_holiday_christmas_day_pt', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'ro', 'public_holiday_new_years_day_ro', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-02', 'ro', 'public_holiday_new_year_holiday_ro', 'New Year Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-24', 'ro', 'public_holiday_u_o_t_r_p_ro', 'Union of the Romanian Principalities', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-30', 'ro', 'public_holiday_orthodox_good_friday_ro', 'Orthodox Good Friday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'ro', 'public_holiday_labor_day_ro', 'Labor Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-02', 'ro', 'public_holiday_orthodox__easter_sunday_ro', 'Orthodox Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-03', 'ro', 'public_holiday_orthodox_easter_monday_ro', 'Orthodox Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-01', 'ro', 'public_holiday_childrens_day_ro', 'Childrens Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-20', 'ro', 'public_holiday_orthodox_whit_sunday_ro', 'Orthodox Whit Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-21', 'ro', 'public_holiday_orthodox_whit_monday_ro', 'Orthodox Whit Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-08-15', 'ro', 'public_holiday_assumption_day_ro', 'Assumption Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-30', 'ro', 'public_holiday_feast_of_saint_andrew_ro', 'Feast of Saint Andrew', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-01', 'ro', 'public_holiday_great_union_day_ro', 'Great Union Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'ro', 'public_holiday_christmas_day_ro', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'ro', 'public_holiday_christmas_day_2_ro', '2nd Day of Christmas', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'se', 'public_holiday_new_years_day_se', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-06', 'se', 'public_holiday_epiphany_se', 'Epiphany', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-02', 'se', 'public_holiday_good_friday_se', 'Good Friday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-04', 'se', 'public_holiday_easter_sunday_se', 'Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'se', 'public_holiday_easter_monday_se', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'se', 'public_holiday_may_day_se', 'May Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-13', 'se', 'public_holiday_ascension_day_se', 'Ascension Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-23', 'se', 'public_holiday_whit_sunday_se', 'Whit Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-06', 'se', 'public_holiday_national_day_se', 'National Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-26', 'se', 'public_holiday_midsummer_day_se', 'Midsummer Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-06', 'se', 'public_holiday_all_saints_day_se', 'All Saints Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-25', 'se', 'public_holiday_christmas_day_se', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'se', 'public_holiday_christmas_day_2_se', '2nd Day of Christmas', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'si', 'public_holiday_new_years_day_si', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-02', 'si', 'public_holiday_new_year_holiday_si', 'New Year Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-02-08', 'si', 'public_holiday_preseren_day_si', 'Prešeren Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-04', 'si', 'public_holiday_easter_sunday_si', 'Easter Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-05', 'si', 'public_holiday_easter_monday_si', 'Easter Monday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-04-27', 'si', 'public_holiday_resistance_day_si', 'Resistance Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-01', 'si', 'public_holiday_may_day_si', 'May Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-02', 'si', 'public_holiday_may_day_holiday_si', 'May Day Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-23', 'si', 'public_holiday_whit_suday_si', 'Whit Sunday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-06-25', 'si', 'public_holiday_national_day_si', 'National Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-08-15', 'si', 'public_holiday_assumption_day_si', 'Assumption Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-10-31', 'si', 'public_holiday_reformation_day_si', 'Reformation Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-11-01', 'si', 'public_holiday_all_saints_day_si', 'All Saints Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-24', 'si', 'public_holiday_christmas_day_si', 'Christmas Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-26', 'si', 'public_holiday_independence_and_unity_day_si', 'Independence and Unity Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-01-01', 'ae', 'public_holiday_new_years_day_ae', 'New Years Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-13', 'ae', 'public_holiday_eid_alfitr_ae', 'Eid al-Fitr', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-14', 'ae', 'public_holiday_eid_alfitr_holiday_ae', 'Eid al-Fitr Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-05-15', 'ae', 'public_holiday_eid_alfitr_holiday_ae', 'Eid al-Fitr Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-07-19', 'ae', 'public_holiday_arafat_day_ae', 'Arafat Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-07-20', 'ae', 'public_holiday_eid_al_adha_ae', 'Eid al-Adha', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-07-21', 'ae', 'public_holiday_eid_al_adha_holiday_ae', 'Eid al-Adha Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-07-22', 'ae', 'public_holiday_eid_al_adha_holiday_ae', 'Eid al-Adha Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-08-09', 'ae', 'public_holiday_islamic_new_year_ae', 'Islamic New Year', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-10-18', 'ae', 'public_holiday_prophet_muhammads_brithday_ae', 'Prophet Muhammads Birthday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-01', 'ae', 'public_holiday_commemoration_day_ae', 'Commemoration Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-02', 'ae', 'public_holiday_national_day_ae', 'National Day', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' ),
( '2021-12-03', 'ae', 'public_holiday_national_day_holiday_ae', 'National Day Holiday', '3' , 'ALL', 2, 'DEV-6639', '2020-12-22 16:00:00' );

INSERT
	INTO
	`dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`)
VALUES
('ae', 'ttwa-base', 'public_holiday_new_years_day_ae', 'New Years Day', '1'),
('ae', 'ttwa-base', 'public_holiday_eid_alfitr_ae', 'Eid al-Fitr', '1'),
('ae', 'ttwa-base', 'public_holiday_eid_alfitr_holiday_ae', 'Eid al-Fitr Holiday', '1'),
('ae', 'ttwa-base', 'public_holiday_arafat_day_ae', 'Arafat Day', '1'),
('ae', 'ttwa-base', 'public_holiday_eid_al_adha_ae', 'Eid al-Adha', '1'),
('ae', 'ttwa-base', 'public_holiday_eid_al_adha_holiday_ae', 'Eid al-Adha Holiday', '1'),
('ae', 'ttwa-base', 'public_holiday_islamic_new_year_ae', 'Islamic New Year', '1'),
('ae', 'ttwa-base', 'public_holiday_prophet_muhammads_brithday_ae', 'Prophet Muhammads Birthday', '1'),
('ae', 'ttwa-base', 'public_holiday_commemoration_day_ae', 'Commemoration Day', '1'),
('ae', 'ttwa-base', 'public_holiday_national_day_ae', 'National Day', '1'),
('ae', 'ttwa-base', 'public_holiday_national_day_holiday_ae', 'National Day Holiday', '1'),
('si', 'ttwa-base', 'public_holiday_new_years_day_si', 'New Years Day', '1'),
('si', 'ttwa-base', 'public_holiday_new_year_holiday_si', 'New Year Holiday', '1'),
('si', 'ttwa-base', 'public_holiday_preseren_day_si', 'Prešeren Day', '1'),
('si', 'ttwa-base', 'public_holiday_easter_sunday_si', 'Easter Sunday', '1'),
('si', 'ttwa-base', 'public_holiday_easter_monday_si', 'Easter Monday', '1'),
('si', 'ttwa-base', 'public_holiday_resistance_day_si', 'Resistance Day', '1'),
('si', 'ttwa-base', 'public_holiday_may_day_si', 'May Day', '1'),
('si', 'ttwa-base', 'public_holiday_may_day_holiday_si', 'May Day Holiday', '1'),
('si', 'ttwa-base', 'public_holiday_whit_suday_si', 'Whit Sunday', '1'),
('si', 'ttwa-base', 'public_holiday_national_day_si', 'National Day', '1'),
('si', 'ttwa-base', 'public_holiday_assumption_day_si', 'Assumption Day', '1'),
('si', 'ttwa-base', 'public_holiday_reformation_day_si', 'Reformation Day', '1'),
('si', 'ttwa-base', 'public_holiday_all_saints_day_si', 'All Saints Day', '1'),
('si', 'ttwa-base', 'public_holiday_christmas_day_si', 'Christmas Day', '1'),
('si', 'ttwa-base', 'public_holiday_independence_and_unity_day_si', 'Independence and Unity Day', '1'),
('se', 'ttwa-base', 'public_holiday_new_years_day_se', 'New Years Day', '1'),
('se', 'ttwa-base', 'public_holiday_epiphany_se', 'Epiphany', '1'),
('se', 'ttwa-base', 'public_holiday_good_friday_se', 'Good Friday', '1'),
('se', 'ttwa-base', 'public_holiday_easter_sunday_se', 'Easter Sunday', '1'),
('se', 'ttwa-base', 'public_holiday_easter_monday_se', 'Easter Monday', '1'),
('se', 'ttwa-base', 'public_holiday_may_day_se', 'May Day', '1'),
('se', 'ttwa-base', 'public_holiday_ascension_day_se', 'Ascension Day', '1'),
('se', 'ttwa-base', 'public_holiday_whit_sunday_se', 'Whit Sunday', '1'),
('se', 'ttwa-base', 'public_holiday_national_day_se', 'National Day', '1'),
('se', 'ttwa-base', 'public_holiday_midsummer_day_se', 'Midsummer Day', '1'),
('se', 'ttwa-base', 'public_holiday_all_saints_day_se', 'All Saints Day', '1'),
('se', 'ttwa-base', 'public_holiday_christmas_day_se', 'Christmas Day', '1'),
('se', 'ttwa-base', 'public_holiday_christmas_day_2_se', '2nd Day of Christmas', '1'),
('ro', 'ttwa-base', 'public_holiday_new_years_day_ro', 'New Years Day', '1'),
('ro', 'ttwa-base', 'public_holiday_new_year_holiday_ro', 'New Year Holiday', '1'),
('ro', 'ttwa-base', 'public_holiday_u_o_t_r_p_ro', 'Union of the Romanian Principalities', '1'),
('ro', 'ttwa-base', 'public_holiday_orthodox_good_friday_ro', 'Orthodox Good Friday', '1'),
('ro', 'ttwa-base', 'public_holiday_labor_day_ro', 'Labor Day', '1'),
('ro', 'ttwa-base', 'public_holiday_orthodox__easter_sunday_ro', 'Orthodox Easter Sunday', '1'),
('ro', 'ttwa-base', 'public_holiday_orthodox_easter_monday_ro', 'Orthodox Easter Monday', '1'),
('ro', 'ttwa-base', 'public_holiday_childrens_day_ro', 'Childrens Day', '1'),
('ro', 'ttwa-base', 'public_holiday_orthodox_whit_sunday_ro', 'Orthodox Whit Sunday', '1'),
('ro', 'ttwa-base', 'public_holiday_orthodox_whit_monday_ro', 'Orthodox Whit Monday', '1'),
('ro', 'ttwa-base', 'public_holiday_assumption_day_ro', 'Assumption Day', '1'),
('ro', 'ttwa-base', 'public_holiday_feast_of_saint_andrew_ro', 'Feast of Saint Andrew', '1'),
('ro', 'ttwa-base', 'public_holiday_great_union_day_ro', 'Great Union Day', '1'),
('ro', 'ttwa-base', 'public_holiday_christmas_day_ro', 'Christmas Day', '1'),
('ro', 'ttwa-base', 'public_holiday_christmas_day_2_ro', '2nd Day of Christmas', '1'),
('pt', 'ttwa-base', 'public_holiday_new_years_day_pt', 'New Years Day', '1'),
('pt', 'ttwa-base', 'public_holiday_goog_friday_pt', 'Good Friday', '1'),
('pt', 'ttwa-base', 'public_holiday_easter_sunday_pt', 'Easter Sunday', '1'),
('pt', 'ttwa-base', 'public_holiday_liberation_day_pt', 'Liberation Day', '1'),
('pt', 'ttwa-base', 'public_holiday_labour_day_pt', 'Labour Day', '1'),
('pt', 'ttwa-base', 'public_holiday_corpus_christi_pt', 'Corpus Christi', '1'),
('pt', 'ttwa-base', 'public_holiday_national_day_pt', 'National Day', '1'),
('pt', 'ttwa-base', 'public_holiday_assumption_day', 'Assumption Day', '1'),
('pt', 'ttwa-base', 'public_holiday_republic_day_pt', 'Republic Day', '1'),
('pt', 'ttwa-base', 'public_holiday_all_saints_day_pt', 'All Saints Day', '1'),
('pt', 'ttwa-base', 'public_holiday_independence_restoration_day_pt', 'Independence Restoration Day', '1'),
('pt', 'ttwa-base', 'public_holiday_immaculate_conception_pt', 'Immaculate Conception', '1'),
('pt', 'ttwa-base', 'public_holiday_christmas_day_pt', 'Christmas Day', '1'),
('pl', 'ttwa-base', 'public_holiday_new_years_day_pl', 'New Years Day', '1'),
('pl', 'ttwa-base', 'public_holiday_epiphany_pl', 'Epiphany', '1'),
('pl', 'ttwa-base', 'public_holiday_easter_sunday_pl', 'Easter Sunday', '1'),
('pl', 'ttwa-base', 'public_holiday_easter_monday_pl', 'Easter Monday', '1'),
('pl', 'ttwa-base', 'public_holiday_labour_day_pl', 'Labour Day', '1'),
('pl', 'ttwa-base', 'public_holiday_constituation_day_pl', 'Constitution Day', '1'),
('pl', 'ttwa-base', 'public_holiday_white_sunday_pl', 'Whit Sunday', '1'),
('pl', 'ttwa-base', 'public_holiday_corpus_christi_pl', 'Corpus Christi', '1'),
('pl', 'ttwa-base', 'public_holiday_assumption_day_pl', 'Assumption Day', '1'),
('pl', 'ttwa-base', 'public_holiday_all_saints_day_pl', 'All Saints Day', '1'),
('pl', 'ttwa-base', 'public_holiday_independence_day_pl', 'Independence Day', '1'),
('pl', 'ttwa-base', 'public_holiday_christmas_day_pl', 'Christmas Day', '1'),
('pl', 'ttwa-base', 'public_holiday_christmas_day_2_pl', '2nd Day of Christmas', '1'),
('no', 'ttwa-base', 'public_holiday_new_years_day_no', 'New Years Day', '1'),
('no', 'ttwa-base', 'public_holiday_maundy_thursday_no', 'Maundy Thursday', '1'),
('no', 'ttwa-base', 'public_holiday_good_friday_no', 'Good Friday', '1'),
('no', 'ttwa-base', 'public_holiday_easter_sunday_no', 'Easter Sunday', '1'),
('no', 'ttwa-base', 'public_holiday_easter_monday_no', 'Easter Monday', '1'),
('no', 'ttwa-base', 'public_holiday_labour_day_no', 'Labour Day', '1'),
('no', 'ttwa-base', 'public_holiday_ascension_day_no', 'Ascension Day', '1'),
('no', 'ttwa-base', 'public_holiday_constitution_day_no', 'Constitution Day', '1'),
('no', 'ttwa-base', 'public_holiday_whit_sunday_no', 'Whit Sunday', '1'),
('no', 'ttwa-base', 'public_holiday_whit_monday_no', 'Whit Monday', '1'),
('no', 'ttwa-base', 'public_holiday_christmas_day_no', 'Christmas Day', '1'),
('no', 'ttwa-base', 'public_holiday_christmas_day_2_no', '2nd Day of Christmas', '1'),
('lv', 'ttwa-base', 'public_holiday_new_years_day_lv', 'New Years Day', '1'),
('lv', 'ttwa-base', 'public_holiday_good_friday_lv', 'Good Friday', '1'),
('lv', 'ttwa-base', 'public_holiday_easter_monday_lv', 'Easter Monday', '1'),
('lv', 'ttwa-base', 'public_holiday_labour_day_lv', 'Labour Day', '1'),
('lv', 'ttwa-base', 'public_holiday_independence_restoration_day_lv', 'Independence Restoration Day', '1'),
('lv', 'ttwa-base', 'public_holiday_midsummers_eve_lv', 'Midsummers Eve', '1'),
('lv', 'ttwa-base', 'public_holiday_st_johns_day_lv', 'St Johns Day', '1'),
('lv', 'ttwa-base', 'public_holiday__independence_day_lv', 'Independence Day', '1'),
('lv', 'ttwa-base', 'public_holiday_christmas_eve_lv', 'Christmas Eve', '1'),
('lv', 'ttwa-base', 'public_holiday_christmas_day_lv', 'Christmas Day', '1'),
('lv', 'ttwa-base', 'public_holiday_christmas_day_2_lv', '2nd Day of Christmas', '1'),
('lv', 'ttwa-base', 'public_holiday_new_years_eve_lv', 'New Years Eve', '1'),
('ru', 'ttwa-base', 'public_holiday_ru_spring_and_labour_day_holiday', 'Spring and Labour Day Holiday', '1'),
('ru', 'ttwa-base', 'public_holiday_ru_day_of_russia_holiday', 'Day of Russia Holiday', '1'),
('az', 'ttwa-base', 'public_holiday_new_year_holiday_3_az', 'New Year Holiday 3', '1'),
('az', 'ttwa-base', 'public_holiday_new_year_holiday_4_az', 'New Year Holiday 4', '1'),
('az', 'ttwa-base', 'ramazan_bayram_holiday', 'Ramazan Bayram Holiday', '1'),
('az', 'ttwa-base', 'public_holiday_armed_forces_day_azer_holiday', 'Armed Forces Day Holiday', '1'),
('md', 'ttwa-base', 'public_holiday_easter_of_blajini_md', 'Easter of Blajini', '1'),
('nl', 'ttwa-base', 'public_holiday_whit_sunday_nl', 'Whit Sunday', '1'),
('bin', 'ttwa-base' , 'public_holiday_new_years_day_bin', 'New Years Day', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_new_years_day_holiday_bin', 'New Year Holiday', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_ortodox_christmas_eve_bin', 'Orthodox Christmas Eve', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_ortodox_christmas_day_bin', 'Orthodox Christmas Day', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_republic_day_bin', 'Republic Day', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_orthodox_new_year_bin', 'Orthodox New Year', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_independence_day_bin', 'Independence Day', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_catholic_easter_sunday_bin', 'Catholic Easter Sunday', '1'),
('bin', 'ttwa-base' , 'public_holiday_catholic_easter_monday_bin', 'Catholic Easter Monday', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_orthodox_good_friday_bin', 'Orthodox Good Friday', '1'),
('bin', 'ttwa-base' , 'public_holiday_labour_day_bin', 'Labour Day', '1'),
('bin', 'ttwa-base' , 'public_holiday_orthodox_easter_sunday_bin', 'Orthodox Easter Sunday', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_orthodoy_easter_monday_bin', 'Orthodox Easter Monday', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_victory_day_bin', 'Victory Day', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_ramadan_bajram_bin', 'Ramadan Bajram', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_st_vitus_day_bin', 'St Vitus Day', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_kurban_bajram_bin', 'Kurban Bajram', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_all_saints_day_bin', 'All Saints Day', '1'),
( 'bin', 'ttwa-base' , 'public_holiday_dayton_agreement_day_bin', 'Dayton Agreement Day', '1'),
('bin', 'ttwa-base' , 'public_holiday_catholic_christmas_day_bin', 'Catholic Christmas Day', '1'),
('be', 'ttwa-base', 'public_holiday_new_years_day_be', 'New Years Day', '1'),
('be', 'ttwa-base', 'public_holiday_easter_monday_be', 'Easter Monday', '1'),
('be', 'ttwa-base', 'public_holiday_labour_day_be', 'Labour Day', '1'),
('be', 'ttwa-base', 'public_holiday_ascension_day_be', 'Ascension Day', '1'),
('be', 'ttwa-base', 'public_holiday_whit_monday_be', 'Whit Monday', '1'),
('be', 'ttwa-base', 'public_holiday_national_day_be', 'National Day', '1'),
('be', 'ttwa-base', 'public_holiday_assumption_day_be', 'Assumption Day', '1'),
('be', 'ttwa-base', 'public_holiday_all_saints_day_be', 'All Saints Day', '1'),
('be', 'ttwa-base', 'public_holiday_armistice_day_be', 'Armistice Day', '1'),
('be', 'ttwa-base', 'public_holiday_christmas_day_be', 'Christmas Day', '1'),
('bg', 'ttwa-base', 'public_holiday_new_years_day_bg', 'New Years Day', '1'),
('bg', 'ttwa-base', 'public_holiday_liberation_day_bg', 'Liberation Day', '1'),
('bg', 'ttwa-base', 'public_holiday_ortodox_good_friday_bg', 'Orthodox Good Friday', '1'),
('bg', 'ttwa-base', 'public_holiday_ortodox_easter_saturday_bg', 'Orthodox Easter Saturday', '1'),
('bg', 'ttwa-base', 'public_holiday_ortodox_easter_sunday_bg', 'Orthodox Easter Sunday', '1'),
('bg', 'ttwa-base', 'public_holiday_ortodox_easter_monday_bg', 'Orthodox Easter Monday', '1'),
('bg', 'ttwa-base', 'public_holiday_saint_georges_day_bg', 'Saint Georges Day / Army Day', '1'),
('bg', 'ttwa-base', 'public_holiday_culture_and_literacy_day_bg', 'Culture and Literacy Day', '1'),
('bg', 'ttwa-base', 'public_holiday_unification_day_bg', 'Unification Day', '1'),
('bg', 'ttwa-base', 'public_holiday_independence_day_bg', 'Independence Day', '1'),
('bg', 'ttwa-base', 'public_holiday_day_of_the_bulgarion_enlighteneres_bg', 'Day of the Bulgarian Enlighteners *', '1'),
('bg', 'ttwa-base', 'public_holiday_christmas_eve_bg', 'Christmas Eve', '1'),
('bg', 'ttwa-base', 'public_holiday_christmas_day_bg', 'Christmas Day', '1'),
('bg', 'ttwa-base', 'public_holiday_christmas_day_2_bg', '2nd Day of Christmas', '1'),
('de', 'ttwa-base', 'public_holiday_new_years_day_de', 'New Years Day', '1'),
('de', 'ttwa-base', 'public_holiday_good_friday_de', 'Good Friday', '1'),
('de', 'ttwa-base', 'public_holiday_easter_monday_de', 'Easter Monday', '1'),
('de', 'ttwa-base', 'public_holiday__labour_day_de', 'Labour Day', '1'),
('de', 'ttwa-base', 'public_holiday_ascension_day_de', 'Ascension Day', '1'),
('de', 'ttwa-base', 'public_holiday_whit_monday_de', 'Whit Monday', '1'),
('de', 'ttwa-base', 'public_holiday_day_of_german_unity_de', 'Day of German Unity', '1'),
('de', 'ttwa-base', 'public_holiday_christmas_day_2_de', '2nd Day of Christmas', '1'),
('dk', 'ttwa-base', 'public_holiday_new_years_day_dk', 'New Years Day', '1'),
('dk', 'ttwa-base', 'public_holiday_maundy_thursday_de', 'Maundy Thursday', '1'),
('dk', 'ttwa-base', 'public_holiday_good_friday_de', 'Good Friday', '1'),
('dk', 'ttwa-base', 'public_holiday_easter_sunday_de', 'Easter Sunday', '1'),
('dk', 'ttwa-base', 'public_holiday_easter_monday_de', 'Easter Monday', '1'),
('dk', 'ttwa-base', 'public_holiday_prayer_day_de', 'Prayer Day', '1'),
('dk', 'ttwa-base', 'public_holiday_ascension_day_de', 'Ascension Day', '1'),
('dk', 'ttwa-base', 'public_holiday_whit_sunday_de', 'Whit Sunday', '1'),
('dk', 'ttwa-base', 'public_holiday_whit_monday_de', 'Whit Monday', '1'),
('dk', 'ttwa-base', 'public_holiday_christmas_day_de', 'Christmas Day', '1'),
('en', 'ttwa-base', 'public_holiday_may_day_en', 'May Day', '1'),
('en', 'ttwa-base', 'public_holiday_late_may_bank_holiday_en', 'Late May Bank Holiday', '1'),
('en', 'ttwa-base', 'public_holiday_august_bank_holiday_en', 'August Bank Holiday', '1'),
('en', 'ttwa-base', 'public_holiday_boxing_day_en', 'Boxing Day', '1'),
('est', 'ttwa-base', 'public_holiday_new_years_day_est', 'New Years Day', '1'),
('est', 'ttwa-base', 'public_holiday_independence_day_est', 'Independence Day', '1'),
('est', 'ttwa-base', 'public_holiday_good_fridday_est', 'Good Friday', '1'),
('est', 'ttwa-base', 'public_holiday_easter_sunday_est', 'Easter Sunday', '1'),
('est', 'ttwa-base', 'public_holiday_spring_day_est', 'Spring Day', '1'),
('est', 'ttwa-base', 'public_holiday_white_sunday_est', 'Whit Sunday', '1'),
('est', 'ttwa-base', 'public_holiday_victory_day_est', 'Victory Day', '1'),
('est', 'ttwa-base', 'public_holiday_midsummer_day_est', 'Midsummer Day', '1'),
('est', 'ttwa-base', 'public_holiday_independence_restoration_day_est', 'Independence Restoration Day', '1'),
('est', 'ttwa-base', 'public_holiday_christmas_eve_est', 'Christmas Eve', '1'),
('est', 'ttwa-base', 'public_holiday_christmas_day_est', 'Christmas Day', '1'),
('est', 'ttwa-base', 'public_holiday_christmas_day_2_est', '2nd Day of Christmas', '1'),
('fi', 'ttwa-base', 'public_holiday_new_years_day_fi', 'New Years Day', '1'),
('fi', 'ttwa-base', 'public_holiday_epiphay_fi', 'Epiphany', '1'),
('fi', 'ttwa-base', 'public_holiday_good_friday_fi', 'Good Friday', '1'),
('fi', 'ttwa-base', 'public_holiday_easter_sunday_fi', 'Easter Sunday', '1'),
('fi', 'ttwa-base', 'public_holiday_easter_monday_fi', 'Easter Monday', '1'),
('fi', 'ttwa-base', 'public_holiday_may_day_fi', 'May Day', '1'),
('fi', 'ttwa-base', 'public_holiday_ascension_day_fi', 'Ascension Day', '1'),
('fi', 'ttwa-base', 'public_holiday_whit_sunday_fi', 'Whit Sunday', '1'),
('fi', 'ttwa-base', 'public_holiday_midsummer_day_fi', 'Midsummer Day', '1'),
('fi', 'ttwa-base', 'public_holiday_all_saints_day_fi', 'All Saints Day', '1'),
('fi', 'ttwa-base', 'public_holiday_independence_day_fi', 'Independence Day', '1'),
('fi', 'ttwa-base', 'public_holiday_christmas_eve_fi', 'Christmas Eve', '1'),
('fi', 'ttwa-base', 'public_holiday_christmas_day_fi', 'Christmas Day', '1'),
('fi', 'ttwa-base', 'public_holiday_christmas_day_2_fi', '2nd Day of Christmas', '1'),
('fr', 'ttwa-base', 'public_holiday_new_years_day_fr', 'New Years Day', '1'),
('fr', 'ttwa-base', 'public_holiday_easter_monday_fr', 'Easter Monday', '1'),
('fr', 'ttwa-base', 'public_holiday_labour_day_fr', 'Labour Day', '1'),
('fr', 'ttwa-base', 'public_holiday_victory_day_fr', 'Victory Day', '1'),
('fr', 'ttwa-base', 'public_holiday_ascension_day_fr', 'Ascension Day', '1'),
('fr', 'ttwa-base', 'public_holiday_whit_sunday_fr', 'Whit Sunday', '1'),
('fr', 'ttwa-base', 'public_holiday_whit_monday_fr', 'Whit Monday', '1'),
('fr', 'ttwa-base', 'public_holiday_bastiller_day_fr', 'Bastille Day', '1'),
('fr', 'ttwa-base', 'public_holiday_assumption_day_fr', 'Assumption Day', '1'),
('fr', 'ttwa-base', 'public_holiday_all_saints_day_fr', 'All Saints Day', '1'),
('fr', 'ttwa-base', 'public_holiday_armistice_day_fr', 'Armistice Day', '1'),
('fr', 'ttwa-base', 'public_holiday_christmas_day_fr', 'Christmas Day', '1'),
('gr', 'ttwa-base', 'public_holiday_new_years_day_gr', 'New Years Day', '1'),
('gr', 'ttwa-base', 'public_holiday_epiphany_gr', 'Epiphany', '1'),
('gr', 'ttwa-base', 'public_holiday_orthodox_ash_monday_gr', 'Orthodox Ash Monday', '1'),
('gr', 'ttwa-base', 'public_holiday_independence_day_gr', 'Independence Day', '1'),
('gr', 'ttwa-base', 'public_holiday_orthodox_good_friday_gr', 'Orthodox Good Friday', '1'),
('gr', 'ttwa-base', 'public_holiday_labour_day_gr', 'Labour Day', '1'),
('gr', 'ttwa-base', 'public_holiday_orthodox_easter_sunday_gr', 'Orthodox Easter Sunday', '1'),
('gr', 'ttwa-base', 'public_holiday_orthodox_eeaster_monday_gr', 'Orthodox Easter Monday', '1'),
('gr', 'ttwa-base', 'public_holiday_orthodox_whit_sunday_gr', 'Orthodox Whit Sunday', '1'),
('gr', 'ttwa-base', 'public_holiday_orthodox_whit_monday_gr', 'Orthodox Whit Monday', '1'),
('gr', 'ttwa-base', 'public_holiday_assumption_day_gr', 'Assumption Day', '1'),
('gr', 'ttwa-base', 'public_holiday_ochi_day_gr', 'Ochi Day', '1'),
('gr', 'ttwa-base', 'public_holiday_christmas_day_gr', 'Christmas Day', '1'),
('gr', 'ttwa-base', 'public_holiday_christmas_day_2_gr', '2nd Day of Christmas', '1'),
('hr', 'ttwa-base', 'public_holiday_new_years_day_hr', 'New Years Day', '1'),
('hr', 'ttwa-base', 'public_holiday_epiphany_hr', 'Epiphany', '1'),
('hr', 'ttwa-base', 'public_holiday_easter_monday_hr', 'Easter Monday', '1'),
('hr', 'ttwa-base', 'public_holiday_labour_day_hr', 'Labour Day', '1'),
('hr', 'ttwa-base', 'public_holiday_statehood_day_hr', 'Statehood Day', '1'),
('hr', 'ttwa-base', 'public_holiday_corpus_chrisit_hr', 'Corpus Christi', '1'),
('hr', 'ttwa-base', 'public_holiday_anti_fascist_resistance_day_hr', 'Anti-Fascist Resistance Day', '1'),
('hr', 'ttwa-base', 'public_holiday_v_and_h_t_day_hr', 'Victory and Homeland Thanksgiving Day', '1'),
('hr', 'ttwa-base', 'public_holiday_assumption_day_hr', 'Assumption Day', '1'),
('hr', 'ttwa-base', 'public_holiday_independence_day_hr', 'Independence Day', '1'),
('hr', 'ttwa-base', 'public_holiday_all_saints_day_hr', 'All Saints Day', '1'),
('hr', 'ttwa-base', 'public_holiday_remembrance_day_hr', 'Remembrance Day', '1'),
('hr', 'ttwa-base', 'public_holiday_christmas_day_hr', 'Christmas Day', '1'),
('hr', 'ttwa-base', 'public_holiday_st_stephens_day_hr', 'St Stephens Day', '1'),
('hu', 'ttwa-base', 'public_holiday_revolution_day_hu', 'Revolution Day', '1'),
('hu', 'ttwa-base', 'public_holiday_saint_stephens_day_hu', 'Saint Stephens Day', '1'),
('hu', 'ttwa-base', 'public_holiday_all_saints_day_hu', 'All Saints Day', '1'),
('hu', 'ttwa-base', 'public_holiday_christmas_day_2_hu', '2nd Day of Christmas', '1'),
('it', 'ttwa-base', 'public_holiday_new_years_day_it', 'New Years Day', '1'),
('it', 'ttwa-base', 'public_holiday_epiphany_it', 'Epiphany', '1'),
('it', 'ttwa-base', 'public_holiday_easter_sunday_it', 'Easter Sunday', '1'),
('it', 'ttwa-base', 'public_holiday_easter_monday_it', 'Easter Monday', '1'),
('it', 'ttwa-base', 'public_holiday_liberation_day_it', 'Liberation Day', '1'),
('it', 'ttwa-base', 'public_holiday_international_workers_day_it', 'International Workers Day', '1'),
('it', 'ttwa-base', 'public_holiday_republic_day_it', 'Republic Day', '1'),
('it', 'ttwa-base', 'public_holiday_assumption_day_it', 'Assumption Day', '1'),
('it', 'ttwa-base', 'public_holiday_all_saints_day_it', 'All Saints Day', '1'),
('it', 'ttwa-base', 'public_holiday_immaculate_conception_it', 'Immaculate Conception', '1'),
('it', 'ttwa-base', 'public_holiday_christmas_day_it', 'Christmas Day', '1'),
('it', 'ttwa-base', 'public_holiday_st_stephens_day_it', 'St Stephens Day', '1'),
('lt', 'ttwa-base', 'public_holiday_new_years_day_lt', 'New Years Day', '1'),
('lt', 'ttwa-base', 'public_holiday_independence_day_lt', 'Independence Day', '1'),
('lt', 'ttwa-base', 'public_holiday_independence_lt', 'Independence Restoration Day', '1'),
('lt', 'ttwa-base', 'public_holiday_easter_sunday_lt', 'Easter Sunday', '1'),
('lt', 'ttwa-base', 'public_holiday_easter_monday_lt', 'Easter Monday', '1'),
('lt', 'ttwa-base', 'public_holiday_labour_day_lt', 'Labour Day', '1'),
('lt', 'ttwa-base', 'public_holiday_mothers_day_lt', 'Mothers Day', '1'),
('lt', 'ttwa-base', 'public_holiday_fathers_day_lt', 'Fathers Day', '1'),
('lt', 'ttwa-base', 'public_holiday_st_johns_day_lt', 'St Johns Day', '1'),
('lt', 'ttwa-base', 'public_holiday_king_mindaugas_day_lt', 'King Mindaugas Day', '1'),
('lt', 'ttwa-base', 'public_holiday_assumption_day_lt', 'Assumption Day', '1'),
('lt', 'ttwa-base', 'public_holiday_all_saints_day_lt', 'All Saints Day', '1'),
('lt', 'ttwa-base', 'public_holiday_all_souls_day_lt', 'All Souls Day', '1'),
('lt', 'ttwa-base', 'public_holiday_christmas_eve_lt', 'Christmas Eve', '1'),
('lt', 'ttwa-base', 'public_holiday_christmas_day_lt', 'Christmas Day', '1'),
('lt', 'ttwa-base', 'public_holiday_christmas_day_2_lt', '2nd Day of Christmas', '1'),
('en', 'ttwa-base', 'lang_az', 'Azer', '1'),
('pt', 'ttwa-base', 'lang_az', 'Azer', '1'),
('hu', 'ttwa-base', 'lang_az', 'Azeri', '1'),
('en', 'ttwa-base', 'lang_bih', 'Bosnia and Herzegovina', '1'),
('pt', 'ttwa-base', 'lang_bih', 'Bosnia and Herzegovina', '1'),
('hu', 'ttwa-base', 'lang_bih', 'Bosznia-Hercegovina', '1'),
('en', 'ttwa-base', 'lang_by', 'Belarus', '1'),
('pt', 'ttwa-base', 'lang_by', 'Belarus', '1'),
('hu', 'ttwa-base', 'lang_by', 'Fehéroroszország', '1'),
('en', 'ttwa-base', 'lang_md', 'Moldova', '1'),
('pt', 'ttwa-base', 'lang_md', 'Moldova', '1'),
('hu', 'ttwa-base', 'lang_md', 'Moldova', '1'),
('en', 'ttwa-base', 'lang_ae', 'United Arab Emirates', '1'),
('pt', 'ttwa-base', 'lang_ae', 'United Arab Emirates', '1'),
('hu', 'ttwa-base', 'lang_ae', 'Egyesült Arab Emirátusok', '1'),
('en', 'ttwa-base', 'lang_ua', 'Ukraine', '1'),
('pt', 'ttwa-base', 'lang_ua', 'Ukraine', '1'),
('hu', 'ttwa-base', 'lang_ua', 'Ukrán', '1')
ON DUPLICATE KEY UPDATE `dict_id` = VALUES(`dict_id`);

UPDATE `_sql_version` SET `revision`=23, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -23--2020.12.22.-15:00----------------------------------------------------------------

INSERT
	INTO
	`dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`)
VALUES
('en', 'ttwa-base', 'lang_biel', 'Belarus', '1'),
('pt', 'ttwa-base', 'lang_biel', 'Belarus', '1'),
('hu', 'ttwa-base', 'lang_biel', 'Belarusz', '1'),
('en', 'ttwa-base', 'lang_nl', 'Dutch', '1'),
('pt', 'ttwa-base', 'lang_nl', 'Dutch', '1'),
('hu', 'ttwa-base', 'lang_nl', 'Holland', '1'),
('en', 'ttwa-base', 'lang_bin', 'Bosnia and Herzegovina', '1'),
('pt', 'ttwa-base', 'lang_bin', 'Bosnia and Herzegovina', '1'),
('hu', 'ttwa-base', 'lang_bin', 'Bosznia-Hercegovina', '1');
UPDATE `dictionary`
	SET `valid`=0
	WHERE `dict_id` = "lang_bih";

UPDATE `_sql_version` SET `revision`=24, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -24--2020.12.23.-14:00----------------------------------------------------------------

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`)
VALUES
    ('pt', 'ttwa-base', 'log_in', 'Ligar normal', '1'),
    ('pt', 'ttwa-base', 'log_out', 'Desligar normal', '1');

UPDATE `_sql_version` SET `revision`=25, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -25--2020.12.18.-09:30----------------------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('NOT');
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('HS751') AND `note` = 'OTHER-s munkacsoport túlóra hétköznap 1 órán felül';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('HS501') AND `note` = 'OTHER-s munkacsoport túlóra hétköznap';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('HS101') AND `note` = 'OTHER-s munkacsoport túlóra hétvége';
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'NOT', 'inside_type_id', NULL, NULL, 0, 'data.`dt__total_wtej_sum` + data.`dt__total_otej_sum` + data.`dt__total_otwej_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`dt__total_wtej_sum` + data.`dt__total_otej_sum` + data.`dt__total_otwej_sum`) > 0 AND (data.`workgroup_name` LIKE \"PRT%PROD%\" OR data.`workgroup_name` LIKE \"PRT%WH%\")', 'Éjszakai órák PROD-s és WH-s mcs.', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS751', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum` - 3600', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Office%\" AND data.`dt__base_overtime_sum` > 3600 AND data.`is_restday` <> 1', 'OFFICE-s munkacsoport túlóra hétköznap 1 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS501', 'inside_type_id', NULL, NULL, 0, 'IF(data.`dt__base_overtime_sum` > 3600, 3600, data.`dt__base_overtime_sum`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Office%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` <> 1', 'OFFICE-s munkacsoport túlóra hétköznap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS101', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Flexible%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` = 1', 'Flexis munkacsoport túlóra hétvége', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS101', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Office%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` = 1', 'OFFICE-s munkacsoport túlóra hétvége', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=26, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -26--2021.01.07.-11:45----------------------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('HS501') AND `note` = 'PROD-s munkacsoport túlóra hétköznap 2 órán felül';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('FAINH') AND `note` = 'PROD-s munkacsoport túlóra hétköznap';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('HS101') AND `note` = 'PROD-s munkacsoport túlóra hétvége 2 órán felül';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('FAINH') AND `note` = 'PROD-s munkacsoport túlóra hétvége';

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'FAINH', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`daily_ia` <> 0', 'OPERATION-s munkacsoport napi egyenleg', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS101', 'inside_type_id', NULL, NULL, 0, 'data.`weekend_ot`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`weekend_ot` > 0', 'OPERATION-s munkacsoport hétvégén keletkezett túlóra', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS751', 'inside_type_id', NULL, NULL, 0, 'data.`weekday_ot` - 3600', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`weekday_ot` > 3600', 'OPERATION-s munkacsoport hétköznap keletkezett túlóra 1 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS501', 'inside_type_id', NULL, NULL, 0, 'IF(data.`weekday_ot` > 3600, 3600, data.`weekday_ot`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`weekday_ot` > 0', 'OPERATION-s munkacsoport hétköznap keletkezett túlóra 1 óráig', 2, 'ptc_sys', NULL, NULL, NULL, NULL);


UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('HS751') AND `note` = 'WH-s munkacsoport túlóra hétköznap 2 órán felül';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('FAINH') AND `note` = 'WH-s munkacsoport túlóra hétköznap';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('HS101') AND `note` = 'WH-s munkacsoport túlóra hétvége 2 órán felül';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('FAINH') AND `note` = 'WH-s munkacsoport túlóra hétvége';

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'FAINH', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`daily_ia` <> 0', 'WH-s munkacsoport napi egyenleg', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS101', 'inside_type_id', NULL, NULL, 0, 'data.`weekend_ot`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`weekend_ot` > 0', 'WH-s munkacsoport hétvégén keletkezett túlóra', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS101', 'inside_type_id', NULL, NULL, 0, 'data.`weekday_ot` - 7200', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`weekday_ot` > 7200', 'WH-s munkacsoport hétköznap keletkezett túlóra 2 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS751', 'inside_type_id', NULL, NULL, 0, 'IF(data.`weekday_ot` > 7200, 3600, data.`weekday_ot` - 3600)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`weekday_ot` > 3600', 'WH-s munkacsoport hétköznap keletkezett túlóra 1 órán felül 2 óráig', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HS501', 'inside_type_id', NULL, NULL, 0, 'IF(data.`weekday_ot` > 3600, 3600, data.`weekday_ot`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`weekday_ot` > 0', 'WH-s munkacsoport hétköznap keletkezett túlóra 1 óráig', 2, 'ptc_sys', NULL, NULL, NULL, NULL);


INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'PAINH', 'inside_type_id', NULL, NULL, 0, 'data.`frame_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`frame_ia` > 0', 'Minden munkacsoport összegyűlt egyenleg', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=27, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -27--2021.01.11.-14:30----------------------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('NOT');

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'NOT', 'inside_type_id', NULL, NULL, 0, 'data.`dt__total_wtej_sum` + data.`dt__total_otej_sum` + data.`dt__total_otwej_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`dt__total_wtej_sum` + data.`dt__total_otej_sum` + data.`dt__total_otwej_sum`) > 0 AND (data.`workgroup_name` LIKE \"Operations%\" OR data.`workgroup_name` LIKE \"WH%\") AND data.`work_type` = \"FRAMEWORK_BALANCE\"', 'Éjszakai órák OPERATION-s és WH-s mcs.', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NOT2', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`value` > 0 AND data.`inside_type_id` IN (\"botej\", \"botwej\") AND (data.`workgroup_name` LIKE \"Operations%\" OR data.`workgroup_name` LIKE \"WH%\") AND data.`work_type` = \"FRAMEWORK_BALANCE\"', 'Éjszakai órák OPERATION-s és WH-s mcs. szaldó éjszaka', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=28, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -28--2021.01.11.-15:30----------------------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('W');
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'W', 'inside_type_id', NULL, NULL, 0, 'IF((data.`workgroup_name` LIKE \"Flexible%\" OR data.`workgroup_name` LIKE \"IHT%\") AND data.`dt__schedule_sum` = 32400, 28800, IF((data.`workgroup_name` LIKE \"WH%\" OR data.`workgroup_name` LIKE \"Operations%\") AND data.`daily_ia` < 0, data.`dt__base_worktime_sum` + data.`daily_ia`, data.`dt__base_worktime_sum`))', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`dt__base_worktime_sum` > 0', 'Ledolgozott munkaidő', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=29, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -29--2021.01.11.-17:30----------------------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('FAINH');
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'FAINH', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`daily_ia` < 0', 'WH-s munkacsoport napi egyenleg negatív', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAINH', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`daily_ia` < 0', 'OPERATION-s munkacsoport napi egyenleg negatív', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'AINH', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`daily_ia` > 0', 'WH-s munkacsoport napi egyenleg pozitív', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'AINH', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`daily_ia` > 0', 'OPERATION-s munkacsoport napi egyenleg pozitív', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=30, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -30--2021.01.15.-13:30----------------------------------------------------------------

UPDATE dictionary SET dict_value = 'Departamento' WHERE dict_id = 'menu_item_company_org_group1_upload' AND lang = 'pt';
UPDATE dictionary SET dict_value = 'Secção' WHERE dict_id = 'menu_item_company_org_group2_upload' AND lang = 'pt';
UPDATE dictionary SET dict_value = 'Gestor de Tempos' WHERE dict_id = 'menu_item_company_org_group3_upload' AND lang = 'pt';

UPDATE dictionary SET dict_value = 'Department' WHERE dict_id = 'menu_item_company_org_group1_upload' AND lang = 'en';
UPDATE dictionary SET dict_value = 'Section' WHERE dict_id = 'menu_item_company_org_group2_upload' AND lang = 'en';

UPDATE dictionary SET dict_value = 'Osztály' WHERE dict_id = 'menu_item_company_org_group1_upload' AND lang = 'hu';
UPDATE dictionary SET dict_value = 'Szekció' WHERE dict_id = 'menu_item_company_org_group2_upload' AND lang = 'hu';

UPDATE `_sql_version` SET `revision`=31, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -31--2021.01.20.-13:30----------------------------------------------------------------

UPDATE dictionary SET dict_value='Uso incorreto do utente ou Palavra chave' WHERE dict_id="error_user_or_password_are_incorrect" and lang="pt";

UPDATE `_sql_version` SET `revision`=32, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -32--2021.02.01.-09:30----------------------------------------------------------------

UPDATE `auth_acl` SET `access_right` = 0 WHERE `controller_id` = 'wfm/reportregistration' AND `operation_id` = 'print';

UPDATE `_sql_version` SET `revision`=33, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -33--2021.02.11.-15:30----------------------------------------------------------------
INSERT INTO dictionary (lang, module, dict_id, dict_value,valid) VALUES
('en', 'ttwa-ahp', 'absence_type_taken_individual_adaptability_hours', 'Taken Individual Adaptability hours',1),
('hu', 'ttwa-ahp','absence_type_taken_individual_adaptability_hours','Egyedi keretet csökkentő pihenőnap',1),
('pt', 'ttwa-ahp', 'absence_type_taken_individual_adaptability_hours', 'Gozo de Adap. Individual horas',1),
('en', 'ttwa-ahp', 'absence_type_individual_adaptability_hours', 'Individual Adaptability hours',1),
('hu', 'ttwa-ahp', 'absence_type_individual_adaptability_hours', 'Egyedi keretet növelő pihenőnap',1),
('pt', 'ttwa-ahp', 'absence_type_individual_adaptability_hours', 'Adap. Individual horas',1),
('en', 'ttwa-ahp', 'absence_type_short_taken_individual_adaptability_hours', 'FAINH',1),
('hu', 'ttwa-ahp','absence_type_short_taken_individual_adaptability_hours','FAINH',1),
('pt', 'ttwa-ahp', 'absence_type_short_taken_individual_adaptability_hours', 'FAINH',1),
('en', 'ttwa-ahp', 'absence_type_short_individual_adaptability_hours', 'AINH',1),
('hu', 'ttwa-ahp', 'absence_type_short_individual_adaptability_hours', 'AINH',1),
('pt', 'ttwa-ahp', 'absence_type_short_individual_adaptability_hours', 'AINH',1),
('en', 'ttwa-ahp-core', 'absence_type_taken_individual_adaptability_hours', 'Taken Individual Adaptability hours',1),
('hu', 'ttwa-ahp-core','absence_type_taken_individual_adaptability_hours','Egyedi keretet csökkentő pihenőnap',1),
('pt', 'ttwa-ahp-core', 'absence_type_taken_individual_adaptability_hours', 'Gozo de Adap. Individual horas',1),
('en', 'ttwa-ahp-core', 'absence_type_individual_adaptability_hours', 'Individual Adaptability hours',1),
('hu', 'ttwa-ahp-core', 'absence_type_individual_adaptability_hours', 'Egyedi keretet növelő pihenőnap',1),
('pt', 'ttwa-ahp-core', 'absence_type_individual_adaptability_hours', 'Adap. Individual horas',1),
('en', 'ttwa-ahp-core', 'absence_type_short_taken_individual_adaptability_hours', 'FAINH',1),
('hu', 'ttwa-ahp-core','absence_type_short_taken_individual_adaptability_hours','FAINH',1),
('pt', 'ttwa-ahp-core', 'absence_type_short_taken_individual_adaptability_hours', 'FAINH',1),
('en', 'ttwa-ahp-core', 'absence_type_short_individual_adaptability_hours', 'AINH',1),
('hu', 'ttwa-ahp-core', 'absence_type_short_individual_adaptability_hours', 'AINH',1),
('pt', 'ttwa-ahp-core', 'absence_type_short_individual_adaptability_hours', 'AINH',1);

INSERT INTO state_type (state_type_id, name_dict_id, short_name_dict_id, bgcolor, status, absence_hour)
VALUES ('taken_individual', 'absence_type_taken_individual_adaptability_hours', 'absence_type_short_taken_individual_adaptability_hours', '#00b33c', 2, 99),
('individual_adaptability', 'absence_type_individual_adaptability_hours', 'absence_type_short_individual_adaptability_hours', '#00b33c', 2, 99);

UPDATE `_sql_version` SET `revision`=34, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -34--2021.02.18.-13:30----------------------------------------------------------------
UPDATE `app_settings` SET `setting_value` = '1', `note` = 'DEV-7828, prev value: 0' WHERE `setting_id` = 'getAllContractType';

UPDATE `_sql_version` SET `revision`=35, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -35--2021.02.22.-11:20----------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note`='prev val: 0, DEV-8030' WHERE `setting_id` = 'showForgottenPwButton';

UPDATE `_sql_version` SET `revision`=36, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -36--2021-02-24-12:39-----------------------------------------------------------------

INSERT INTO dictionary (lang, module, dict_id, dict_value,valid) VALUES
('en', 'ttwa-ahp', 'absence_type_vacation_h_d_4_h', 'Vacation (half-day, 4 h)',1),
('hu', 'ttwa-ahp','absence_type_vacation_h_d_4_h','Szabadság (4 óra)',1),
('pt', 'ttwa-ahp', 'absence_type_vacation_h_d_4_h', 'Férias (meio dia, 4 h)',1),
('en', 'ttwa-ahp-core', 'absence_type_vacation_h_d_4_h', 'Vacation (half-day, 4 h)',1),
('hu', 'ttwa-ahp-core','absence_type_vacation_h_d_4_h','Szabadság (4 óra)',1),
('pt', 'ttwa-ahp-core', 'absence_type_vacation_h_d_4_h', 'Férias (meio dia, 4 h)',1),
('en', 'ttwa-ahp', 'absence_type_short_vacation_h_d_4_h', 'VH4',1),
('hu', 'ttwa-ahp','absence_type_short_vacation_h_d_4_h','SZ4',1),
('pt', 'ttwa-ahp', 'absence_type_short_vacation_h_d_4_h', 'F4H',1),
('en', 'ttwa-ahp-core', 'absence_type_short_vacation_h_d_4_h', 'VH4',1),
('hu', 'ttwa-ahp-core','absence_type_short_vacation_h_d_4_h','SZ4',1),
('pt', 'ttwa-ahp-core', 'absence_type_short_vacation_h_d_4_h', 'F4H',1);

INSERT INTO state_type (state_type_id, name_dict_id, short_name_dict_id, bgcolor, status, absence_hour)
VALUES ('absence_type_vacation_h_d_4_h', 'absence_type_vacation_h_d_4_h', 'absence_type_short_vacation_h_d_4_h', '#c61aff', 2, 4);

INSERT INTO `link_at_to_bat` (`state_type_id`, `base_absence_type_id`) VALUES
	('absence_type_vacation_h_d_4_h', '8d92474d33de2925e59dbeb01e7602ad');

UPDATE `_sql_version` SET `revision`=37, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -37--2021-03-05-08:30-----------------------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_value`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
	('SAGE', 'FER', 'state_type_id', 'absence_type_vacation_h_d_4_h', NULL, 0, 0, NULL, NULL, 'Vacation (half-day, 4 h)', 2);

UPDATE `_sql_version` SET `revision`=38, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -38--2021-03-05-10:00-----------------------------------------------------------------
UPDATE dictionary SET dict_value = CONCAT("_", dict_value) WHERE dict_id IN("absence_type_training", "absence_type_training_leave", "absence_type_unpaid_hours", "absence_type_prenatal_consultation", "absence_type_lactation", "absence_type_indiv_adapt_h", "absence_type_legal_obl_h", "absence_type_paid_fam_ass_h", "absence_type_school_acc_h", "absence_type_studyleave_h", "absence_type_unpaid_fam_ass_h", "absence_type_justifiednotpaid_h", "absence_type_paid_illness_real_hours");

UPDATE `_sql_version` SET `revision`=39, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -39--2021-02-24-12:39-----------------------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('W');
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'W', 'inside_type_id', NULL, NULL, 0, 'IF((data.`workgroup_name` LIKE \"Flexible%\" OR data.`workgroup_name` LIKE \"IHT%\") AND data.`dt__schedule_sum` = 32400, 28800, IF((data.`workgroup_name` LIKE \"WH%\" OR data.`workgroup_name` LIKE \"Operations%\") AND data.`daily_ia` < 0, data.`dt__base_worktime_sum` + data.`daily_ia`, data.`dt__base_worktime_sum`)) - data.`dt__inside_absence_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`dt__base_worktime_sum` > 0', 'Ledolgozott munkaidő', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=40, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -40--2021-03-12-15:30-----------------------------------------------------------------

UPDATE app_lookup SET valid=0 WHERE dict_id in ("employee_contract_type_OKJ", "employee_contract_type_trainee", "employee_contract_type_learner","employee_contract_type_firm","employee_contract_type_indefinite");

UPDATE `_sql_version` SET `revision`=41, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -41--2021-03-16-16:00-----------------------------------------------------------------

UPDATE `state_type` SET `worktime` = 0 WHERE `state_type_id` IN ('absence_type_school_acc_h', 'absence_type_paid_illness_real_h', 'absence_type_studyleave_h', 'absence_type_unpaid_fam_ass_h', 'absence_type_paid_fam_ass_h', 'absence_type_justifiednotpaid_h', 'absence_type_unpaid_h', 'absence_type_training_h', 'absence_type_paid_illness_h', 'absence_type_legal_obl_h', 'absence_type_indiv_adapt_h', 'absence_type_lactation', 'absence_type_prenatal_app_h') AND `status` = 2 AND `state_type_status` = 0;

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('HS501') AND `note` = 'OPERATION-s munkacsoport hétköznap keletkezett túlóra 1 óráig';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('HS751') AND `note` = 'OPERATION-s munkacsoport hétköznap keletkezett túlóra 1 órán felül';
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'HS501', 'inside_type_id', NULL, NULL, 0, 'data.`weekday_ot`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`weekday_ot` > 0', 'OPERATION-s munkacsoport hétköznap keletkezett túlóra', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('HS751') AND `note` = 'WH-s munkacsoport hétköznap keletkezett túlóra 1 órán felül 3 óráig';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('HS101') AND `note` = 'WH-s munkacsoport hétköznap keletkezett túlóra 3 órán felül';

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'HS751', 'inside_type_id', NULL, NULL, 0, 'data.`weekday_ot` - 3600', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`weekday_ot` > 3600', 'WH-s munkacsoport hétköznap keletkezett túlóra 1 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL);


UPDATE `_sql_version` SET `revision`=42, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -42--2021-03-16-16:00-----------------------------------------------------------------

INSERT INTO `public_holiday` ( `holidaydate`, `country`, `name_dict_id`, `name`, `type`, `company_id`, `status`, `created_by`, `created_on` ) VALUES
( '2021-01-01', 'ptlisboa', 'public_holiday_new_years_day_pt', 'New Years Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-04-02', 'ptlisboa', 'public_holiday_goog_friday_pt', 'Good Friday', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-04-04', 'ptlisboa', 'public_holiday_easter_sunday_pt', 'Easter Sunday', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-04-25', 'ptlisboa', 'public_holiday_liberation_day_pt', 'Liberation Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-05-01', 'ptlisboa', 'public_holiday_labour_day_pt', 'Labour Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-06-03', 'ptlisboa', 'public_holiday_corpus_christi_pt', 'Corpus Christi', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-06-10', 'ptlisboa', 'public_holiday_national_day_pt', 'National Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-08-15', 'ptlisboa', 'public_holiday_assumption_day', 'Assumption Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-10-05', 'ptlisboa', 'public_holiday_republic_day_pt', 'Republic Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-11-01', 'ptlisboa', 'public_holiday_all_saints_day_pt', 'All Saints Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-12-01', 'ptlisboa', 'public_holiday_independence_restoration_day_pt', 'Independence Restoration Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-12-08', 'ptlisboa', 'public_holiday_immaculate_conception_pt', 'Immaculate Conception', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-12-25', 'ptlisboa', 'public_holiday_christmas_day_pt', 'Christmas Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-02-16', 'ptlisboa', 'public_holiday_national_holiday_pt', 'National Holiday', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-06-13', 'ptlisboa', 'public_holiday_national_local_holiday_lisboa_pt', 'Local holiday - Lisboa', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),

( '2021-01-01', 'ptsede', 'public_holiday_new_years_day_pt', 'New Years Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-04-02', 'ptsede', 'public_holiday_goog_friday_pt', 'Good Friday', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-04-04', 'ptsede', 'public_holiday_easter_sunday_pt', 'Easter Sunday', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-04-25', 'ptsede', 'public_holiday_liberation_day_pt', 'Liberation Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-05-01', 'ptsede', 'public_holiday_labour_day_pt', 'Labour Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-06-03', 'ptsede', 'public_holiday_corpus_christi_pt', 'Corpus Christi', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-06-10', 'ptsede', 'public_holiday_national_day_pt', 'National Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-08-15', 'ptsede', 'public_holiday_assumption_day', 'Assumption Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-10-05', 'ptsede', 'public_holiday_republic_day_pt', 'Republic Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-11-01', 'ptsede', 'public_holiday_all_saints_day_pt', 'All Saints Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-12-01', 'ptsede', 'public_holiday_independence_restoration_day_pt', 'Independence Restoration Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-12-08', 'ptsede', 'public_holiday_immaculate_conception_pt', 'Immaculate Conception', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-12-25', 'ptsede', 'public_holiday_christmas_day_pt', 'Christmas Day', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-02-16', 'ptsede', 'public_holiday_national_holiday_pt', 'National Holiday', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' ),
( '2021-07-25', 'ptsede', 'public_holiday_national_local_holiday_sede_pt', 'Local holiday - Sede', '3' , 'ALL', 2, 'DEV-8232', '2021-03-22 12:00:00' );

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
('pt', 'ttwa-base', 'public_holiday_national_holiday_pt', 'National Holiday', '1'),
('pt', 'ttwa-base', 'public_holiday_national_local_holiday_lisboa_pt', 'Local holiday - Lisboa', '1'),
('pt', 'ttwa-base', 'public_holiday_national_local_holiday_sede_pt', 'Local holiday - Sede', '1');

UPDATE company SET country='ptlisboa' WHERE country = "pt" AND status = 2 AND company_name = "Lisboa";
UPDATE company SET country='ptsede' WHERE country = "pt" AND status = 2 AND company_name = "Sede";

UPDATE `_sql_version` SET `revision`=43, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -43--2021-03-22-12:00-----------------------------------------------------------------
UPDATE `app_settings` SET `setting_value` = '0', `note`='prev val: 1, DEV-7935' WHERE `setting_id` = 'showRandomSelectedOptionsForSummarySheet';

UPDATE `app_settings` SET `setting_value` = '0', `note`='prev val: COST, DEV-7935' WHERE `setting_id` = 'summarySheet_costMode';

UPDATE `app_settings` SET `setting_value` = '0', `note`='prev val: cost_center, DEV-7935' WHERE `setting_id` = 'costMode';

UPDATE `_sql_version` SET `revision`=44, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -44--2021-02-24-12:39-----------------------------------------------------------------

INSERT IGNORE INTO dictionary (lang,module,dict_id,dict_value,valid) VALUES
	 ('hu','ttwa-base','lang_ptlisboa','Lisboa',1),
	 ('en','ttwa-base','lang_ptlisboa','Lisboa',1),
	 ('pt','ttwa-base','lang_ptlisboa','Lisboa',1),
	 ('hu','ttwa-base','lang_ptsede','Sede',1),
	 ('en','ttwa-base','lang_ptsede','Sede',1),
	 ('pt','ttwa-base','lang_ptsede','Sede',1);

UPDATE `_sql_version` SET `revision`=45, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -45--2021-02-24-12:39-----------------------------------------------------------------

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('pt', 'ttwa-base', 'ptrFiles', 'Arquivos', '1'),
	('pt', 'ttwa-base', 'ptrLocked', 'Trancado', '1'),
	('pt', 'ttwa-base', 'payroll_transfer_type_sage', 'Sage', '1');

UPDATE `_sql_version` SET `revision`=46, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION -46--2021-04-16-15:00-----------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '0',`note`='DEV-7935, prev val:1' WHERE `setting_id` = 'showCostInfoInSummarySheet';

UPDATE `_sql_version` SET `revision`=47, `updated_on`=NOW() WHERE `module`='c_stricker';

-- VERSION 47-2021-04-26-12:00-------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`) VALUES
	('hu', 'ttwa-ahp', 'absence_type_illness_hours', '_Betegség (óra)'),
	('pt', 'ttwa-ahp', 'absence_type_illness_hours', '_Doença (horas)'),
	('en', 'ttwa-ahp', 'absence_type_illness_hours', '_Illness (hours)'),
	('hu', 'ttwa-ahp-core', 'absence_type_illness_hours', '_Betegség (óra)'),
	('pt', 'ttwa-ahp-core', 'absence_type_illness_hours', '_Doença (horas)'),
	('en', 'ttwa-ahp-core', 'absence_type_illness_hours', '_Illness (hours)'),
	('hu', 'ttwa-ahp', 'absence_type_short_illness_hours', 'DOEH'),
	('pt', 'ttwa-ahp', 'absence_type_short_illness_hours', 'DOEH'),
	('en', 'ttwa-ahp', 'absence_type_short_illness_hours', 'DOEH'),
	('hu', 'ttwa-ahp-core', 'absence_short_type_illness_hours', 'DOEH'),
	('pt', 'ttwa-ahp-core', 'absence_short_type_illness_hours', 'DOEH'),
	('en', 'ttwa-ahp-core', 'absence_short_type_illness_hours', 'DOEH');

DELETE FROM `state_type` WHERE `state_type_id` = 'absence_type_illness_h';
INSERT INTO `state_type` (`state_type_id`, `name_dict_id`, `short_name_dict_id`, `bgcolor`, `status`, `absence_hour`, `worktime`) VALUES
	('absence_type_illness_h', 'absence_type_illness_hours', 'absence_type_short_illness_hours', '#ff7043', 2, 1, 0);

DELETE FROM `payroll_transfer_config` WHERE `transfer_id` = 'DOEH' AND `note` = 'Illness (Hours)';
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_value`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
	('SAGE', 'DOEH', 'state_type_id', 'absence_type_illness_h', NULL, 0, 0, NULL, NULL, 'Illness (Hours)', 2);

DELETE FROM `payroll_transfer_config` WHERE `transfer_id` = 'DOEH' AND `note` = 'Illness (Hours) - jogcím';
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'DOEH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_illness_h\"', 'Illness (Hours) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=48, `updated_on`=NOW() WHERE `module`='c_stricker';

-- VERSION 48-2021-05-03-09:45-------------------------------------------------

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('pt', 'ttwa-wfm', 'software_detected_incorrect_data_during_transfer', 'O software detetou dados incorretos durante a transferência.', '1'),
	('pt', 'ttwa-wfm', 'check_errors_by_selecting_viewfinder', 'Por favor selecione - Error list mode - no visor para verficar os erros.', '1'),
	('pt', 'ttwa-wfm', 'transfer_completed_without_saving', 'A transferência foi concluída sem guardar. A Lista de Erros não poderá ser verificada posteriormente.', '1'),
	('pt', 'ttwa-wfm', 'error_list_cannot_be_verified_afterwards', 'A Lista de Erros não poderá ser verificada posteriormente.', '1'),
	('pt', 'ttwa-wfm', 'export_error_list', 'Exportação de lista dos erros', '1');

UPDATE `_sql_version` SET `revision`=49, `updated_on`=NOW() WHERE `module`='c_stricker';

-- VERSION 49-2021-05-03-15:45-------------------------------------------------

INSERT INTO `dictionary` (`module`, `valid`, `lang`, `dict_id`, `dict_value`) VALUES
    ('ttwa-ahp', '1', 'hu', 'absence_type_short_exceptional_support', 'ESA23'),
	('ttwa-ahp', '1', 'en', 'absence_type_short_exceptional_support', 'ESA23'),
    ('ttwa-ahp', '1', 'hu', 'absence_type_short_food_allowance', 'FA'),
	('ttwa-ahp', '1', 'en', 'absence_type_short_food_allowance', 'FA'),
    ('ttwa-ahp', '1', 'hu', 'absence_type_short_disc_suspension', 'DS'),
	('ttwa-ahp', '1', 'en', 'absence_type_short_disc_suspension', 'DS'),
    ('ttwa-ahp', '1', 'hu', 'absence_type_short_illness_prev_month', 'IPM'),
	('ttwa-ahp', '1', 'en', 'absence_type_short_illness_prev_month', 'IPM'),
    ('ttwa-ahp', '1', 'hu', 'absence_type_short_legal_reduction', 'LR'),
	('ttwa-ahp', '1', 'en', 'absence_type_short_legal_reduction', 'LR'),
    ('ttwa-ahp', '1', 'hu', 'absence_type_short_low_risk_pregnancy', 'LSRP'),
	('ttwa-ahp', '1', 'en', 'absence_type_short_low_risk_pregnancy', 'LSRP'),
    ('ttwa-ahp', '1', 'hu', 'absence_type_short_non_family_assist', 'NFA'),
	('ttwa-ahp', '1', 'en', 'absence_type_short_non_family_assist', 'NFA'),
    ('ttwa-ahp', '1', 'hu', 'absence_type_short_non_prof_disease', 'NPD'),
	('ttwa-ahp', '1', 'en', 'absence_type_short_non_prof_disease', 'NPD'),
    ('ttwa-ahp', '1', 'hu', 'absence_type_short_notcert_prof_dise', 'NOTPD'),
	('ttwa-ahp', '1', 'en', 'absence_type_short_notcert_prof_dise', 'NOTPD'),
    ('ttwa-ahp', '1', 'hu', 'absence_type_short_paid_family_assis', 'PFA'),
	('ttwa-ahp', '1', 'en', 'absence_type_short_paid_family_assis', 'PFA'),
    ('ttwa-ahp', '1', 'hu', 'absence_type_short_strike', 'S'),
	('ttwa-ahp', '1', 'en', 'absence_type_short_strike', 'S');

INSERT INTO `dictionary` (`module`, `valid`, `lang`, `dict_id`, `dict_value`) VALUES
    ('ttwa-ahp-core', '1', 'hu', 'absence_type_short_exceptional_support', 'ESA23'),
	('ttwa-ahp-core', '1', 'en', 'absence_type_short_exceptional_support', 'ESA23'),
    ('ttwa-ahp-core', '1', 'hu', 'absence_type_short_food_allowance', 'FA'),
	('ttwa-ahp-core', '1', 'en', 'absence_type_short_food_allowance', 'FA'),
    ('ttwa-ahp-core', '1', 'hu', 'absence_type_short_disc_suspension', 'DS'),
	('ttwa-ahp-core', '1', 'en', 'absence_type_short_disc_suspension', 'DS'),
    ('ttwa-ahp-core', '1', 'hu', 'absence_type_short_illness_prev_month', 'IPM'),
	('ttwa-ahp-core', '1', 'en', 'absence_type_short_illness_prev_month', 'IPM'),
    ('ttwa-ahp-core', '1', 'hu', 'absence_type_short_legal_reduction', 'LR'),
	('ttwa-ahp-core', '1', 'en', 'absence_type_short_legal_reduction', 'LR'),
    ('ttwa-ahp-core', '1', 'hu', 'absence_type_short_low_risk_pregnancy', 'LSRP'),
	('ttwa-ahp-core', '1', 'en', 'absence_type_short_low_risk_pregnancy', 'LSRP'),
    ('ttwa-ahp-core', '1', 'hu', 'absence_type_short_non_family_assist', 'NFA'),
	('ttwa-ahp-core', '1', 'en', 'absence_type_short_non_family_assist', 'NFA'),
    ('ttwa-ahp-core', '1', 'hu', 'absence_type_short_non_prof_disease', 'NPD'),
	('ttwa-ahp-core', '1', 'en', 'absence_type_short_non_prof_disease', 'NPD'),
    ('ttwa-ahp-core', '1', 'hu', 'absence_type_short_notcert_prof_dise', 'NOTPD'),
	('ttwa-ahp-core', '1', 'en', 'absence_type_short_notcert_prof_dise', 'NOTPD'),
    ('ttwa-ahp-core', '1', 'hu', 'absence_type_short_paid_family_assis', 'PFA'),
	('ttwa-ahp-core', '1', 'en', 'absence_type_short_paid_family_assis', 'PFA'),
    ('ttwa-ahp-core', '1', 'hu', 'absence_type_short_strike', 'S'),
	('ttwa-ahp-core', '1', 'en', 'absence_type_short_strike', 'S');


INSERT INTO `dictionary` (`module`, `valid`, `lang`, `dict_id`, `dict_value`) VALUES
	('ttwa-ahp', '1', 'pt', 'absence_type_short_exceptional_support', 'ESA23'),
	('ttwa-ahp', '1', 'pt', 'absence_type_short_food_allowance', 'FA'),
	('ttwa-ahp', '1', 'pt', 'absence_type_short_disc_suspension', 'DS'),
	('ttwa-ahp', '1', 'pt', 'absence_type_short_illness_prev_month', 'IPM'),
	('ttwa-ahp', '1', 'pt', 'absence_type_short_legal_reduction', 'LR'),
	('ttwa-ahp', '1', 'pt', 'absence_type_short_low_risk_pregnancy', 'LSRP'),
	('ttwa-ahp', '1', 'pt', 'absence_type_short_non_family_assist', 'NFA'),
	('ttwa-ahp', '1', 'pt', 'absence_type_short_non_prof_disease', 'NPD'),
	('ttwa-ahp', '1', 'pt', 'absence_type_short_notcert_prof_dise', 'NOTPD'),
	('ttwa-ahp', '1', 'pt', 'absence_type_short_paid_family_assis', 'PFA'),
	('ttwa-ahp', '1', 'pt', 'absence_type_short_strike', 'S');

INSERT INTO `dictionary` (`module`, `valid`, `lang`, `dict_id`, `dict_value`) VALUES
	('ttwa-ahp-core', '1', 'pt', 'absence_type_short_exceptional_support', 'ESA23'),
	('ttwa-ahp-core', '1', 'pt', 'absence_type_short_food_allowance', 'FA'),
	('ttwa-ahp-core', '1', 'pt', 'absence_type_short_disc_suspension', 'DS'),
	('ttwa-ahp-core', '1', 'pt', 'absence_type_short_illness_prev_month', 'IPM'),
	('ttwa-ahp-core', '1', 'pt', 'absence_type_short_legal_reduction', 'LR'),
	('ttwa-ahp-core', '1', 'pt', 'absence_type_short_low_risk_pregnancy', 'LSRP'),
	('ttwa-ahp-core', '1', 'pt', 'absence_type_short_non_family_assist', 'NFA'),
	('ttwa-ahp-core', '1', 'pt', 'absence_type_short_non_prof_disease', 'NPD'),
	('ttwa-ahp-core', '1', 'pt', 'absence_type_short_notcert_prof_dise', 'NOTPD'),
	('ttwa-ahp-core', '1', 'pt', 'absence_type_short_paid_family_assis', 'PFA'),
	('ttwa-ahp-core', '1', 'pt', 'absence_type_short_strike', 'S');

UPDATE state_type SET short_name_dict_id = 'absence_type_short_exceptional_support' WHERE state_type_id = 'absence_type_exceptional_support' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_medical_leave' WHERE state_type_id = 'absence_type_medical_leave' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_disc_suspension' WHERE state_type_id = 'absence_type_disc_suspension' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_food_allowance' WHERE state_type_id = 'absence_type_food_allowance' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_illness_prev_month' WHERE state_type_id = 'absence_type_illness_prev_month' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_illness_prev_month' WHERE state_type_id = 'absence_type_illness_prev_month' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_legal_reduction' WHERE state_type_id = 'absence_type_legal_reduction' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_low_risk_pregnancy' WHERE state_type_id = 'absence_type_low_risk_pregnancy' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_marriage' WHERE state_type_id = 'absence_type_marriage' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_non_family_assist' WHERE state_type_id = 'absence_type_non_family_assist' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_non_prof_disease' WHERE state_type_id = 'absence_type_non_prof_disease' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_notcert_prof_dise' WHERE state_type_id = 'absence_type_notcert_prof_dise' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_paid_family_assis' WHERE state_type_id = 'absence_type_paid_family_assis' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_strike' WHERE state_type_id = 'absence_type_strike' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_uncert_occ_ill' WHERE state_type_id = 'absence_type_uncert_occ_ill' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_proplact_iso' WHERE state_type_id = 'absence_type_proplact_iso' AND status = 2;

UPDATE `_sql_version` SET `revision`=50, `updated_on`=NOW() WHERE `module`='c_stricker';

-- VERSION 50-2021-05-07-14:00-------------------------------------------------

UPDATE state_type SET short_name_dict_id = 'absence_type_short_prenatal_consultation' WHERE state_type_id = 'absence_type_prenatal_consultation' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_formation' WHERE state_type_id = 'absence_type_formation' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_jubilee' WHERE state_type_id = 'absence_type_jubilee' AND status = 2;
UPDATE state_type SET short_name_dict_id = 'absence_type_short_health_harmful' WHERE state_type_id = 'absence_type_health_harmful' AND status = 2;

UPDATE state_type SET short_name_dict_id= 'absence_type_short_birth' WHERE state_type_id='d519994ed69869e8928bd98c118db46d' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_contract_suspension' WHERE state_type_id='057385583107d23d85f397ad847fff42' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_electoral_law' WHERE state_type_id='0c48b766ff7d9b6b28a89b93847ea2fe' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_exceptional_sup_h' WHERE state_type_id='absence_type_exceptional_sup_h' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_exceptional_support' WHERE state_type_id='5ac088f356af3650149df3038ca8eba1' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_illness_day' WHERE state_type_id='83fd36d8ca2599a09b7224a8c7b1e1e1' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_insurance_leave' WHERE state_type_id='84dcf298fca29a1dd246502724d826eb' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_leaving_in_service' WHERE state_type_id='35b17c53f4bbacd97e741e90c811ad36' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_medical_leave' WHERE state_type_id='c4a23d94c93c7500e3bdf97cc8cbf8b7' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_missing_strike' WHERE state_type_id='b4c0651d31c63c96df4f35a41ea33902' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_mourning' WHERE state_type_id='b5152bd8472e81a666381bf99d546c87' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_pregnancy' WHERE state_type_id='4237ba7960c41f48793e7ec644a1fe35' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_prenatal_consultation' WHERE state_type_id='absence_type_prenatal_consul' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_previous_year_holidays' WHERE state_type_id='f54d066221bb49cc5528d59c9c20e24c' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_prolonged_absence' WHERE state_type_id='ec56c2ce6cf4c25d63cf147365afdd58' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_retirement_for_childcare' WHERE state_type_id='089abfabd43313d043ac807e775128f2' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_suspension_for_punishment' WHERE state_type_id='3948f477432aebf15a90a051c4b07840' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_union' WHERE state_type_id='21abd6dbf2ed7ad73e4abc2adb4e12c3' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_unpaid_leave' WHERE state_type_id='1eb1bbd0e8772267d22ae86f7ab63bce' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_urgent_childcare' WHERE state_type_id='1e4240db0d7790479ea799944d135fec' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_vacation' WHERE state_type_id='a779664df6da7e203a07e21a0762c6cc' AND status = 2;
UPDATE state_type SET short_name_dict_id= 'absence_type_short_wedding' WHERE state_type_id='236ab9d5a79b89dc1b6cf832e5f3150e' AND status = 2;

UPDATE state_type SET status = 7 WHERE status = 2 AND short_name_dict_id NOT LIKE "%short%" AND bgcolor = "FFFFFF";

INSERT INTO `dictionary` (`module`, `valid`, `lang`, `dict_id`, `dict_value`) VALUES
    ('ttwa-ahp', '1', 'hu', 'absence_type_short_prenatal_consultation', 'PC'),
	('ttwa-ahp', '1', 'en', 'absence_type_short_prenatal_consultation', 'PC'),
	('ttwa-ahp', '1', 'pt', 'absence_type_short_prenatal_consultation', 'PC'),
	('ttwa-ahp', '1', 'hu', 'absence_type_short_jubilee', 'J'),
	('ttwa-ahp', '1', 'en', 'absence_type_short_jubilee', 'J'),
	('ttwa-ahp', '1', 'pt', 'absence_type_short_jubilee', 'J'),
	('ttwa-ahp', '1', 'hu', 'absence_type_short_health_harmful', 'HH'),
	('ttwa-ahp', '1', 'en', 'absence_type_short_health_harmful', 'HH'),
	('ttwa-ahp', '1', 'pt', 'absence_type_short_health_harmful', 'HH');

INSERT INTO `dictionary` (`module`, `valid`, `lang`, `dict_id`, `dict_value`) VALUES
    ('ttwa-ahp-core', '1', 'hu', 'absence_type_short_prenatal_consultation', 'PC'),
	('ttwa-ahp-core', '1', 'en', 'absence_type_short_prenatal_consultation', 'PC'),
	('ttwa-ahp-core', '1', 'pt', 'absence_type_short_prenatal_consultation', 'PC'),
	('ttwa-ahp-core', '1', 'hu', 'absence_type_short_jubilee', 'J'),
	('ttwa-ahp-core', '1', 'en', 'absence_type_short_jubilee', 'J'),
	('ttwa-ahp-core', '1', 'pt', 'absence_type_short_jubilee', 'J'),
	('ttwa-ahp-core', '1', 'hu', 'absence_type_short_health_harmful', 'HH'),
	('ttwa-ahp-core', '1', 'en', 'absence_type_short_health_harmful', 'HH'),
	('ttwa-ahp-core', '1', 'pt', 'absence_type_short_health_harmful', 'HH');


INSERT INTO `dictionary` (`module`, `valid`, `lang`, `dict_id`, `dict_value`) VALUES
	('ttwa-ahp', '1', 'hu','absence_type_short_birth','B'),
	('ttwa-ahp', '1', 'hu','absence_type_short_contract_suspension','CS'),
	('ttwa-ahp', '1', 'hu','absence_type_short_electoral_law','EL'),
	('ttwa-ahp', '1', 'hu','absence_type_short_exceptional_sup_h','ES'),
	('ttwa-ahp', '1', 'hu','absence_type_short_exceptional_support','ES'),
	('ttwa-ahp', '1', 'hu','absence_type_short_illness_day','ID'),
	('ttwa-ahp', '1', 'hu','absence_type_short_insurance_leave','IL'),
	('ttwa-ahp', '1', 'hu','absence_type_short_leaving_in_service','LI'),
	('ttwa-ahp', '1', 'hu','absence_type_short_medical_leave','ML'),
	('ttwa-ahp', '1', 'hu','absence_type_short_missing_strike','MS'),
	('ttwa-ahp', '1', 'hu','absence_type_short_mourning','M'),
	('ttwa-ahp', '1', 'hu','absence_type_short_pregnancy','P'),
	('ttwa-ahp', '1', 'hu','absence_type_short_previous_year_holidays','PY'),
	('ttwa-ahp', '1', 'hu','absence_type_short_prolonged_absence','PA'),
	('ttwa-ahp', '1', 'hu','absence_type_short_retirement_for_childcare','RF'),
	('ttwa-ahp', '1', 'hu','absence_type_short_suspension_for_punishment','SF'),
	('ttwa-ahp', '1', 'hu','absence_type_short_union','U'),
	('ttwa-ahp', '1', 'hu','absence_type_short_unpaid_leave','UL'),
	('ttwa-ahp', '1', 'hu','absence_type_short_urgent_childcare','UC'),
	('ttwa-ahp', '1', 'hu','absence_type_short_vacation','V'),
	('ttwa-ahp', '1', 'hu','absence_type_short_wedding','W'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_birth','B'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_contract_suspension','CS'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_electoral_law','EL'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_exceptional_sup_h','ES'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_exceptional_support','ES'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_illness_day','ID'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_insurance_leave','IL'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_leaving_in_service','LI'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_medical_leave','ML'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_missing_strike','MS'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_mourning','M'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_pregnancy','P'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_prenatal_consultation','PC'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_previous_year_holidays','PY'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_prolonged_absence','PA'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_retirement_for_childcare','RF'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_suspension_for_punishment','SF'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_union','U'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_unpaid_leave','UL'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_urgent_childcare','UC'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_vacation','V'),
	('ttwa-ahp-core', '1', 'hu','absence_type_short_wedding','W'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_birth','B'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_contract_suspension','CS'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_electoral_law','EL'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_exceptional_sup_h','ES'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_exceptional_support','ES'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_illness_day','ID'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_insurance_leave','IL'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_leaving_in_service','LI'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_medical_leave','ML'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_missing_strike','MS'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_mourning','M'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_pregnancy','P'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_prenatal_consultation','PC'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_previous_year_holidays','PY'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_prolonged_absence','PA'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_retirement_for_childcare','RF'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_suspension_for_punishment','SF'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_union','U'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_unpaid_leave','UL'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_urgent_childcare','UC'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_vacation','V'),
	('ttwa-ahp-core', '1', 'en','absence_type_short_wedding','W'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_birth','B'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_contract_suspension','CS'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_electoral_law','EL'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_exceptional_sup_h','ES'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_exceptional_support','ES'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_illness_day','ID'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_insurance_leave','IL'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_leaving_in_service','LI'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_medical_leave','ML'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_missing_strike','MS'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_mourning','M'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_pregnancy','P'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_prenatal_consultation','PC'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_previous_year_holidays','PY'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_prolonged_absence','PA'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_retirement_for_childcare','RF'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_suspension_for_punishment','SF'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_union','U'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_unpaid_leave','UL'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_urgent_childcare','UC'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_vacation','V'),
	('ttwa-ahp-core', '1', 'pt','absence_type_short_wedding','W'),
	('ttwa-ahp', '1', 'pt','absence_type_short_birth','B'),
	('ttwa-ahp', '1', 'pt','absence_type_short_contract_suspension','CS'),
	('ttwa-ahp', '1', 'pt','absence_type_short_electoral_law','EL'),
	('ttwa-ahp', '1', 'pt','absence_type_short_exceptional_sup_h','ES'),
	('ttwa-ahp', '1', 'pt','absence_type_short_exceptional_support','ES'),
	('ttwa-ahp', '1', 'pt','absence_type_short_illness_day','ID'),
	('ttwa-ahp', '1', 'pt','absence_type_short_insurance_leave','IL'),
	('ttwa-ahp', '1', 'pt','absence_type_short_leaving_in_service','LI'),
	('ttwa-ahp', '1', 'pt','absence_type_short_medical_leave','ML'),
	('ttwa-ahp', '1', 'pt','absence_type_short_missing_strike','MS'),
	('ttwa-ahp', '1', 'pt','absence_type_short_mourning','M'),
	('ttwa-ahp', '1', 'pt','absence_type_short_pregnancy','P'),
	('ttwa-ahp', '1', 'pt','absence_type_short_prenatal_consultation','PC'),
	('ttwa-ahp', '1', 'pt','absence_type_short_previous_year_holidays','PY'),
	('ttwa-ahp', '1', 'pt','absence_type_short_prolonged_absence','PA'),
	('ttwa-ahp', '1', 'pt','absence_type_short_retirement_for_childcare','RF'),
	('ttwa-ahp', '1', 'pt','absence_type_short_suspension_for_punishment','SF'),
	('ttwa-ahp', '1', 'pt','absence_type_short_union','U'),
	('ttwa-ahp', '1', 'pt','absence_type_short_unpaid_leave','UL'),
	('ttwa-ahp', '1', 'pt','absence_type_short_urgent_childcare','UC'),
	('ttwa-ahp', '1', 'pt','absence_type_short_vacation','V'),
	('ttwa-ahp', '1', 'pt','absence_type_short_wedding','W'),
	('ttwa-ahp', '1', 'en','absence_type_short_birth','B'),
	('ttwa-ahp', '1', 'en','absence_type_short_contract_suspension','CS'),
	('ttwa-ahp', '1', 'en','absence_type_short_electoral_law','EL'),
	('ttwa-ahp', '1', 'en','absence_type_short_exceptional_sup_h','ES'),
	('ttwa-ahp', '1', 'en','absence_type_short_exceptional_support','ES'),
	('ttwa-ahp', '1', 'en','absence_type_short_illness_day','ID'),
	('ttwa-ahp', '1', 'en','absence_type_short_insurance_leave','IL'),
	('ttwa-ahp', '1', 'en','absence_type_short_leaving_in_service','LI'),
	('ttwa-ahp', '1', 'en','absence_type_short_medical_leave','ML'),
	('ttwa-ahp', '1', 'en','absence_type_short_missing_strike','MS'),
	('ttwa-ahp', '1', 'en','absence_type_short_mourning','M'),
	('ttwa-ahp', '1', 'en','absence_type_short_pregnancy','P'),
	('ttwa-ahp', '1', 'en','absence_type_short_prenatal_consultation','PC'),
	('ttwa-ahp', '1', 'en','absence_type_short_previous_year_holidays','PY'),
	('ttwa-ahp', '1', 'en','absence_type_short_prolonged_absence','PA'),
	('ttwa-ahp', '1', 'en','absence_type_short_retirement_for_childcare','RF'),
	('ttwa-ahp', '1', 'en','absence_type_short_suspension_for_punishment','SF'),
	('ttwa-ahp', '1', 'en','absence_type_short_union','U'),
	('ttwa-ahp', '1', 'en','absence_type_short_unpaid_leave','UL'),
	('ttwa-ahp', '1', 'en','absence_type_short_urgent_childcare','UC'),
	('ttwa-ahp', '1', 'en','absence_type_short_vacation','V'),
	('ttwa-ahp', '1', 'en','absence_type_short_wedding','W')
	 ON DUPLICATE KEY UPDATE dict_id = VALUES(dict_id);

UPDATE `_sql_version` SET `revision`=51, `updated_on`=NOW() WHERE `module`='c_stricker';

-- VERSION 51-2021-05-12-14:00-------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Éves szinten az időarányosan kivehető szabadságnapok száma' WHERE `dict_id` = 'difference_from_pro-rata' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Annual number of days off' WHERE `dict_id` = 'difference_from_pro-rata' AND `lang` = 'en';
UPDATE `app_settings` SET `setting_value` = '0', `note` = 'DEV-6471, Prev val: 1' WHERE `setting_id` = 'absenceplanneryearview_show_extra_words';

UPDATE `_sql_version` SET `revision`=52, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 52-2021-05-12-14:00-------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'FNRH', 'inside_type_id', NULL, NULL, 0, 'ABS(data.`value`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`company_org_group1_id` IN (\"22\", \"27\", \"20e011fbdba9bc7a1f350e3c051cea8f\", \"29\", \"18\", \"e5dcf9b2525f438ec1d2b36936605a08\", \"40\") AND data.`inside_type_id` = \"paidot\" AND data.`value` < 0', 'Unjustified absence', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=53, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 53-2021-05-21-09:30-------------------------------------------------

UPDATE `app_lookup` SET `valid` = 1 WHERE `lookup_value` IN ('absenceApproverOneDay', 'absenceApproverMoreDays');
UPDATE `app_settings` SET `setting_value` = '1', `note` = 'SN-JIRA-DEV-9035, Prev val: 0' WHERE `setting_id` = 'limitAbsencesByAbsenceDays';

UPDATE `_sql_version` SET `revision`=54, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 54-2021-05-21-11:30-------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'DEV-9298, Prev val: 0' WHERE `setting_id` = 'summarySheet_deduction_of_breaks_outside_working_hours';

UPDATE `_sql_version` SET `revision`=55, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 55-2021-06-22-14:00-------------------------------------------------

UPDATE dictionary SET dict_value = '_Bank of Hours'        WHERE dict_value='_Individual Adaptability (hours)'    AND valid=1 AND lang='en';
UPDATE dictionary SET dict_value = 'Bank of Hours'         WHERE dict_value='Individual Adaptability hours'       AND valid=1 AND lang='en';
UPDATE dictionary SET dict_value = 'Bank of hours'         WHERE dict_value='Individual adaptability management'  AND valid=1 AND lang='en';
UPDATE dictionary SET dict_value = 'Daily bank of hours'   WHERE dict_value='Daily individual adaptability'       AND valid=1 AND lang='en';
UPDATE dictionary SET dict_value = 'Frame bank of hours'   WHERE dict_value='Frame individual adaptability'       AND valid=1 AND lang='en';
UPDATE dictionary SET dict_value = 'Holiday bank of hours' WHERE dict_value='Holiday individual adaptability'     AND valid=1 AND lang='en';
UPDATE dictionary SET dict_value = 'Taken Bank of Hours'   WHERE dict_value='Taken Individual Adaptability hours' AND valid=1 AND lang='en';
UPDATE dictionary SET dict_value = 'Wrapped bank of hours' WHERE dict_value='Wrapped individual adaptability'     AND valid=1 AND lang='en';

UPDATE `_sql_version` SET `revision`=56, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 56-2021-08-06-17:00-------------------------------------------------

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`) VALUES
	('pt', 'ttwa-wfm', 'summarySheet_showLockedDays', 'Funcionários com dias fechados podem ser vistos');

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'DEV-10240, Prev val: 0' WHERE `setting_id` = 'summarySheet_lockedNotLockedSearch';

UPDATE `_sql_version` SET `revision`=57, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 57-2021-08-11-11:30-------------------------------------------------

UPDATE app_settings SET setting_value=1 where setting_id='doNotLockDaysMarkedOrange';

UPDATE `_sql_version` SET `revision`=58, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 58-2021-08-18-12:00-------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` = 'NOT2';
UPDATE `payroll_transfer_config` SET `transfer_id` = 'NH_SUP501' WHERE `transfer_id` = 'HS501';
UPDATE `payroll_transfer_config` SET `transfer_id` = 'NH_SUP101' WHERE `transfer_id` = 'HS101';
UPDATE `payroll_transfer_config` SET `transfer_id` = 'NH_SUP751' WHERE `transfer_id` = 'HS751';

UPDATE `_sql_version` SET `revision`=59, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 59-2021-08-26-16:30-------------------------------------------------

UPDATE dictionary SET dict_value='_Paga. Banco de Horas'       WHERE valid=1 AND lang='pt' AND module='ttwa-ahp'       AND dict_id='absence_type_indiv_adapt_h' ;
UPDATE dictionary SET dict_value='_Paga. Banco de Horas'       WHERE valid=1 AND lang='pt' AND module='ttwa-ahp-core'  AND dict_id='absence_type_indiv_adapt_h' ;
UPDATE dictionary SET dict_value='Banco de Horas management'   WHERE valid=1 AND lang='pt' AND module='ttwa-wfm'       AND dict_id='page_title_frame_balance_management_daily' ;
UPDATE dictionary SET dict_value='Banco de Horas management'   WHERE valid=1 AND lang='pt' AND module='ttwa-wfm'       AND dict_id='menu_item_frame_balance_management_daily' ;
UPDATE dictionary SET dict_value='Daily Banco de Horas'        WHERE valid=1 AND lang='pt' AND module='ttwa-wfm'       AND dict_id='daily_individual_adaptability' ;
UPDATE dictionary SET dict_value='Frame Banco de Horas'        WHERE valid=1 AND lang='pt' AND module='ttwa-wfm'       AND dict_id='frame_individual_adaptability' ;
UPDATE dictionary SET dict_value='Holiday Banco de Horas'      WHERE valid=1 AND lang='pt' AND module='ttwa-wfm'       AND dict_id='holiday_individual_adaptability' ;
UPDATE dictionary SET dict_value='Wrapped Banco de Horas'      WHERE valid=1 AND lang='pt' AND module='ttwa-wfm'       AND dict_id='wrapped_individual_adaptability' ;
UPDATE dictionary SET dict_value='Gozo de Banco de Horas'      WHERE valid=1 AND lang='pt' AND module='ttwa-ahp'       AND dict_id='absence_type_taken_individual_adaptability_hours' ;
UPDATE dictionary SET dict_value='Gozo de Banco de Horas'      WHERE valid=1 AND lang='pt' AND module='ttwa-ahp-core'  AND dict_id='absence_type_taken_individual_adaptability_hours' ;
UPDATE dictionary SET dict_value='Banco de Horas'              WHERE valid=1 AND lang='pt' AND module='ttwa-ahp'       AND dict_id='absence_type_individual_adaptability_hours' ;
UPDATE dictionary SET dict_value='Banco de Horas'              WHERE valid=1 AND lang='pt' AND module='ttwa-ahp-core'  AND dict_id='absence_type_individual_adaptability_hours' ;
UPDATE dictionary SET dict_value='Gozo de Banco de Horas'      WHERE valid=1 AND lang='pt' AND module='ttwa-ahp'       AND dict_id='absence_type_short_taken_individual_adaptability_hours' ;
UPDATE dictionary SET dict_value='Gozo de Banco de Horas'      WHERE valid=1 AND lang='pt' AND module='ttwa-ahp-core'  AND dict_id='absence_type_short_taken_individual_adaptability_hours' ;

UPDATE `_sql_version` SET `revision`=60, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 60-2021-09-02-13:52-------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 2, `sql_conditions_2` = 'data.`value` > 0 AND data.`inside_type_id` IN (\"botej\", \"botwej\") AND (data.`workgroup_name` LIKE \"Operations%\" OR data.`workgroup_name` LIKE \"WH%\") AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`date_day_of_week` NOT IN (7, 1) AND data.`is_public_holiday` <> 1' WHERE `transfer_id` = 'NOT2' AND `status` = 7;
UPDATE `payroll_transfer_config` SET `transfer_id` = 'HS501' WHERE `transfer_id` = 'NH_SUP501';
UPDATE `payroll_transfer_config` SET `transfer_id` = 'HS101' WHERE `transfer_id` = 'NH_SUP101';
UPDATE `payroll_transfer_config` SET `transfer_id` = 'HS751' WHERE `transfer_id` = 'NH_SUP751';

UPDATE `_sql_version` SET `revision`=61, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 61-2021-09-02-15:30-------------------------------------------------

UPDATE `payroll_transfer_config` SET `transfer_id` = 'NH_SUP501' WHERE `transfer_id` = 'HS501';
UPDATE `payroll_transfer_config` SET `transfer_id` = 'NH_SUP101' WHERE `transfer_id` = 'HS101';
UPDATE `payroll_transfer_config` SET `transfer_id` = 'NH_SUP751' WHERE `transfer_id` = 'HS751';

UPDATE `_sql_version` SET `revision`=62, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 62-2021-09-06-13:00-------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'NH_BHP', 'inside_type_id', NULL, NULL, 0, 'data.`frame_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`frame_ia` > 0', 'Minden munkacsoport összegyűlt egyenleg', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'NH_BHP', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_indiv_adapt_h\"', 'Individual Adaptability (hours) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'NH_BHP', 'state_type_id', NULL, 'absence_type_indiv_adapt_h', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Individual Adaptability (hours)', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'NH_BHP', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"next_balancetr\" AND data.`value` > 0', 'Egyenleg', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'GBH', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`daily_ia` < 0', 'OPERATION-s munkacsoport napi egyenleg negatív', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'GBH', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`daily_ia` < 0', 'WH-s munkacsoport napi egyenleg negatív', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'BHT', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`daily_ia` > 0', 'OPERATION-s munkacsoport napi egyenleg pozitív', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'BHT', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`daily_ia` > 0', 'WH-s munkacsoport napi egyenleg pozitív', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

-- Megint variáltak
TRUNCATE `payroll_transfer_config`;
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'PAINH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"next_balancetr\" AND data.`value` > 0 AND data.`date` <= \"2021-08-31\"', 'Egyenleg', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAFNH', 'state_type_id', NULL, 'absence_type_non_rem_fam_ass_h', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Non remunerated family assistance hours', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAFRH', 'state_type_id', NULL, 'absence_type_rem_fam_ass_h', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Remunerated family assistance', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'AT', 'state_type_id', NULL, '945e3618f487efc4325204521e49c5f3', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Work Accident', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'CASAM', 'state_type_id', NULL, 'absence_type_marriage', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Marriage', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'DOC', 'state_type_id', NULL, 'absence_type_cert_prof_disease', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Certified professional disease', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'DOE', 'state_type_id', NULL, 'absence_type_non_prof_disease', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Non-professional disease', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'DOP', 'state_type_id', NULL, 'absence_type_notcert_prof_dise', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Professional disease not certified', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAF', 'state_type_id', NULL, 'absence_type_paid_family_assis', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Paid family assistance', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAFNR', 'state_type_id', NULL, 'absence_type_non_family_assist', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Non-remunerated family assistance', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAL', 'state_type_id', NULL, 'absence_type_family_death', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Family Death', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FNRD', 'state_type_id', NULL, 'def32968390fb987c823da0cbf7d3bd8', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Unpaid absences', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FNRH', 'state_type_id', NULL, 'absence_type_unpaid_h', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Unpaid absences hours', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FOR', 'state_type_id', NULL, 'absence_type_formation', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Formation', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FRD', 'state_type_id', NULL, 'f4f63ae4dd65cd97ee1f409d8b620806', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Paid absences', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FSD', 'state_type_id', NULL, 'absence_type_disc_suspension', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Absence for disciplinary suspension', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'GRE', 'state_type_id', NULL, 'absence_type_strike', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Strike', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'HC', 'state_type_id', NULL, '29337d9204baca9588942e162d229087', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Concentrated schedule enjoyment', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'MAT', 'state_type_id', NULL, 'absence_type_maternity', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Maternity', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'PACN', 'state_type_id', NULL, 'absence_type_prenatal_consul', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Prenatal consultation', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'PAMA', 'state_type_id', NULL, 'ebf4ac30e7dc238fd2f4bc86332e0675', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Breastfeeding - lactation', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'PAR', 'state_type_id', NULL, 'absence_type_parenting', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Parenting', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'PATHM', 'state_type_id', NULL, 'absence_type_risk_pregnancy', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'At-risk pregnancy leave', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'PATHO', 'state_type_id', NULL, 'absence_type_interrupt_pregn', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Licence to interrupt pregnancy', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'RDL', 'state_type_id', NULL, 'absence_type_legal_reduction', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'By legal reduction of activity', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'DOER', 'state_type_id', NULL, '7b3cf78f9fd2a404567fe572fcb0eaf9', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Paid illness day', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'SD', 'state_type_id', NULL, 'absence_type_disc_susp', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Disciplinary Suspension', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'AE', 'state_type_id', NULL, 'absence_type_school_acc_h', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'School accompaniment (hours)', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'ESTH', 'state_type_id', NULL, 'absence_type_studyleave_h', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Student (h)', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAEF', 'state_type_id', NULL, '5ac088f356af3650149df3038ca8eba1', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Exceptional support Art.23', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FJNRD', 'state_type_id', NULL, '269b59b4efbbb4ef1d527492dc06cb60', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Unpaid justfied absence (Day)', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FJNRH', 'state_type_id', NULL, 'absence_type_justifiednotpaid_h', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Unpaid justfied absence (Hour)', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'IP', 'state_type_id', NULL, 'absence_type_proplact_iso', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Prophylactic isolation', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'OL', 'state_type_id', NULL, 'absence_type_legal_obl_h', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Legal obligations', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAFNH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_non_rem_fam_ass_h\"', 'Non remunerated family assistance hours - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAFRH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_rem_fam_ass_h\"', 'Remunerated family assistance - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FNRH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_unpaid_h\"', 'Unpaid absences hours - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'PAINH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_indiv_adapt_h\" AND data.`date` <= \"2021-08-31\"', 'Individual Adaptability (hours) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'AE', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_school_acc_h\"', 'School accompaniment (hours) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'ESTH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_studyleave_h\"', 'Student (h) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FJNRH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_justifiednotpaid_h\"', 'Unpaid justfied absence (Hour) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'OL', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_legal_obl_h\"', 'Legal obligations - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'DOE', 'state_type_id', NULL, 'c4a23d94c93c7500e3bdf97cc8cbf8b7', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Medical leave', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'DOP', 'state_type_id', NULL, 'absence_type_uncert_occ_ill', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Uncertified Occupational illness', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAFNR', 'state_type_id', NULL, 'absence_type_unpaid_fam_ass', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Unpaid Family Assistance (Days)', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FER', 'state_type_id', NULL, 'a272f564576d443e7832587126b070aa', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Vacation', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FRH', 'state_type_id', NULL, 'absence_type_paid_illness_h', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Paid justified absences', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FRH', 'state_type_id', NULL, 'absence_type_justifiedpaid', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Paid justified absences', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAFNH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_unpaid_fam_ass_h\"', 'Unpaid Family Assistance (Hours) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAFRH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_paid_fam_ass_h\"', 'Paid Family Assistance (Hours) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FOR', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_trl\"', 'Training - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FRH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_paid_illness_h\"', 'Paid justified absences - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'PAMA', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_lactation\"', 'Breastfeeding / Lactation - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'PACN', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_prenatal_app_h\"', 'Prenatal Consultation - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'DOERH', 'state_type_id', NULL, 'absence_type_paid_illness_real_h', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Paid illness (hours)', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'DOERH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_paid_illness_real_h\"', 'Paid illness (hours) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_SUP751', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum` - 3600', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Office%\" AND data.`dt__base_overtime_sum` > 3600 AND data.`is_restday` <> 1', 'OFFICE-s munkacsoport túlóra hétköznap 1 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_SUP501', 'inside_type_id', NULL, NULL, 0, 'IF(data.`dt__base_overtime_sum` > 3600, 3600, data.`dt__base_overtime_sum`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Office%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` <> 1', 'OFFICE-s munkacsoport túlóra hétköznap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_SUP101', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Flexible%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` = 1', 'Flexis munkacsoport túlóra hétvége', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_SUP101', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Office%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` = 1', 'OFFICE-s munkacsoport túlóra hétvége', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_SUP101', 'inside_type_id', NULL, NULL, 0, 'data.`weekend_ot`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`weekend_ot` > 0', 'OPERATION-s munkacsoport hétvégén keletkezett túlóra', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_SUP101', 'inside_type_id', NULL, NULL, 0, 'data.`weekend_ot`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`weekend_ot` > 0', 'WH-s munkacsoport hétvégén keletkezett túlóra', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_SUP501', 'inside_type_id', NULL, NULL, 0, 'IF(data.`weekday_ot` > 3600, 3600, data.`weekday_ot`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`weekday_ot` > 0', 'WH-s munkacsoport hétköznap keletkezett túlóra 1 óráig', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'PAINH', 'inside_type_id', NULL, NULL, 0, 'data.`frame_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`frame_ia` > 0 AND data.`date` <= \"2021-08-31\"', 'Minden munkacsoport összegyűlt egyenleg', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NOT', 'inside_type_id', NULL, NULL, 0, 'data.`dt__total_wtej_sum` + data.`dt__total_otej_sum` + data.`dt__total_otwej_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`dt__total_wtej_sum` + data.`dt__total_otej_sum` + data.`dt__total_otwej_sum`) > 0 AND (data.`workgroup_name` LIKE \"Operations%\" OR data.`workgroup_name` LIKE \"WH%\") AND data.`work_type` = \"FRAMEWORK_BALANCE\"', 'Éjszakai órák OPERATION-s és WH-s mcs.', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NOT2', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`value` > 0 AND data.`inside_type_id` IN (\"botej\", \"botwej\") AND (data.`workgroup_name` LIKE \"Operations%\" OR data.`workgroup_name` LIKE \"WH%\") AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`date_day_of_week` NOT IN (7, 1) AND data.`is_public_holiday` <> 1', 'Éjszakai órák OPERATION-s és WH-s mcs. szaldó éjszaka', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAINH', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`daily_ia` < 0 AND data.`date` <= \"2021-08-31\"', 'WH-s munkacsoport napi egyenleg negatív', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FAINH', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`daily_ia` < 0 AND data.`date` <= \"2021-08-31\"', 'OPERATION-s munkacsoport napi egyenleg negatív', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'AINH', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`daily_ia` > 0 AND data.`date` <= \"2021-08-31\"', 'WH-s munkacsoport napi egyenleg pozitív', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'AINH', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`daily_ia` > 0 AND data.`date` <= \"2021-08-31\"', 'OPERATION-s munkacsoport napi egyenleg pozitív', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FER', 'state_type_id', NULL, 'absence_type_vacation_h_d_4_h', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Vacation (half-day, 4 h)', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'W', 'inside_type_id', NULL, NULL, 0, 'IF((data.`workgroup_name` LIKE \"Flexible%\" OR data.`workgroup_name` LIKE \"IHT%\") AND data.`dt__schedule_sum` = 32400, 28800, IF((data.`workgroup_name` LIKE \"WH%\" OR data.`workgroup_name` LIKE \"Operations%\") AND data.`daily_ia` < 0, data.`dt__base_worktime_sum` + data.`daily_ia`, data.`dt__base_worktime_sum`)) - data.`dt__inside_absence_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`dt__base_worktime_sum` > 0', 'Ledolgozott munkaidő', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_SUP501', 'inside_type_id', NULL, NULL, 0, 'data.`weekday_ot`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`weekday_ot` > 0', 'OPERATION-s munkacsoport hétköznap keletkezett túlóra', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_SUP751', 'inside_type_id', NULL, NULL, 0, 'data.`weekday_ot` - 3600', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`weekday_ot` > 3600', 'WH-s munkacsoport hétköznap keletkezett túlóra 1 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'DOEH', 'state_type_id', NULL, 'absence_type_illness_h', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Illness (Hours)', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'DOEH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_illness_h\"', 'Illness (Hours) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'FNRH', 'inside_type_id', NULL, NULL, 0, 'ABS(data.`value`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`company_org_group1_id` IN (\"22\", \"27\", \"20e011fbdba9bc7a1f350e3c051cea8f\", \"29\", \"18\", \"e5dcf9b2525f438ec1d2b36936605a08\", \"40\") AND data.`inside_type_id` = \"paidot\" AND data.`value` < 0', 'Unjustified absence', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_BHP', 'inside_type_id', NULL, NULL, 0, 'data.`frame_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`frame_ia` > 0 AND data.`date` > \"2021-08-31\"', 'Minden munkacsoport összegyűlt egyenleg', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_BHP', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_indiv_adapt_h\" AND data.`date` > \"2021-08-31\"', 'Individual Adaptability (hours) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_BHP', 'state_type_id', NULL, 'absence_type_indiv_adapt_h', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Individual Adaptability (hours)', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_BHP', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"next_balancetr\" AND data.`value` > 0 AND data.`date` > \"2021-08-31\"', 'Egyenleg', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'GBH', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`daily_ia` < 0 AND data.`date` > \"2021-08-31\"', 'OPERATION-s munkacsoport napi egyenleg negatív', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'GBH', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`daily_ia` < 0 AND data.`date` > \"2021-08-31\"', 'WH-s munkacsoport napi egyenleg negatív', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'BHT', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Operations%\" AND data.`daily_ia` > 0 AND data.`date` > \"2021-08-31\"', 'OPERATION-s munkacsoport napi egyenleg pozitív', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'BHT', 'inside_type_id', NULL, NULL, 0, 'data.`daily_ia`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"WH%\" AND data.`work_type` = \"FRAMEWORK_BALANCE\" AND data.`daily_ia` > 0 AND data.`date` > \"2021-08-31\"', 'WH-s munkacsoport napi egyenleg pozitív', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'GBH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"taken_individual\" AND data.`date` > \"2021-08-31\"', 'Individual Adaptability (hours) (absence) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'AINH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"taken_individual\" AND data.`date` <= \"2021-08-31\"', 'Individual Adaptability (hours) (absence) - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=63, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 63-2021-09-15-16:00-------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'FAINH', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_abstime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`state_type_id` = \"taken_individual_old\" AND data.`date` > \"2021-08-31\"', 'Taken Individual adaptability absence', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'GBH', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_abstime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`state_type_id` = \"taken_individual\" AND data.`date` <= \"2021-08-31\"', 'Taken Bank of Hours absence', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=64, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 64-2021-09-20-08:45-------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'FER', 'state_type_id', NULL, '61cc57d3e69d0', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Vacation from next year', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=65, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 65-2022-01-13-09:00-------------------------------------------------

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'page_title_monthly_hour_distribution', 'Havi óramegoszlás kimutatás', '1'),
	('en', 'ttwa-base', 'page_title_monthly_hour_distribution', 'Monthly hour distribution report', '1'),
	('pt', 'ttwa-base', 'page_title_monthly_hour_distribution', 'Declaração de distribuição horária mensal', '1'),
	('hu', 'ttwa-base', 'menu_item_monthly_hour_distribution', 'Havi óramegoszlás kimutatás', '1'),
	('en', 'ttwa-base', 'menu_item_monthly_hour_distribution', 'Monthly hour distribution report', '1'),
	('pt', 'ttwa-base', 'menu_item_monthly_hour_distribution', 'Declaração de distribuição horária mensal', '1');

INSERT INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`,	`login_need`, `created_by`, `created_on`) VALUES
	('customers/stricker/monthlyHourDistribution', 'customers/stricker/monthlyHourDistribution', 'view', '1', '1', 'SN-DEV-12043', NOW());

INSERT INTO `auth_controller` (`controller_id`, `controller_name`, `controller_dict_id`, `created_by`, `created_on`) VALUES
	('customers/stricker/monthlyHourDistribution', 'MonthlyHourDistribution', 'menu_item_monthly_hour_distribution', 'SN-DEV-12043', NOW());

INSERT INTO `auth_role` (`role_id`, `role_name`, `description`, `created_by`, `created_on`) VALUES
    ('customers/stricker/monthlyHourDistribution', 'customers/stricker/monthlyHourDistribution --- view', 'Havi óramegoszlás kimutatás megtekintés jogosultság', 'SN-DEV-12043', NOW());

INSERT INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('a5b6bd79e008725744118c7c46e10cda', 'customers/stricker/monthlyHourDistribution', 'SN-DEV-12043', NOW());

INSERT INTO `menu_item_table` (`menu_item_id`,`menu_item_name`,`menu_modul`,`menu_label`,`menu_item_css_class`,`menu_url`,`menu_visible`,`menu_visible_operation`,`menu_item_parent_id`,`menu_order`) VALUES
	('monthly_hour_distribution', 'monthly_hour_distribution', 'ttwa-base', 'menu_item_monthly_hour_distribution', 'sub', '/customers/stricker/monthlyHourDistribution/index', 'customers/stricker/monthlyHourDistribution', 'view', 82, 202);

INSERT INTO `column_rights` (`controller_id`, `column_id`, `model_name`, `rolegroup_id`, `status`, `roles`, `created_by`, `created_on`) VALUES
	('customers/stricker/monthlyHourDistribution', 'emp_id', NULL, 'ALL', '2', NULL, 'SN-DEV-12043', NOW()),
	('customers/stricker/monthlyHourDistribution', 'fullname', NULL, 'ALL', '2', NULL, 'SN-DEV-12043', NOW()),
	('customers/stricker/monthlyHourDistribution', 'unit', NULL, 'ALL', '2', NULL, 'SN-DEV-12043', NOW()),
    ('customers/stricker/monthlyHourDistribution', 'section', NULL, 'ALL', '2', NULL, 'SN-DEV-12043', NOW()),
	('customers/stricker/monthlyHourDistribution', 'potential_hours', NULL, 'ALL', '2', NULL, 'SN-DEV-12043', NOW()),
	('customers/stricker/monthlyHourDistribution', 'absence_hours', NULL, 'ALL', '2', NULL, 'SN-DEV-12043', NOW()),
	('customers/stricker/monthlyHourDistribution', 'overtime_hours', NULL, 'ALL', '2', NULL, 'SN-DEV-12043', NOW()),
	('customers/stricker/monthlyHourDistribution', 'actual_worked_hours', NULL, 'ALL', '2', NULL, 'SN-DEV-12043', NOW());

INSERT INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
	('customers/stricker/monthlyHourDistribution', 'date', 'EMPLOYEE_WITH_FROM_TO', 'combo', '0', '2','SN-DEV-12043', NOW()),
	('customers/stricker/monthlyHourDistribution', 'group', 'COMPANY_ORG_GROUP1', 'combo', '0', '2', 'SN-DEV-12043', NOW()),
	('customers/stricker/monthlyHourDistribution', 'group', 'COMPANY_ORG_GROUP2', 'combo', '0', '2', 'SN-DEV-12043', NOW()),
	('customers/stricker/monthlyHourDistribution', 'group', 'WORKGROUP', 'combo', '0', '2', 'SN-DEV-12043', NOW()),
	('customers/stricker/monthlyHourDistribution', 'group', 'UNIT', 'combo', '0', '2', 'SN-DEV-12043', NOW()),
	('customers/stricker/monthlyHourDistribution', 'group', 'EMPLOYEECONTRACT', 'auto', '0', '2', 'SN-DEV-12043', NOW());

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'potential_hours', 'Törvényes óraszám', '1'),
    ('en', 'ttwa-base', 'potential_hours', 'Potential hours', '1'),
	('pt', 'ttwa-base', 'potential_hours', 'Horas Potenciais', '1'),
	('hu', 'ttwa-base', 'overtime_hours', 'Túlóra szám', '1'),
    ('en', 'ttwa-base', 'overtime_hours', 'Overtime hours', '1'),
	('pt', 'ttwa-base', 'overtime_hours', 'Horas Extra', '1'),
	('hu', 'ttwa-base', 'actual_worked_hours', 'Ténylegesen ledolgozott óraszám', '1'),
    ('en', 'ttwa-base', 'actual_worked_hours', 'Actual Worked Hours', '1'),
	('pt', 'ttwa-base', 'actual_worked_hours', 'Horas Trabalhadas', '1');

UPDATE `_sql_version` SET `revision`=66, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 66-2022-02-14-16:00-------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'EST', 'state_type_id', NULL, '63a8c36acab8e006aa0c192ce934e1b2', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Student absence', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'LE', 'state_type_id', NULL, '0c48b766ff7d9b6b28a89b93847ea2fe', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Electoral law', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=67, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 67-2022-03-08-14:00-------------------------------------------------

INSERT INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
	('wfm/summarySheet', 'group', 'COMPANY_ORG_GROUP1', 'combo', '0', '2','SN-DEV-12188', NOW()),
	('wfm/summarySheet', 'group', 'COMPANY_ORG_GROUP2', 'combo', '0', '2', 'SN-DEV-12188', NOW()),
	('wfm/frameBalanceManagementDaily', 'group', 'COMPANY_ORG_GROUP1', 'combo', '0', '2','SN-DEV-12188', NOW()),
	('wfm/frameBalanceManagementDaily', 'group', 'COMPANY_ORG_GROUP2', 'combo', '0', '2', 'SN-DEV-12188', NOW());

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'DEV-12188, Prev val: 0' WHERE `setting_id` = 'summarySheet_hideCompanyPayrollSearch';
UPDATE `app_settings` SET `setting_value` = '1', `note` = 'DEV-12188, Prev val: 0' WHERE `setting_id` = 'summarySheet_showEmployeeContractTypeSearch';

UPDATE `_sql_version` SET `revision`=68, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 68-2022-03-11-13:45-------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'DCH', 'state_type_id', NULL, 'absence_type_compensatory_rest_h', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Compensatory rest', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'DCH', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_compensatory_rest_h\"', 'Compensatory rest - jogcím', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=69, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 69-2022-04-14-09:00-------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'NH_SUP501', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` LIKE \"ot%\" AND data.`inside_type_id` NOT LIKE \"otw%\" AND data.`inside_type_id` NOT LIKE \"otstw%\" AND data.`workgroup_id` = \"4eb2a09a0a813365029080b8eaae91d2\"', 'Breastfeeding mothers hétköznapi túlóra', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=70, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 70-2022-04-22-09:00-------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'DEV-12777, Prev val: 0' WHERE `setting_id` = 'balanceRoundTolerance';

UPDATE `_sql_version` SET `revision`=71, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 71-2022-05-09-13:45-------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'NH_SUP101', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Manager%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` = 1', 'Manageres munkacsoport túlóra hétvége', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=72, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 72-2022-05-23-13:45-------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` = 'NH_SUP501' AND `note` = 'Breastfeeding mothers hétköznapi túlóra';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` = 'NH_SUP101' AND `note` = 'Breastfeedinges munkacsoport túlóra hétvége';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` = 'NH_SUP751' AND `note` = 'Breastfeeding-s munkacsoport túlóra hétköznap 1 órán felül';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` = 'NH_SUP751' AND `note` = 'Flexible-s munkacsoport túlóra hétköznap 1 órán felül';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` = 'NH_SUP751' AND `note` = 'Manager-s munkacsoport túlóra hétköznap 1 órán felül';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` = 'NH_SUP501' AND `note` = 'Breastfeeding-s munkacsoport túlóra hétköznap';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` = 'NH_SUP501' AND `note` = 'Flexible-s munkacsoport túlóra hétköznap';
UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` = 'NH_SUP501' AND `note` = 'Manager-s munkacsoport túlóra hétköznap';

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'NH_SUP101', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Breastfeeding%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` = 1', 'Breastfeedinges munkacsoport túlóra hétvége', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'NH_SUP751', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum` - 3600', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Breastfeeding%\" AND data.`dt__base_overtime_sum` > 3600 AND data.`is_restday` <> 1', 'Breastfeeding-s munkacsoport túlóra hétköznap 1 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_SUP501', 'inside_type_id', NULL, NULL, 0, 'IF(data.`dt__base_overtime_sum` > 3600, 3600, data.`dt__base_overtime_sum`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Breastfeeding%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` <> 1', 'Breastfeeding-s munkacsoport túlóra hétköznap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_SUP751', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum` - 3600', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Flexible%\" AND data.`dt__base_overtime_sum` > 3600 AND data.`is_restday` <> 1', 'Flexible-s munkacsoport túlóra hétköznap 1 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_SUP501', 'inside_type_id', NULL, NULL, 0, 'IF(data.`dt__base_overtime_sum` > 3600, 3600, data.`dt__base_overtime_sum`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Flexible%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` <> 1', 'Flexible-s munkacsoport túlóra hétköznap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_SUP751', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum` - 3600', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Manager%\" AND data.`dt__base_overtime_sum` > 3600 AND data.`is_restday` <> 1', 'Manager-s munkacsoport túlóra hétköznap 1 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
	('SAGE', 'NH_SUP501', 'inside_type_id', NULL, NULL, 0, 'IF(data.`dt__base_overtime_sum` > 3600, 3600, data.`dt__base_overtime_sum`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Manager%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` <> 1', 'Manager-s munkacsoport túlóra hétköznap', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=73, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 73-2022-05-26-08:45-------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'DEV-12777, Prev val: 0' WHERE `setting_id` = 'overtimebeforeunset';
UPDATE `app_settings` SET `setting_value` = '1', `note` = 'DEV-12777, Prev val: 0' WHERE `setting_id` = 'balancetimeback';

UPDATE `_sql_version` SET `revision`=74, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 74-2022-06-08-13:45-------------------------------------------------

UPDATE `dictionary`
SET `dict_value` = 'Férias marcadas'
WHERE `module` = 'ttwa-ahp'
AND `dict_value` = 'Férias tiradas'
AND `lang` = 'pt';

UPDATE `dictionary`
SET `dict_value` = 'Saldo de férias'
WHERE `module` = 'ttwa-ahp'
AND `dict_value` = 'Férias que podem ser usadas'
AND `lang` = 'pt';

UPDATE `_sql_version` SET `revision`=75, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 75-2022-06-21-08:45-------------------------------------------------


UPDATE `dictionary`
SET `dict_value` = 'Férias marcadas'
WHERE `dict_value` = 'Férias tiradas'
AND `lang` = 'pt';

UPDATE `_sql_version` SET `revision`=76, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 76-2022-06-21-09:45-------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'DEV-13541, Prev val: 0' WHERE `setting_id` = 'framework_balance_lost_time_to_other_insidetype';

UPDATE `_sql_version` SET `revision`=77, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 77-2022-08-09-13:45-------------------------------------------------

INSERT INTO `auth_role` (`role_id`, `role_name`, `description`, `created_by`, `created_on`) VALUES
    ('unjustifiedAbsencesReport', 'customers/stricker/reportUnjustifiedAbsences --- view', 'Indokolatlan hiányzás kimutatás (Stricker)', 'SN-DEV-13542', NOW());

INSERT INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('a5b6bd79e008725744118c7c46e10cda', 'unjustifiedAbsencesReport', 'SN-DEV-13542', NOW());

INSERT INTO `auth_acl` (`role_id`, `controller_id`, `column_name`, `operation_id`, `usergroup_id`, `access_right`, `created_by`, `created_on`) VALUES
    ('unjustifiedAbsencesReport', 'customers/stricker/reportUnjustifiedAbsences', NULL, 'view', NULL, 1, 'SN-DEV-13542', NOW());

INSERT INTO `auth_controller` (`controller_id`, `controller_name`, `controller_dict_id`, `created_by`, `created_on`) VALUES
	('customers/stricker/reportUnjustifiedAbsences', 'ReportUnjustifiedAbsences', 'menu_item_report_unjustified_absences', 'SN-DEV-13542', NOW());

INSERT INTO `menu_item_table` (`menu_item_id`, `menu_item_name`, `menu_modul`, `menu_label`, `menu_item_css_class`, `menu_url`, `menu_visible`, `menu_visible_operation`, `menu_item_parent_id`, `menu_order`) VALUES
	('report_unjustified_absences', 'report_unjustified_absences', 'ttwa-wfm', 'menu_item_report_unjustified_absences', 'sub', '/customers/stricker/reportUnjustifiedAbsences/index', 'customers/stricker/reportUnjustifiedAbsences', 'view', 82, 206);

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-wfm', 'page_title_report_unjustified_absences', 'Indokolatlan hiányzás kimutatás', '1'),
    ('en', 'ttwa-wfm', 'page_title_report_unjustified_absences', 'Unjustified absence report', '1'),
    ('pt', 'ttwa-wfm', 'page_title_report_unjustified_absences', 'Falta injustificada', '1'),
    ('hu', 'ttwa-wfm', 'menu_item_report_unjustified_absences', 'Indokolatlan hiányzás kimutatás', '1'),
    ('en', 'ttwa-wfm', 'menu_item_report_unjustified_absences', 'Unjustified absence report', '1'),
    ('pt', 'ttwa-wfm', 'menu_item_report_unjustified_absences', 'Falta injustificada', '1'),
	('hu', 'ttwa-wfm', 'report_unjustified_absences', 'Indokolatlan hiányzás kimutatás', '1'),
	('en', 'ttwa-wfm', 'report_unjustified_absences', 'Unjustified absence report', '1'),
    ('pt', 'ttwa-wfm', 'report_unjustified_absences', 'Falta injustificada', '1');

INSERT INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `status`, `created_by`, `created_on`) VALUES
	('customers/stricker/reportUnjustifiedAbsences', 'date', 'EMPLOYEE_WITH_FROM_TO', 2, 'SN-DEV-13542', NOW()),
	('customers/stricker/reportUnjustifiedAbsences', 'group', 'DEFAULT_GROUP_FILTER', 2, 'SN-DEV-13542', NOW()),
	('customers/stricker/reportUnjustifiedAbsences', 'group', 'COMPANY_ORG_GROUP1', 2, 'SN-DEV-13542', NOW()),
	('customers/stricker/reportUnjustifiedAbsences', 'group', 'COMPANY_ORG_GROUP2', 2, 'SN-DEV-13542', NOW());

UPDATE `_sql_version` SET `revision`=78, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 78-2022-08-12-10:30-------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'DEV-13821, Prev val: 0' WHERE `setting_id` = 'summarySheet_setting_4_reg_turned_on';
UPDATE `app_settings` SET `setting_value` = 'Managers', `note` = 'DEV-13821, Prev val: empty string' WHERE `setting_id` = 'summarySheet_setting_4_reg_workgroup';
UPDATE `app_settings` SET `setting_value` = '1;5', `note` = 'DEV-13821, Prev val: empty string' WHERE `setting_id` = 'summarySheet_setting_4_reg_daytypes';

UPDATE `_sql_version` SET `revision`=79, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 79-2022-09-22-16:00-------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'DEV-13954, Prev val: 0' WHERE `setting_id` = 'hasMobileLayout';

UPDATE `_sql_version` SET `revision`=80, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 80-2022-09-29-11:00-------------------------------------------------

UPDATE `payroll_transfer_config` SET `transfer_id` = 'HS101' WHERE `transfer_id` = 'NH_SUP101';
UPDATE `payroll_transfer_config` SET `transfer_id` = 'HS501' WHERE `transfer_id` = 'NH_SUP501';
UPDATE `payroll_transfer_config` SET `transfer_id` = 'HS751' WHERE `transfer_id` = 'NH_SUP751';

UPDATE `_sql_version` SET `revision`=81, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 81-2022-10-25-09:00-------------------------------------------------

UPDATE `dictionary` SET dict_value = 'Nappal' WHERE lang = 'hu' AND module = 'ttwa-wfm' AND dict_id = 'wtdailyworktime';
UPDATE `dictionary` SET dict_value = 'Daytime hours' WHERE lang = 'en' AND module = 'ttwa-wfm' AND dict_id = 'wtdailyworktime';
UPDATE `dictionary` SET dict_value = 'Horas diurnas' WHERE lang = 'pt' AND module = 'ttwa-wfm' AND dict_id = 'wtdailyworktime';

UPDATE `dictionary` SET dict_value = 'Éjszaka' WHERE lang = 'hu' AND module = 'ttwa-wfm' AND dict_id = 'wtej';
UPDATE `dictionary` SET dict_value = 'Night hours' WHERE lang = 'en' AND module = 'ttwa-wfm' AND dict_id = 'wtej';
UPDATE `dictionary` SET dict_value = 'Horas noturnas' WHERE lang = 'pt' AND module = 'ttwa-wfm' AND dict_id = 'wtej';

UPDATE `_sql_version` SET `revision`=82, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 82-2022-11-18-16:00-------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1800', `note` = 'DEV-14078, Prev val: 3600' WHERE `setting_id` = 'summarySheet_minOvertimeBeforeWork';

UPDATE `_sql_version` SET `revision`=83, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 83-2022-11-23-11:00-------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `sql_conditions_2` LIKE '%Breastfeeding%' AND `status` = 2;
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'HS101', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Breastfeeding%\" AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` = 1', 'Breastfeedinges munkacsoport túlóra hétvége', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'HS501', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Breastfeeding%\" AND data.`unit_id` IN (\"fb444f63547a91881961665689f24687\", \"d6106cafc72cf817e4ca3b10d5682a27\", \"c093973d386b45bf49d25e9f79ab082c\", \"0994776cf143ae20c0f3ce5d03949c58\") AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` <> 1', 'Breastfeeding-s munkacsoport (Production-shopfloor, structure, design-preimpressao, engineering egységek) túlóra hétköznap', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'HS501', 'inside_type_id', NULL, NULL, 0, 'IF(data.`dt__base_overtime_sum` > 3600, 3600, data.`dt__base_overtime_sum`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Breastfeeding%\" AND (data.`unit_id` NOT IN (\"fb444f63547a91881961665689f24687\", \"d6106cafc72cf817e4ca3b10d5682a27\", \"c093973d386b45bf49d25e9f79ab082c\", \"0994776cf143ae20c0f3ce5d03949c58\") OR data.`unit_id` IS NULL) AND data.`dt__base_overtime_sum` > 0 AND data.`is_restday` <> 1', 'Breastfeeding-s munkacsoport (nem Production-shopfloor, structure, design-preimpressao, engineering egységek) túlóra hétköznap 1 óráig', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
	('SAGE', 'HS751', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum` - 3600', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`workgroup_name` LIKE \"Breastfeeding%\" AND (data.`unit_id` NOT IN (\"fb444f63547a91881961665689f24687\", \"d6106cafc72cf817e4ca3b10d5682a27\", \"c093973d386b45bf49d25e9f79ab082c\", \"0994776cf143ae20c0f3ce5d03949c58\") OR data.`unit_id` IS NULL) AND data.`dt__base_overtime_sum` > 3600 AND data.`is_restday` <> 1', 'Breastfeeding-s munkacsoport (nem Production-shopfloor, structure, design-preimpressao, engineering egységek) túlóra hétköznap 1 órán felül', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=84, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 84-2022-12-02-11:15-------------------------------------------------

UPDATE `app_settings`
SET `setting_value` = '1', `note` = 'DEV-14469, Prev val: 0'
WHERE `setting_id` = 'summarySheet_showWarningIconIfCalcContainsOfUnjustified';

UPDATE `_sql_version` SET `revision`=85, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 85-2022-12-15-14:00-------------------------------------------------

DELETE FROM `public_holiday`
WHERE `holidaydate` BETWEEN '2022-01-01' AND '2023-12-31'
AND `country` IN('hu', 'ptlisboa', 'ptsede');

INSERT IGNORE INTO `public_holiday`
(`holidaydate`, `country`,  `name_dict_id`,                                 `name`,                     `type`, `company_id`, `status`, `created_by`, `created_on`)
VALUES
-- 2022. év:
('2022-01-01',  'hu',       'public_holiday_global_newyear',                'Újév',                      '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-01-01',  'ptlisboa', 'public_holiday_global_newyear',                'Újév',                      '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-01-01',  'ptsede',   'public_holiday_global_newyear',                'Újév',                      '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-03-01',  'ptlisboa', 'public_holiday_pt_carnival',                   'Húshagyó kedd',             '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-03-01',  'ptsede',   'public_holiday_pt_carnival',                   'Húshagyó kedd',             '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-03-14',  'hu',       'public_holiday_global_transferred_restday',    '2022. március 26. helyett', '5',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-03-15',  'hu',       'public_holiday_hu_15_mar',                     '1848-as forradalom',        '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-03-26',  'hu',       'public_holiday_global_transferred_workday',    '2022. március 14. helyett', 'D',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-04-15',  'hu',       'public_holiday_global_catholic_good_friday',   'Nagypéntek',                '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-04-15',  'ptlisboa', 'public_holiday_global_catholic_good_friday',   'Nagypéntek',                '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-04-15',  'ptsede',   'public_holiday_global_catholic_good_friday',   'Nagypéntek',                '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-04-17',  'hu',       'public_holiday_global_catholic_easter_sunday', 'Húsvét vasárnap',           '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-04-17',  'ptlisboa', 'public_holiday_global_catholic_easter_sunday', 'Húsvét vasárnap',           '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-04-17',  'ptsede',   'public_holiday_global_catholic_easter_sunday', 'Húsvét vasárnap',           '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-04-18',  'hu',       'public_holiday_global_catholic_easter_monday', 'Húsvét hétfő',              '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-04-18',  'ptlisboa', 'public_holiday_global_catholic_easter_monday', 'Húsvét hétfő',              '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-04-18',  'ptsede',   'public_holiday_global_catholic_easter_monday', 'Húsvét hétfő',              '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-04-25',  'ptlisboa', 'public_holiday_global_freedom_day',            'Szabadság napja',           '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-04-25',  'ptsede',   'public_holiday_global_freedom_day',            'Szabadság napja',           '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-05-01',  'hu',       'public_holiday_global_labour_day',             'Munka ünnepe',              '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-05-01',  'ptlisboa', 'public_holiday_global_labour_day',             'Munka ünnepe',              '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-05-01',  'ptsede',   'public_holiday_global_labour_day',             'Munka ünnepe',              '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-06-05',  'hu',       'public_holiday_global_catholic_whit_sunday',   'Pünkösd vasárnap',          '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-06-05',  'ptlisboa', 'public_holiday_global_catholic_whit_sunday',   'Pünkösd vasárnap',          '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-06-05',  'ptsede',   'public_holiday_global_catholic_whit_sunday',   'Pünkösd vasárnap',          '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-06-06',  'hu',       'public_holiday_global_catholic_whit_monday',   'Pünkösd hétfő',             '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-06-06',  'ptlisboa', 'public_holiday_global_catholic_whit_monday',   'Pünkösd hétfő',             '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-06-06',  'ptsede',   'public_holiday_global_catholic_whit_monday',   'Pünkösd hétfő',             '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-06-10',  'ptlisboa', 'public_holiday_pt_portugal_day',               'Portugália napja',          '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-06-10',  'ptsede',   'public_holiday_pt_portugal_day',               'Portugália napja',          '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-06-13',  'ptlisboa', 'public_holiday_pt_saint_anthony',              'Szent Antal napja',         '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-06-16',  'ptlisboa', 'public_holiday_pt_corpus_christi',             'Úrnapja',                   '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-06-16',  'ptsede',   'public_holiday_pt_corpus_christi',             'Úrnapja',                   '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-07-25',  'ptsede',   'public_holiday_global_local_holiday',          'Városi munkaszüneti nap',   '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-08-15',  'ptlisboa', 'public_holiday_global_assumption_of_mary',     'Nagyboldogasszony',         '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-08-15',  'ptsede',   'public_holiday_global_assumption_of_mary',     'Nagyboldogasszony',         '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-08-20',  'hu',       'public_holiday_hu_20_aug',                     'Az államalapítás ünnepe',   '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-10-05',  'ptlisboa', 'public_holiday_global_republic_day',           'A köztársaság napja',       '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-10-05',  'ptsede',   'public_holiday_global_republic_day',           'A köztársaság napja',       '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-10-15',  'hu',       'public_holiday_global_transferred_workday',    '2022. október 31. helyett', 'D',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-10-23',  'hu',       'public_holiday_hu_23_oct',                     '1956-os forradalom ünnepe', '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-10-31',  'hu',       'public_holiday_global_transferred_restday',    '2022. október 15. helyett', '5',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-11-01',  'hu',       'public_holiday_global_allsaints_day',          'Mindenszentek napja',       '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-11-01',  'ptlisboa', 'public_holiday_global_allsaints_day',          'Mindenszentek napja',       '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-11-01',  'ptsede',   'public_holiday_global_allsaints_day',          'Mindenszentek napja',       '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-12-01',  'ptlisboa', 'public_holiday_global_independence_day',       'A függetlenség napja',      '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-12-01',  'ptsede',   'public_holiday_global_independence_day',       'A függetlenség napja',      '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-12-08',  'ptlisboa', 'public_holiday_pt_immaculate',                 'Szeplőtelen fogantatás',    '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-12-08',  'ptsede',   'public_holiday_pt_immaculate',                 'Szeplőtelen fogantatás',    '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-12-25',  'hu',       'public_holiday_global_catholic_christmasday1', 'Karácsony első napja',      '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-12-25',  'ptlisboa', 'public_holiday_global_catholic_christmasday1', 'Karácsony első napja',      '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2022-12-25',  'ptsede',   'public_holiday_global_catholic_christmasday1', 'Karácsony első napja',      '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2022-12-26',  'hu',       'public_holiday_global_catholic_christmasday2', 'Karácsony másnapja',        '3',    'ALL',        2,        'DEV-14625',  NOW()),

-- 2023. év:
('2023-01-01',  'hu',       'public_holiday_global_newyear',                'Újév',                      '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-01-01',  'ptlisboa', 'public_holiday_global_newyear',                'Újév',                      '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-01-01',  'ptsede',   'public_holiday_global_newyear',                'Újév',                      '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-02-21',  'ptlisboa', 'public_holiday_pt_carnival',                   'Húshagyó kedd',             '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-02-21',  'ptsede',   'public_holiday_pt_carnival',                   'Húshagyó kedd',             '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-03-15',  'hu',       'public_holiday_hu_15_mar',                     '1848-as forradalom',        '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-04-07',  'hu',       'public_holiday_global_catholic_good_friday',   'Nagypéntek',                '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-04-07',  'ptlisboa', 'public_holiday_global_catholic_good_friday',   'Nagypéntek',                '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-04-07',  'ptsede',   'public_holiday_global_catholic_good_friday',   'Nagypéntek',                '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-04-09',  'hu',       'public_holiday_global_catholic_easter_sunday', 'Húsvét vasárnap',           '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-04-09',  'ptlisboa', 'public_holiday_global_catholic_easter_sunday', 'Húsvét vasárnap',           '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-04-09',  'ptsede',   'public_holiday_global_catholic_easter_sunday', 'Húsvét vasárnap',           '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-04-10',  'hu',       'public_holiday_global_catholic_easter_monday', 'Húsvét hétfő',              '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-04-10',  'ptlisboa', 'public_holiday_global_catholic_easter_monday', 'Húsvét hétfő',              '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-04-10',  'ptsede',   'public_holiday_global_catholic_easter_monday', 'Húsvét hétfő',              '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-04-25',  'ptlisboa', 'public_holiday_global_freedom_day',            'Szabadság napja',           '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-04-25',  'ptsede',   'public_holiday_global_freedom_day',            'Szabadság napja',           '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-05-01',  'hu',       'public_holiday_global_labour_day',             'Munka ünnepe',              '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-05-01',  'ptlisboa', 'public_holiday_global_labour_day',             'Munka ünnepe',              '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-05-01',  'ptsede',   'public_holiday_global_labour_day',             'Munka ünnepe',              '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-05-28',  'hu',       'public_holiday_global_catholic_whit_sunday',   'Pünkösd vasárnap',          '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-05-28',  'ptlisboa', 'public_holiday_global_catholic_whit_sunday',   'Pünkösd vasárnap',          '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-05-28',  'ptsede',   'public_holiday_global_catholic_whit_sunday',   'Pünkösd vasárnap',          '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-05-29',  'hu',       'public_holiday_global_catholic_whit_monday',   'Pünkösd hétfő',             '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-05-29',  'ptlisboa', 'public_holiday_global_catholic_whit_monday',   'Pünkösd hétfő',             '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-05-29',  'ptsede',   'public_holiday_global_catholic_whit_monday',   'Pünkösd hétfő',             '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-06-08',  'ptlisboa', 'public_holiday_pt_corpus_christi',             'Úrnapja',                   '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-06-08',  'ptsede',   'public_holiday_pt_corpus_christi',             'Úrnapja',                   '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-06-10',  'ptlisboa', 'public_holiday_pt_portugal_day',               'Portugália napja',          '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-06-10',  'ptsede',   'public_holiday_pt_portugal_day',               'Portugália napja',          '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-06-13',  'ptlisboa', 'public_holiday_pt_saint_anthony',              'Szent Antal napja',         '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-07-25',  'ptsede',   'public_holiday_global_local_holiday',          'Városi munkaszüneti nap',   '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-08-15',  'ptlisboa', 'public_holiday_global_assumption_of_mary',     'Nagyboldogasszony',         '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-08-15',  'ptsede',   'public_holiday_global_assumption_of_mary',     'Nagyboldogasszony',         '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-08-20',  'hu',       'public_holiday_hu_20_aug',                     'Az államalapítás ünnepe',   '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-10-05',  'ptlisboa', 'public_holiday_global_republic_day',           'A köztársaság napja',       '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-10-05',  'ptsede',   'public_holiday_global_republic_day',           'A köztársaság napja',       '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-10-23',  'hu',       'public_holiday_hu_23_oct',                     '1956-os forradalom ünnepe', '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-11-01',  'hu',       'public_holiday_global_allsaints_day',          'Mindenszentek napja',       '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-11-01',  'ptlisboa', 'public_holiday_global_allsaints_day',          'Mindenszentek napja',       '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-11-01',  'ptsede',   'public_holiday_global_allsaints_day',          'Mindenszentek napja',       '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-12-01',  'ptlisboa', 'public_holiday_global_independence_day',       'A függetlenség napja',      '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-12-01',  'ptsede',   'public_holiday_global_independence_day',       'A függetlenség napja',      '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-12-08',  'ptlisboa', 'public_holiday_pt_immaculate',                 'Szeplőtelen fogantatás',    '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-12-08',  'ptsede',   'public_holiday_pt_immaculate',                 'Szeplőtelen fogantatás',    '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-12-25',  'hu',       'public_holiday_global_catholic_christmasday1', 'Karácsony első napja',      '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-12-25',  'ptlisboa', 'public_holiday_global_catholic_christmasday1', 'Karácsony első napja',      '3',    'ALL',        2,        'DEV-14625',  NOW()),
('2023-12-25',  'ptsede',   'public_holiday_global_catholic_christmasday1', 'Karácsony első napja',      '3',    'ALL',        2,        'DEV-14625',  NOW()),

('2023-12-26',  'hu',       'public_holiday_global_catholic_christmasday2', 'Karácsony másnapja',        '3',    'ALL',        2,        'DEV-14625',  NOW());

UPDATE
    `company`
SET
    `country`     = 'ptsede',
    `modified_by` = 'DEV-14625',
    `modified_on` = NOW()
WHERE `status` = 2
    AND `country` <> 'ptsede'
    AND `country` <> 'hu'
    AND `company_name` LIKE 'Sede%'
    AND CURDATE() BETWEEN `company`.`valid_from` AND `company`.`valid_to`;

UPDATE
    `company`
SET
    `country`     = 'ptlisboa',
    `modified_by` = 'DEV-14625',
    `modified_on` = NOW()
WHERE `status` = 2
    AND `country` <> 'ptlisboa'
    AND `country` <> 'hu'
    AND `company_name` LIKE 'Lisboa%'
    AND CURDATE() BETWEEN `company`.`valid_from` AND `company`.`valid_to`;

UPDATE `_sql_version` SET `revision`=86, `updated_on`=NOW() WHERE `module`='c_stricker';

-- VERSION 86-2023-01-06-16:00-------------------------------------------------DEV-14625

INSERT INTO `country`
(`country_hu_name`,         `country_sign`, `country_capital`,  `country_en_name`)
VALUES
('Portugália/Lisszabon',    'ptlisboa',     'Lisszabon',        'Portugal/Lisbon'),
('Portugália/Sede',         'ptsede',       'Sede',             'Portugal/Sede');

UPDATE `_sql_version` SET `revision`=87, `updated_on`=NOW() WHERE `module`='c_stricker';

-- VERSION 87-2023-01-31-15:00-------------------------------------------------DEV-14889

UPDATE `dictionary` SET `dict_value` = 'Fel nem használt előző évi szabadság' WHERE `dict_id` = 'base_absence_type_previous_year' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Unused previous years leave' WHERE `dict_id` = 'base_absence_type_previous_year' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Férias do ano anterior' WHERE `dict_id` = 'base_absence_type_previous_year' AND `lang` = 'pt';

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'DEV-12187, Prev val: 0' WHERE `setting_id` = 'showPreviousYearFrameLineOnYearLayout';

UPDATE `_sql_version` SET `revision`=88, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 88-2023-02-20-09:15-------------------------------------------------

INSERT IGNORE INTO `public_holiday`
(`holidaydate`, `country`,  `name_dict_id`,										`name`,							`type`, 	`company_id`, `status`, `created_by`, `created_on`)
VALUES
('2024-01-01',	'ptlisboa',	'public_holiday_pt_newyear',						"New Year's Day",				'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-02-13',	'ptlisboa',	'public_holiday_pt_carnival',						"Carnival",						'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-03-29',	'ptlisboa',	'public_holiday_pt_good_friday',					"Good Friday",					'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-03-31',	'ptlisboa',	'public_holiday_pt_easter',							"Easter Sunday",				'2',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-04-25',	'ptlisboa',	'public_holiday_pt_easter_monday',					"Liberation Day",				'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-05-01',	'ptlisboa',	'public_holiday_pt_labour_day',						"Labour Day",					'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-05-30',	'ptlisboa',	'public_holiday_pt_corpus_christi',					"Corpus Christi",				'2',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-06-10',	'ptlisboa',	'public_holiday_pt_national_day',					"National Day",					'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-06-13',	'ptlisboa',	'public_holiday_pt_st_anthonys_day',				"St. Anthony's Day",			'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-08-15',	'ptlisboa',	'public_holiday_pt_municipal_holiday',				"Assumption Day",				'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-10-05',	'ptlisboa',	'public_holiday_pt_republic_day',					"Republic Day",					'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-11-01',	'ptlisboa',	'public_holiday_hun_all_saints_day',				"All Saints' Day",				'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-12-01',	'ptlisboa',	'public_holiday_pt_independence_restoration_day',	"Independence Restoration Day",	'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-12-08',	'ptlisboa',	'public_holiday_pt_immaculate_conception',			"Immaculate Conception",		'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-12-25',	'ptlisboa',	'public_holiday_pt_christmasday',					"Christmas Day",				'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-01-01',	'ptsede',	'public_holiday_pt_newyear',						"New Year's Day",				'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-02-13',	'ptsede',	'public_holiday_pt_carnival',						"Carnival",						'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-03-29',	'ptsede',	'public_holiday_pt_good_friday',					"Good Friday",					'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-03-31',	'ptsede',	'public_holiday_pt_easter',							"Easter Sunday",				'2',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-04-25',	'ptsede',	'public_holiday_pt_easter_monday',					"Liberation Day",				'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-05-01',	'ptsede',	'public_holiday_pt_labour_day',						"Labour Day",					'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-05-30',	'ptsede',	'public_holiday_pt_corpus_christi',					"Corpus Christi",				'2',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-06-10',	'ptsede',	'public_holiday_pt_national_day',					"National Day",					'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-07-25',	'ptsede',	'public_holiday_pt_october_23th',					"Municipal Holiday",			'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-08-15',	'ptsede',	'public_holiday_pt_municipal_holiday',				"Assumption Day",				'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-10-05',	'ptsede',	'public_holiday_pt_republic_day',					"Republic Day",					'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-11-01',	'ptsede',	'public_holiday_hun_all_saints_day',				"All Saints' Day",				'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-12-01',	'ptsede',	'public_holiday_pt_independence_restoration_day',	"Independence Restoration Day",	'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-12-08',	'ptsede',	'public_holiday_pt_immaculate_conception',			"Immaculate Conception",		'3',			'ALL',		2,		'SZOF-1711',	NOW()),
('2024-12-25',	'ptsede',	'public_holiday_pt_christmasday',					"Christmas Day",				'3',			'ALL',		2,		'SZOF-1711',	NOW());

UPDATE `_sql_version` SET `revision`=89, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 89-2023-10-27-14:24-------------------------------------------------

UPDATE 
	`app_settings` 
SET `setting_value` = 'workgroup_id', 
	`modified_by` = 'SZOF-3119', 
    `modified_on` = NOW(), 
    `note` = 'Prev value: NONE' 
WHERE `setting_id` = 'link_group_to_terminal_related_id';

UPDATE `_sql_version` SET `revision`=90, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 90-2024-04-17-14:24-------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = '365',
    `modified_by` = 'SZOF-3402',
    `modified_on` = NOW(),
    `note` = 'Prev val: 62, Naptípus utánkövetés ha érvényesség több mint 1 év, ennyi napot módosít a mai naphoz képest (ha még a naptípus érvényességébe bele esik!)'
WHERE
    `setting_id` = 'daytype_follow_changes_days'
AND `valid` = '1';

UPDATE
    `app_settings`
SET `setting_value` = '1',
    `modified_by` = 'SZOF-3402',
    `modified_on` = NOW(),
    `note` = 'Prev val: 0, Naptípus módosítás levezetés módosítatlan egyéni munkarendre'
WHERE
    `setting_id` = 'daytype_follow_changes_wsu'
  AND `valid` = '1';

UPDATE `_sql_version` SET `revision`=91, `updated_on`=NOW() WHERE `module` = 'c_stricker';

-- VERSION 91-2024-05-29-16:00-------------------------------------------------
