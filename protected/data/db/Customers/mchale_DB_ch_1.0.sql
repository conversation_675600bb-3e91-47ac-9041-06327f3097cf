-- # RELEASE 1.0

INSERT INTO `_sql_version` (`id`, `module`, `major_minor`, `revision`, `updated_on`) VALUES (NULL, 'c_mchale', '1.0', '0', '2015-10-08 14:00:00') ON DUPLICATE KEY UPDATE `module`=VALUES(`module`), `major_minor`=VALUES(`major_minor`), `revision`=VALUES(`revision`);

UPDATE `_sql_version` SET `revision`=1, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE `approver` a, `user` u SET a.`related_value`='törölni' WHERE a.`approver_user_id`=u.`user_id` and u.`rolegroup_id`='588506584bfbeadb4719b723d7a71b44' and process_id='employeeManagement';
DELETE FROM `approver` WHERE `related_value`='törölni';

INSERT INTO `approver` (`row_id`,`process_id`,`related_model`,`related_id`,`related_value`,`approver_user_id`,`note`,`valid_from`,`valid_to`,`status`,`created_by`,`created_on`,`modified_by`,`modified_on`,`pre_row_id`) VALUES
 (null,'employeeManagement','Company','company_id','ALL','1082','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:09:31',NULL,NULL,NULL),
 (null,'employeeManagement','EmployeeContract','employee_contract_id','ALL','1082','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:10:30',NULL,NULL,NULL),
 (null,'employeeManagement','Unit','unit_id','ALL','1082','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:10:48',NULL,NULL,NULL),
 (null,'employeeManagement','Workgroup','workgroup_id','ALL','1082','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:11:07',NULL,NULL,NULL),
 (null,'employeeManagement','Payroll','payroll_id','ALL','1082','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:11:21',NULL,NULL,NULL),

 (null,'employeeManagement','Company','company_id','ALL','1007','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:09:31',NULL,NULL,NULL),
 (null,'employeeManagement','EmployeeContract','employee_contract_id','ALL','1007','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:10:30',NULL,NULL,NULL),
 (null,'employeeManagement','Unit','unit_id','ALL','1007','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:10:48',NULL,NULL,NULL),
 (null,'employeeManagement','Workgroup','workgroup_id','ALL','1007','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:11:07',NULL,NULL,NULL),
 (null,'employeeManagement','Payroll','payroll_id','ALL','1007','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:11:21',NULL,NULL,NULL),

 (null,'employeeManagement','Company','company_id','ALL','1208','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:09:31',NULL,NULL,NULL),
 (null,'employeeManagement','EmployeeContract','employee_contract_id','ALL','1208','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:10:30',NULL,NULL,NULL),
 (null,'employeeManagement','Unit','unit_id','ALL','1208','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:10:48',NULL,NULL,NULL),
 (null,'employeeManagement','Workgroup','workgroup_id','ALL','1208','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:11:07',NULL,NULL,NULL),
 (null,'employeeManagement','Payroll','payroll_id','ALL','1208','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:11:21',NULL,NULL,NULL),

 (null,'employeeManagement','Company','company_id','ALL','1038','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:09:31',NULL,NULL,NULL),
 (null,'employeeManagement','EmployeeContract','employee_contract_id','ALL','1038','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:10:30',NULL,NULL,NULL),
 (null,'employeeManagement','Unit','unit_id','ALL','1038','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:10:48',NULL,NULL,NULL),
 (null,'employeeManagement','Workgroup','workgroup_id','ALL','1038','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:11:07',NULL,NULL,NULL),
 (null,'employeeManagement','Payroll','payroll_id','ALL','1038','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:11:21',NULL,NULL,NULL),

 (null,'employeeManagement','Company','company_id','ALL','1079','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:09:31',NULL,NULL,NULL),
 (null,'employeeManagement','EmployeeContract','employee_contract_id','ALL','1079','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:10:30',NULL,NULL,NULL),
 (null,'employeeManagement','Unit','unit_id','ALL','1079','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:10:48',NULL,NULL,NULL),
 (null,'employeeManagement','Workgroup','workgroup_id','ALL','1079','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:11:07',NULL,NULL,NULL),
 (null,'employeeManagement','Payroll','payroll_id','ALL','1079','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:11:21',NULL,NULL,NULL),

 (null,'employeeManagement','Company','company_id','ALL','1001','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:09:31',NULL,NULL,NULL),
 (null,'employeeManagement','EmployeeContract','employee_contract_id','ALL','1001','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:10:30',NULL,NULL,NULL),
 (null,'employeeManagement','Unit','unit_id','ALL','1001','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:10:48',NULL,NULL,NULL),
 (null,'employeeManagement','Workgroup','workgroup_id','ALL','1001','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:11:07',NULL,NULL,NULL),
 (null,'employeeManagement','Payroll','payroll_id','ALL','1001','','2015-01-01','2038-01-01',2,'6acd9683761b153750db382c1c3694f6','2015-11-27 16:11:21',NULL,NULL,NULL);

UPDATE `_sql_version` SET `revision`=2, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE `company` SET `company_id`=MD5('McHale') WHERE `company_name`='McHale';
UPDATE `company` SET `valid_to`='2038-01-01' WHERE `company_name`='McHale Hungária Kft.';

UPDATE `_sql_version` SET `revision`=3, `updated_on`=NOW() WHERE `module` = 'c_mchale';
-- VERSION --------------------------------------------------------------------

INSERT INTO `auth_role_in_group` (`row_id`,`rolegroup_id`,`role_id`,`created_by`,`created_on`) VALUES
(null,'ed32a0ab14aab435303726d80e2e1221','3edbd8510b1713fcb1bf2ab629068f12','6acd9683761b153750db382c1c3694f6','2015-12-01 12:25:19'),
(null,'ed32a0ab14aab435303726d80e2e1221','b4f220772f7af725b605485aa3c5a458','6acd9683761b153750db382c1c3694f6','2015-12-01 12:25:48'),
(null,'ed32a0ab14aab435303726d80e2e1221','9e92710cbaa1eb79d0a7e4e249565d6b','6acd9683761b153750db382c1c3694f6','2015-12-01 12:26:18');

UPDATE `_sql_version` SET `revision`=4, `updated_on`=NOW() WHERE `module` = 'c_mchale';
-- VERSION --------------------------------------------------------------------

UPDATE `reader` SET `is_calc`=1 WHERE 1;

UPDATE `_sql_version` SET `revision`=5, `updated_on`=NOW() WHERE `module` = 'c_mchale';
-- VERSION --------------------------------------------------------------------

UPDATE `app_settings` SET setting_value = 1 WHERE `setting_id` = 'summarySheet_rule_regs_no_cause';

UPDATE `_sql_version` SET `revision`=6, `updated_on`=NOW() WHERE `module` = 'c_mchale';
-- VERSION --------------------------------------------------------------------

UPDATE `event_type` SET status = 4 WHERE 1;
UPDATE `event_type` SET status = 2 WHERE event_type_id='MZG';

UPDATE `_sql_version` SET `revision`=7, `updated_on`=NOW() WHERE `module` = 'c_mchale';
-- VERSION --------------------------------------------------------------------

UPDATE `state_type` SET in_use = 1 WHERE state_type_id='aad4833e34da5d4a6eb00a13088aeab5';
UPDATE `dictionary` SET dict_value='Hivatalos kiküldetés' WHERE dict_id='absence_type_delegacy' and lang='hu';

UPDATE `_sql_version` SET `revision`=8, `updated_on`=NOW() WHERE `module` = 'c_mchale';
-- VERSION --------------------------------------------------------------------

DELETE FROM `competency_levels` WHERE `row_id` > 3;

UPDATE `_sql_version` SET `revision`=9, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

INSERT INTO `auth_role_in_group` (`row_id`, `rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    (NULL, '588506584bfbeadb4719b723d7a71b44', 'bdce35bbfeae0d66e68889bb37626219', '6acd9683761b153750db382c1c3694f6', '2016-01-05 11:20:00');

UPDATE `_sql_version` SET `revision`=10, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

INSERT INTO `auth_role_in_group` (`row_id`, `rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    (NULL, '588506584bfbeadb4719b723d7a71b44', '2241c8af6279461412095b6996a5fa52', '6acd9683761b153750db382c1c3694f6', '2016-01-05 11:30:00');

UPDATE `_sql_version` SET `revision`=11, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

DROP TABLE IF EXISTS `payroll_users`;
CREATE TABLE IF NOT EXISTS `payroll_users` (
  `row_id` int(11) NOT NULL,
  `payroll_user_id` int(11) DEFAULT NULL,
  `user_id` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Lekérdezés azonosítója',
  `user_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Megnevezés',
  `detail_where` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Megjelenítendő sorok',
  `sum_where` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Megjelenítendő sorok',
  `detail_per_due` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'Jogcímek külön sorban?',
  `code_order` int(11) NOT NULL DEFAULT '6' COMMENT 'Tételesnél kód helye',
  `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'INS/DEL kell?',
  `type_order` int(11) NOT NULL DEFAULT '5' COMMENT 'Type mező helye',
  `time_function` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Időadat átalakítása',
  `all_sum` tinyint(4) DEFAULT NULL COMMENT '0: tételes, 1: összesen',
  `status` tinyint(4) NOT NULL,
  `created_by` varchar(32) COLLATE utf8_unicode_ci NOT NULL,
  `created_on` datetime NOT NULL,
  `pre_row_id` int(11) DEFAULT NULL,
  `modified_by` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL,
  `modified_on` datetime DEFAULT NULL
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

INSERT INTO `payroll_users` (`row_id`, `payroll_user_id`, `user_id`, `user_name`, `detail_where`, `sum_where`, `detail_per_due`, `code_order`, `type`, `type_order`, `time_function`, `all_sum`, `status`, `created_by`, `created_on`, `pre_row_id`, `modified_by`, `modified_on`) VALUES
(18, 17, 'mchale', 'McHale', '', '', 1, 4, 0, 0, 'SEC_TO_HOUR_2_DECIMAL_LOGIN', 0, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-08 13:35:38', 17, '6acd9683761b153750db382c1c3694f6', '2016-01-08 13:36:25');

ALTER TABLE `payroll_users`
  ADD PRIMARY KEY (`row_id`);

ALTER TABLE `payroll_users`
  MODIFY `row_id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=19;

UPDATE `_sql_version` SET `revision`=12, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

INSERT INTO `due_to_payroll` (`row_id`, `due_to_payroll_id`, `user_id`, `due_id`, `due_label`, `due_value`, `due_sum`, `due_which`, `due_group`, `due_order`, `due_formula`, `due_view`, `due_due`, `due_code`, `due_other_code`, `due_other_value`, `due_other_order`, `due_null`, `due_time`, `all_sum`, `status`, `created_by`, `created_on`, `pre_row_id`, `modified_by`, `modified_on`) VALUES
(null, 0, 'mchale', 'gyap', 'GYÁP', 'childcaresickpay', NULL, 'refer', 0, 5, NULL, 1, 1, '306', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 10:42:30', NULL, NULL, NULL),
(null, 0, 'mchale', 'szabadsag', 'Fizetett szabadság', 'onleave', NULL, 'refer', NULL, 5, NULL, 1, 1, '300', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 10:40:09', 286, '6acd9683761b153750db382c1c3694f6', '2016-01-11 10:42:57'),
(null, 0, 'mchale', 'ubaleset', 'Üzemi baleset', 'accidentsickpay', NULL, '', NULL, 5, NULL, 1, 1, '308', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 10:44:49', NULL, NULL, NULL),
(null, 0, 'mchale', 'gyed', 'GYED', 'gyed_time', NULL, '', NULL, 5, NULL, 1, 1, '309', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 10:46:48', NULL, NULL, NULL),
(null, 0, 'mchale', 'gyes', 'GYES', 'gyes_time', NULL, '', NULL, 5, NULL, 1, 1, '310', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 10:48:24', NULL, NULL, NULL),
(null, 0, 'mchale', 'nemfizetettszabi', 'Igazolt fizetés nélküli hiányzás', 'withoutpayleave_time', NULL, '', NULL, 5, NULL, 1, 1, '322', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 10:51:55', NULL, NULL, NULL),
(null, 0, 'mchale', 'igazolatlan', 'Igazolatlan távollét', 'igazolatlan', NULL, '', NULL, 5, NULL, 1, 1, '323', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 10:56:41', 293, '6acd9683761b153750db382c1c3694f6', '2016-01-11 10:57:26'),
(null, 0, 'mchale', 'fizetett_igazolt', 'Igazolt fizetett távollét', 'fizetett_igazolt', NULL, '', NULL, 5, NULL, 1, 1, '329', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:01:23', NULL, NULL, NULL),
(null, 0, 'mchale', 'kozhozhal_csak', 'Közeli hozzátartozó halála', 'kozhozhal_csak', NULL, '', NULL, 5, NULL, 1, 1, '335', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:05:14', NULL, NULL, NULL),
(null, 0, 'mchale', 'szakszervezet', 'Szakszervezet', 'szakszervezet', NULL, '', NULL, 5, NULL, 1, 1, '339', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:10:23', NULL, NULL, NULL),
(null, 0, 'mchale', 'uzemi_tanacs', 'Üzemi tanács', 'uzemi_tanacs', NULL, '', NULL, 5, NULL, 1, 1, '340', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:12:00', NULL, NULL, NULL),
(null, 0, 'mchale', 'munkavedelem', 'Munkavédelmi képviselet', 'munkavedelem', NULL, '', NULL, 5, NULL, 1, 1, '341', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:13:43', NULL, NULL, NULL),
(null, 0, 'mchale', 'tanulmanyi', 'Tanulmányi szabadság', 'studyleave', NULL, '', NULL, 5, NULL, 1, 1, '342', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:15:38', NULL, NULL, NULL),
(null, 0, 'mchale', 'allasido', 'Állásidő', 'allasido', NULL, '', NULL, 5, NULL, 1, 1, '364', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:18:55', NULL, NULL, NULL),
(null, 0, 'mchale', 'apaszabi', 'Munkaidő kedvezmény', 'paternityleave', NULL, '', NULL, 5, NULL, 1, 1, '378', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:23:06', NULL, NULL, NULL),
(null, 0, 'mchale', 'csusztatas', 'Csúsztatás', 'if(csusztatas_napi=0,csusztatas_naptipus,csusztatas_napi)', NULL, '', NULL, 5, 1, 1, 1, '445', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:27:22', NULL, NULL, NULL),
(null, 0, 'mchale', 'tuloramunkanap', 'Rendkívüli munkavégzés munkanapi', 'tuloramunkanap', 1, 'refer', NULL, 5, NULL, 1, 1, '1402', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:43:02', NULL, NULL, NULL),
(null, 0, 'mchale', 'tulorapihenonap', 'Rendkívüli munkavégzés pihenőnapi', 'tulorapihenonap', 1, 'refer', NULL, 5, NULL, 1, 1, '1403', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:45:49', NULL, NULL, NULL),
(null, 0, 'mchale', 'keret_balance', 'Túlóra munkaidőkeret', 'keret_munka+keret_tavollet-keret_tvmunkaido', 1, 'frame', NULL, 5, 1, 1, 1, '1426', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:48:15', 307, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:48:45'),
(null, 0, 'mchale', 'tvmunkaido', 'Ledolgozott munkaórák', 'tvmunkaido', 1, 'refer', NULL, 5, NULL, 1, 1, '403', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:53:09', NULL, NULL, NULL),
(null, 0, 'mchale', 'muszakpotlek', 'Műszakpótlék (18 óra és 6 óra között)', 'muszakpotlek', 1, 'refer', NULL, 5, NULL, 1, 1, '1601', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:57:33', NULL, NULL, NULL),
(null, 0, 'mchale', 'betegszabi', '?betegségnapok (valószínű táppénz)', 'disease_time', NULL, '', NULL, 5, NULL, 1, 1, '357', '', '', 8, NULL, 1, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:17:21', 301, '6acd9683761b153750db382c1c3694f6', '2016-01-11 12:57:46'),
(null, 0, 'mchale', 'nev', 'Név', 'full_name', NULL, '', 1, 3, NULL, 1, 0, '', '', '', 8, NULL, NULL, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 13:04:19', NULL, NULL, NULL),
(null, 0, 'mchale', 'torzsszam', 'Törzsszám', 'employee_id', NULL, '', 1, 2, NULL, 1, 0, '', '', '', 8, NULL, NULL, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 13:06:05', NULL, NULL, NULL),
(null, 0, 'mchale', 'csusztatas_napi', 'Csúsztatás (napi távollét alapján)', 'csusztatas_napi', NULL, '', NULL, NULL, NULL, 0, 0, '', '', '', 8, NULL, NULL, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 13:11:21', 314, '6acd9683761b153750db382c1c3694f6', '2016-01-11 13:11:56'),
(null, 0, 'mchale', 'csusztatas_naptipus', 'Csúsztatás (naptípus alapján)', 'csusztatas_naptipus', NULL, '', NULL, NULL, NULL, 0, 0, '', '', '', 8, NULL, NULL, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 13:15:10', NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=13, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE app_settings SET setting_value='unit' WHERE setting_id='heatmapGroup';

UPDATE `_sql_version` SET `revision`=14, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE `dictionary` SET dict_value='Mozgóbéresek' WHERE lang='hu'
AND dict_id in ('company_org_group3','menu_item_companyorggroup3','page_title_companyorggroup3');

UPDATE `dictionary` SET dict_value='mozgoberesek' WHERE lang='hu'
AND dict_id='export_file_companyorggroup3';

INSERT INTO `company_org_group3`(`company_org_group_id`, `company_org_group_name`, `valid_from`, `valid_to`, `status`,`created_by`,`created_on`) VALUES
(1,'Igen','2016-01-01','2038-01-01',2,'system','2016-02-05 00:00:00'),
(2,'Nem','2016-01-01','2038-01-01',2,'system','2016-02-05 00:00:00');

UPDATE `_sql_version` SET `revision`=15, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE `dictionary` SET dict_value='Movingwage' WHERE lang='en'
AND dict_id in ('company_org_group3','menu_item_companyorggroup3','page_title_companyorggroup3');

UPDATE `dictionary` SET dict_value='movingwage' WHERE lang='en'
AND dict_id='export_file_companyorggroup3';

UPDATE `_sql_version` SET `revision`=16, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

DROP TABLE IF EXISTS `payroll_users`;
CREATE TABLE IF NOT EXISTS `payroll_users` (
  `row_id` int(11) NOT NULL,
  `payroll_user_id` int(11) DEFAULT NULL,
  `user_id` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Lekérdezés azonosítója',
  `user_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Megnevezés',
  `detail_where` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Megjelenítendő sorok',
  `sum_where` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Megjelenítendő sorok',
  `detail_per_due` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'Jogcímek külön sorban?',
  `code_order` int(11) NOT NULL DEFAULT '6' COMMENT 'Tételesnél kód helye',
  `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'INS/DEL kell?',
  `type_order` int(11) NOT NULL DEFAULT '5' COMMENT 'Type mező helye',
  `time_function` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Időadat átalakítása',
  `all_sum` tinyint(4) DEFAULT NULL COMMENT '0: tételes, 1: összesen',
  `status` tinyint(4) NOT NULL,
  `created_by` varchar(32) COLLATE utf8_unicode_ci NOT NULL,
  `created_on` datetime NOT NULL,
  `pre_row_id` int(11) DEFAULT NULL,
  `modified_by` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL,
  `modified_on` datetime DEFAULT NULL
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

ALTER TABLE `payroll_users`
  ADD PRIMARY KEY (`row_id`);

ALTER TABLE `payroll_users`
  MODIFY `row_id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=1;

INSERT INTO `payroll_users` (`row_id`, `payroll_user_id`, `user_id`, `user_name`, `detail_where`, `sum_where`, `detail_per_due`, `code_order`, `type`, `type_order`, `time_function`, `all_sum`, `status`, `created_by`, `created_on`, `pre_row_id`, `modified_by`, `modified_on`) VALUES
(null, 17, 'mchale', 'McHale', '', '1', 1, 5, 0, 0, 'SEC_TO_HOUR_2_DECIMAL_LOGIN', 0, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-08 13:35:38', 17, '6acd9683761b153750db382c1c3694f6', '2016-01-08 13:36:25');

UPDATE `_sql_version` SET `revision`=17, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

INSERT INTO `auth_role_in_group` (`row_id`, `rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    (NULL, '588506584bfbeadb4719b723d7a71b44', '2be26ecf3ea9ced18d79aed403f284a9', '6acd9683761b153750db382c1c3694f6', '2015-11-23 11:20:00');

UPDATE `_sql_version` SET `revision`=18, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

INSERT INTO `state_type_right`(`state_type_id`, `rolegroup_id`)
SELECT 'b859c8b809b6b30ae5d7208dff841dec' as state_type_id, rolegroup_id FROM auth_rolegroup;

UPDATE `state_type` SET `in_use`=1 WHERE `state_type_id`='b859c8b809b6b30ae5d7208dff841dec';

UPDATE `link_at_to_bat` SET state_type_id='b859c8b809b6b30ae5d7208dff841dec' WHERE base_absence_type_id='63a1079c69d79753cd623ee2620687a1';

UPDATE `_sql_version` SET `revision`=19, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE `state_type` SET `in_use`=1 WHERE `state_type_id`='def32968390fb987c823da0cbf7d3bd8';

UPDATE `_sql_version` SET `revision`=20, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE `state_type` SET `in_use`=1 WHERE `state_type_id`='d8fe7db7b99500f7abd159cdfb4a47e6';

UPDATE `_sql_version` SET `revision`=21, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE due_to_payroll SET status = 7 WHERE user_id='mchale' AND due_id IN ('torzsszam');

INSERT INTO `due_to_payroll` (`row_id`, `due_to_payroll_id`, `user_id`, `due_id`, `due_label`, `due_value`, `due_sum`, `due_which`, `due_group`, `due_order`, `due_formula`, `due_view`, `due_due`, `due_code`, `due_other_code`, `due_other_value`, `due_other_order`, `due_null`, `due_time`, `all_sum`, `status`, `created_by`, `created_on`, `pre_row_id`, `modified_by`, `modified_on`) VALUES
(null, 660, 'mchale', 'torzsszam', 'Törzsszám', 'emp_id', NULL, '', 1, 2, NULL, 1, 0, '', '', '', 8, NULL, NULL, NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 13:06:05', 309, '6acd9683761b153750db382c1c3694f6', '2016-05-25 11:34:24');

UPDATE `_sql_version` SET `revision`=22, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/reportMovingWageRight', 'date', 'EMPLOYEE_WITH_FROM_TO', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/reportMovingWageRight', 'group', 'DEFAULT_GROUP_FILTER', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/reportMovingWageRight', 'date', 'EMPLOYEE_WITH_FROM_TO', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/reportMovingWageRight', 'group', 'DEFAULT_GROUP_FILTER', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/reportMovingWageRightDetails', 'date', 'EMPLOYEE_WITH_FROM_TO', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/reportMovingWageRightDetails', 'group', 'DEFAULT_GROUP_FILTER', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/reportMovingWageRightDetails', 'date', 'EMPLOYEE_WITH_FROM_TO', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/reportMovingWageRightDetails', 'group', 'DEFAULT_GROUP_FILTER', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/reportTwentyMinutesBreakWork', 'date', 'EMPLOYEE_WITH_FROM_TO', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/reportTwentyMinutesBreakWork', 'group', 'DEFAULT_GROUP_FILTER', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/reportTwentyMinutesBreakWork', 'date', 'EMPLOYEE_WITH_DATE', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/reportTwentyMinutesBreakWork', 'group', 'DEFAULT_GROUP_FILTER', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/timeSheetMcHale', 'date', 'EMPLOYEE_WITH_YEARMONTH', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/timeSheetMcHale', 'group', 'DEFAULT_GROUP_FILTER', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/timeSheetMcHale', 'date', 'EMPLOYEE_WITH_YEARMONTH', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);
INSERT INTO `search_filter` (`row_id`, `controller_id`, `filter_type`, `filter_id`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES (NULL, 'customers/mchale/timeSheetMcHale', 'group', 'DEFAULT_GROUP_FILTER', NULL, '2', 'system', '2016-10-19 10:10:10', NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=23, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

INSERT INTO `public_holiday` (`row_id`, `holidaydate`, `name_dict_id`, `name`, `type`, `chdate`, `company_id`, `status`, `created_by`, `created_on`, `pre_row_id`) VALUES
(NULL, '2017-01-01', 'public_holiday_hun_newyear', 'Újév', '3', NULL, 'ALL', 2, '', '0000-00-00 00:00:00', NULL),
(NULL, '2017-03-15', 'public_holiday_hun_march_15th', 'Március 15.', '3', NULL, 'ALL', 2, '', '0000-00-00 00:00:00', NULL),
(NULL, '2017-05-01', 'public_holiday_hun_international_workers_day', 'Május 1.', '2', NULL, 'ALL', 2, '', '0000-00-00 00:00:00', NULL),
(NULL, '2017-08-20', 'public_holiday_hun_august_20th', 'Augusztus 20.', '2', NULL, 'ALL', 2, '', '0000-00-00 00:00:00', NULL),
(NULL, '2017-10-23', 'public_holiday_hun_october_23th', 'Október 23.', '2', NULL, 'ALL', 2, '', '0000-00-00 00:00:00', NULL),
(NULL, '2017-11-01', 'public_holiday_hun_all_saints_day', 'Mindenszentek', '3', NULL, 'ALL', 2, '', '0000-00-00 00:00:00', NULL),
(NULL, '2017-12-25', 'public_holiday_hun_christmasday1', 'Karácsony', '2', NULL, 'ALL', 2, '', '0000-00-00 00:00:00', NULL),
(NULL, '2017-12-26', 'public_holiday_hun_christmasday2', 'Karácsony', '3', NULL, 'ALL', 2, '', '0000-00-00 00:00:00', NULL),
(NULL, '2017-04-16', 'public_holiday_hun_easter', 'Húsvét', '2', NULL, 'ALL', 2, '', '0000-00-00 00:00:00', NULL),
(NULL, '2017-04-17', 'public_holiday_hun_easter_monday', 'Húsvét hétfő', '3', NULL, 'ALL', 2, '', '0000-00-00 00:00:00', NULL),
(NULL, '2017-06-04', 'public_holiday_hun_whitsun', 'Pünkösd', '2', NULL, 'ALL', 2, '', '0000-00-00 00:00:00', NULL),
(NULL, '2017-06-05', 'public_holiday_hun_whitsun_monday', 'Pünkösd hétfő', '3', NULL, 'ALL', 2, '', '0000-00-00 00:00:00', NULL)
ON DUPLICATE KEY UPDATE `holidaydate`=VALUES(`holidaydate`);

UPDATE `_sql_version` SET `revision`=24, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE  `sync_config` SET `source_sql`='SELECT DISTINCT MHELY_KOD AS "unit_id", MHELY_KOD AS "old_unit_id", ''1'' AS "company_id", ''ALL'' AS "payroll_id",  MHELY_NEV AS "unit_name", ''1900-01-01'' AS "valid_from", ''2038-01-01'' AS "valid_to", 2 AS "status", ''import'' AS "created_by", TO_CHAR(SYSDATE, ''YYYY-MM-DD HH24:MI:SS'') AS "created_on" FROM MF03.BDG1_MC_DOLG WHERE JOGV_SORSZAM = ''1''' WHERE `sync_process_id`='McHaleSyncUnit';

UPDATE `_sql_version` SET `revision`=25, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE due_to_payroll SET status = 7 WHERE user_id='mchale' AND due_id IN ('muszakpotlek', 'muszakpotlek_du2', 'muszakpotlek_ej');

INSERT INTO `due_to_payroll` (`row_id`, `due_to_payroll_id`, `user_id`, `due_id`, `due_label`, `due_value`, `due_sum`, `due_which`, `due_daily`, `due_group`, `due_order`, `due_formula`, `due_view`, `due_due`, `due_code`, `due_other_code`, `due_other_value`, `due_other_order`, `due_null`, `due_time`, `due_time_function`, `all_sum`, `status`, `created_by`, `created_on`, `pre_row_id`, `modified_by`, `modified_on`) VALUES
(null, 848, 'mchale', 'muszakpotlek_du2', 'Műszakpótlék (18 óra és 22 óra között)', 'ot_du2_ts+wt_du2_ts', 1, 'refer', NULL, NULL, 5, 1, 1, 1, '1601', '', '', 8, NULL, 1, '', NULL, 2, '6acd9683761b153750db382c1c3694f6', '2016-01-11 11:57:33', 306, '6acd9683761b153750db382c1c3694f6', '2017-02-21 17:07:22'),
(null, 849, 'mchale', 'muszakpotlek_ej', 'Műszakpótlék 22 óra és reggel 6 óra között', 'ot_ej_ts+wt_ej_ts', 1, 'refer', NULL, NULL, 5, 1, 1, 1, '1602', '', '', 7, NULL, 1, '', NULL, 2, '6acd9683761b153750db382c1c3694f6', '2017-02-21 17:09:34', NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=26, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = 'ABAKUSZ' WHERE `setting_id` = 'ptr_default_mode';
UPDATE `app_settings` SET `setting_value` = 'MCHALE' WHERE `setting_id` = 'ptr_customer';

UPDATE `_sql_version` SET `revision`=27, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

/*
OK! 300	Fizetett szabadság napok
OK! 301	Betegszabadság napok
OK! 306	GYÁP miatti kieső napok
OK! 308	Üzemi baleset miatti kieső napok
OK! 309	GYED miatti kieső napok
OK! 310	GYES miatti kieső napok
OK! 322	Igazolt fizetés nélküli hiányzás miatti k.napok
OK! 323	Igazolatlan fizetés nélküli hiányzás miatti k.nap
OK! 329	Igazolt fizetett távollét napok
OK! 335	Igazolt fiz. táv. napok /közeli hozzátart. halála
OK! 339	Igazolt fiz. táv. napok /szakszervezet
340	Igazolt fiz. táv. napok /üzemi tanács
341	Igazolt fiz. táv. napok /munkavéd. képv.
342	Igazolt fiz. táv. napok /iskola, tanf., tov.képz.
357	?Betegség napok (valószínű táppénz)
OK! 364	Állásidő napjai
378	Munkaidőkedvezmény napok
OK! 445	Csúsztatás napok

OK! 1402	Rendk.m.végzés 50% munkanapi
OK! 1403	Rendk.m.végzés 100% pihenőnapi
1426	Túlóra munkaidőkeret
OK! 403	Ledolgozott munkaórák
OK! 1601	Műszakpótlék (18 óra és 6 óra között )
*/

TRUNCATE `payroll_transfer_config`;
INSERT INTO `payroll_transfer_config` (`row_id`, `transfer_id`, `content_type`, `content_value`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES

(NULL, '300', 'state_type_id', 'a272f564576d443e7832587126b070aa', NULL, 0, 0, NULL, NULL, 'Fizetett szabadság', 2),
(NULL, '301', 'state_type_id', '3ad610cfce362896dbb4b11858dfae40', NULL, 0, 0, NULL, NULL, 'Betegszabadság', 2),
(NULL, '306', 'state_type_id', 'a8fd428dd18c27257aceb1629c75d637', NULL, 0, 0, NULL, NULL, 'Gyermekápolási táppénz', 2),
(NULL, '308', 'state_type_id', '945e3618f487efc4325204521e49c5f3', NULL, 0, 0, NULL, NULL, 'Üzemi/Munkahelyi baleset', 2),
(NULL, '309', 'state_type_id', 'ddc0e01ba27be8926cc66424a88865c5', NULL, 0, 0, NULL, NULL, 'GYED', 2),
(NULL, '310', 'state_type_id', 'db8d6192d34f21a4b779e0afb1120c42', NULL, 0, 0, NULL, NULL, 'GYES', 2),
(NULL, '322', 'state_type_id', '269b59b4efbbb4ef1d527492dc06cb60', NULL, 0, 0, NULL, NULL, 'Igazolt, de nem fizetett távollét', 2),
(NULL, '323', 'state_type_id', 'def32968390fb987c823da0cbf7d3bd8', NULL, 0, 0, NULL, NULL, 'Igazolatlan távollét', 2),
(NULL, '329', 'state_type_id', 'f4f63ae4dd65cd97ee1f409d8b620806', NULL, 0, 0, NULL, NULL, 'Igazolt, fizetett', 2),
(NULL, '364', 'state_type_id', '1ecfd16854c92211d6278a918038903d', NULL, 0, 0, NULL, NULL, 'Állásidő', 2),
(NULL, '445', 'state_type_id', '29337d9204baca9588942e162d229087', NULL, 0, 0, NULL, NULL, 'Csúsztatás', 2),
(NULL, '335', 'state_type_id', '8c3708a21ae32e17af7da36babb301be', NULL, 0, 0, NULL, NULL, 'Igazolt fiz. táv. napok /közeli hozzátart. halála', 2),
(NULL, '339', 'state_type_id', '355107fe74e5d1bd5844c56e1b98951a', NULL, 0, 0, NULL, NULL, 'Igazolt fiz. táv. napok /szakszervezet', 2),

(NULL, '403', 'inside_type_id', 'wt%%', NULL, 0, 0, NULL, NULL, 'Ledolgozott munkaórák', 2),
(NULL, '1601', 'inside_type_id', NULL, NULL, 0, 0, 'base.`inside_type_id` IN (\"wtdu2\",\"otdu2\",\"otwdu2\",\"wtej\",\"otej\",\"otwej\") AND dt.`work_type` != \'STANDBYJOB\' AND (dt.worktime_sum + dt.base_overtime_sum >= 4 * 3600)', NULL, 'Műszakpótlék', 2),

(NULL, '1402', 'inside_type_id', 'otde', NULL, 0, 0, NULL, NULL, 'Túlóra 50%', 2),
(NULL, '1402', 'inside_type_id', 'otdu%', NULL, 0, 0, NULL, NULL, 'Túlóra 50%', 2),
(NULL, '1402', 'inside_type_id', 'otej', NULL, 0, 0, NULL, NULL, 'Túlóra 50%', 2),

(NULL, '1403', 'inside_type_id', 'otwde', NULL, 0, 0, NULL, NULL, 'Túlóra 100%', 2),
(NULL, '1403', 'inside_type_id', 'otwdu%', NULL, 0, 0, NULL, NULL, 'Túlóra 100%', 2),
(NULL, '1403', 'inside_type_id', 'otwej', NULL, 0, 0, NULL, NULL, 'Túlóra 100%', 2);

UPDATE `_sql_version` SET `revision`=28, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE `payroll_transfer_config` SET `target` = "ABAKUSZ";

UPDATE `_sql_version` SET `revision`=29, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

INSERT INTO `payroll_transfer_config` (`row_id`, `transfer_id`, `content_type`, `content_value`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
(NULL, '300', 'state_type_id', 'c9c203ce94830523b07616850d43172d', NULL, 0, 0, NULL, NULL, 'Gyermekek után járó pótszabadság', 2);

UPDATE `payroll_transfer_config` SET `target` = "ABAKUSZ";

UPDATE `_sql_version` SET `revision`=30, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE `payroll_transfer_config` SET `target` = "MEGAORA";

UPDATE `_sql_version` SET `revision`=31, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = 'MEGAORA' WHERE `setting_id` = 'ptr_default_mode';

UPDATE `_sql_version` SET `revision`=32, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Telefonszám' WHERE `dict_id` = 'option1' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Phone Number' WHERE `dict_id` = 'option1' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Email' WHERE `dict_id` = 'option2' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Email' WHERE `dict_id` = 'option2' AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=33, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

INSERT INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
	('588506584bfbeadb4719b723d7a71b44', '77d65c6558bb497c9fc19e6b4181ea8b', '6acd9683761b153750db382c1c3694f6', NOW()),
	('588506584bfbeadb4719b723d7a71b44', 'bed98a4b955296c86f40237bfae11f0f', '6acd9683761b153750db382c1c3694f6', NOW()),
	('588506584bfbeadb4719b723d7a71b44', '3ec7ba98efc4a394833af5b11e2de711', '6acd9683761b153750db382c1c3694f6', NOW()),
	('588506584bfbeadb4719b723d7a71b44', '8b63e556938a126e78c3e308de25697e', '6acd9683761b153750db382c1c3694f6', NOW());

UPDATE `_sql_version` SET `revision`=34, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION --------------------------------------------------------------------

UPDATE `app_settings`
SET    `setting_value` = '365',
       `note`          = 'prev value: 31',
       `modified_by`   = 'DEV-14350',
       `modified_on`   = NOW()
WHERE  `setting_id`    = 'origRegsRep_max_days';

UPDATE `_sql_version` SET `revision`=35, `updated_on`=NOW() WHERE `module`='c_mchale';

-- VERSION -35-2022-11-16-13:00----------------------------------------------DEV-14350

UPDATE `app_settings` SET `setting_value` = 'OFF', `note` = CONCAT('DEV-14389, prev val: ON_WITHOUTCOST; ',`note`) WHERE `setting_id` = 'summarySheet_rule_regs_no_cause';

UPDATE `_sql_version` SET `revision`=36, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION -36-2022-11-18-13:15------------------------------------------------

UPDATE `app_settings` SET `setting_value`=1 WHERE `setting_id`='summarySheet_calculation_shiftStartInWorkgroup';

UPDATE `_sql_version` SET `revision`=37, `updated_on`=NOW() WHERE `module`='c_mchale';

-- VERSION -37-2022-11-28-14:30------------------------------------------------

UPDATE `app_settings` SET `setting_value` = 'decimal', `note` = 'prev value: string | DEV-7347 default: string', `modified_on` = NOW(), `modified_by`='FR-14471' WHERE `setting_id` = 'overtimeReportTimeFormat';

UPDATE `_sql_version` SET `revision`=38, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION -38-2022-11-28-11:00------------------------------------------------

UPDATE
    `search_filter`
SET
    `filter_id`   = 'EMPLOYEE_WITH_FROM_TO',
    `modified_by` = 'DEV-14472',
    `modified_on` = NOW()
WHERE
    `status`        = 2
AND `controller_id` = 'wfm/reportWhoIsIn'
AND `filter_type`   = 'date';

UPDATE `_sql_version` SET `revision`=39, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION -39-2022-12-01-14:30------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev val: 0, DEV-14550' WHERE `setting_id` = 'employeeReportShowOriginalContractStart';
UPDATE `app_settings` SET `setting_value` = 'employee_id', `note` = 'Prev val: tax_number, DEV-14550' WHERE `setting_id` = 'firstContractFieldEmployeeReport';

UPDATE `_sql_version` SET `revision`=40, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION -40-2022-12-12-15:50------------------------------------------------

INSERT INTO `inside_type` (`inside_type_id`, `shift_type_id`, `dict_id`, `valid_from`, `valid_to`, `status`, `created_by`, `created_on`) VALUES
('working_off_work_break1',NULL,'working_off_work_break1','2000-01-01','2038-01-01',2,'DEV-14538',NOW()),
('working_off_work_break2',NULL,'working_off_work_break2','2000-01-01','2038-01-01',2,'DEV-14538',NOW()),
('working_off_work_break3',NULL,'working_off_work_break3','2000-01-01','2038-01-01',2,'DEV-14538',NOW()),
('working_off_work_break4',NULL,'working_off_work_break4','2000-01-01','2038-01-01',2,'DEV-14538',NOW());

INSERT IGNORE INTO `dictionary` (`row_id`, `lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
(NULL, 'hu', 'ttwa-wfm', 'working_off_work_break1', 'Munkaközi szünet ledolgozás 1 óra', 1),
(NULL, 'en', 'ttwa-wfm', 'working_off_work_break1', 'Working off work break 1 hour', 1),
(NULL, 'hu', 'ttwa-wfm', 'working_off_work_break2', 'Munkaközi szünet ledolgozás 2 óra', 1),
(NULL, 'en', 'ttwa-wfm', 'working_off_work_break2', 'Working off work break 2 hour', 1),
(NULL, 'hu', 'ttwa-wfm', 'working_off_work_break3', 'Munkaközi szünet ledolgozás 3 óra', 1),
(NULL, 'en', 'ttwa-wfm', 'working_off_work_break3', 'Working off work break 3 hour', 1),
(NULL, 'hu', 'ttwa-wfm', 'working_off_work_break4', 'Munkaközi szünet ledolgozás 4 óra', 1),
(NULL, 'en', 'ttwa-wfm', 'working_off_work_break4', 'Working off work break 4 hour', 1),
(NULL, 'hu', 'ttwa-wfm', 'worked_off', 'Ledolgozott', 1),
(NULL, 'en', 'ttwa-wfm', 'worked_off', 'Worked-off', 1),
(NULL, 'hu', 'ttwa-wfm', 'minutes_to_work_off', 'Ledolgozandó', 1),
(NULL, 'en', 'ttwa-wfm', 'minutes_to_work_off', 'Need to work-off', 1),
(NULL, 'hu', 'ttwa-wfm', 'work_off_ratio', 'Ledolgozási arány', 1),
(NULL, 'en', 'ttwa-wfm', 'work_off_ratio', 'Work-off ratio', 1),
(NULL, 'hu', 'ttwa-base', 'in_hours', 'órában', 1),
(NULL, 'en', 'ttwa-base', 'in_hours', 'in hours', 1),
(NULL, 'hu', 'ttwa-base', 'in_minutes', 'percben', 1),
(NULL, 'en', 'ttwa-base', 'in_minutes', 'in minutes', 1);

UPDATE
	`search_filter`
SET
	`filter_id` = 'EMPLOYEE_WITH_YEAR',
	`modified_by` = 'DEV-14539',
	`modified_on` = NOW()
WHERE
	 `controller_id` = 'customers/mchale/reportTwentyMinutesBreakWork'
AND `filter_type` = 'date'
AND `status` = 2;

INSERT INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `status`, `created_by`, `created_on`) VALUES
('customers/mchale/reportTwentyMinutesBreakWork', 'group', 'COMPANY_ORG_GROUP2', 'combo', '2', 'DEV-14539', NOW());

UPDATE `_sql_version` SET `revision`=41, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION -41-2022-12-19-10:00------------------------------------------------

INSERT INTO `auth_acl` (`role_id`, `controller_id`, `column_name`, `operation_id`, `usergroup_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
    ('mchaleAttendanceSheet', 'customers/mchale/attendanceSheet', NULL, 'view', NULL, '1', '1', 'DEV-14590', NOW());

INSERT INTO `auth_role` (`role_id`, `role_name`, `description`, `created_by`, `created_on`) VALUES
    ('mchaleAttendanceSheet', 'customers/mchale/attendanceSheet --- view', 'McHale Munkaidő nyilvántartási lap', 'DEV-14590', NOW());

INSERT INTO `auth_controller` (`controller_id`, `controller_name`, `controller_dict_id`, `created_by`, `created_on`) VALUES
    ('customers/mchale/attendanceSheet', 'AttendanceSheet', 'menu_item_attendanceSheet', 'DEV-14590', NOW());

INSERT INTO `menu_item_table` (`menu_item_id`,`menu_item_name`,`menu_modul`,`menu_label`,`menu_item_css_class`,`menu_url`,`menu_visible`,`menu_visible_operation`,`menu_item_parent_id`,`menu_order`) VALUES
    ('customers/mchale/attendanceSheet', 'customers/mchale/attendanceSheet', 'ttwa-wfm', 'menu_item_attendanceSheet', 'sub', '/customers/mchale/attendanceSheet/index', 'customers/mchale/attendanceSheet', 'view', '82', '205');

INSERT INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
    ('customers/mchale/attendanceSheet', 'date', 'EMPLOYEE_WITH_YEARMONTH', 'combo', '0', '2', 'DEV-14590', NOW()),
    ('customers/mchale/attendanceSheet', 'group', 'DEFAULT_GROUP_FILTER', 'combo', '0', '2',  'DEV-14590', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
  ('hu', 'ttwa-wfm', 'working_off_work_break', 'Munkaközi szünet ledolgozás', 1),
  ('en', 'ttwa-wfm', 'working_off_work_break', 'Working off work break', 1),
  ('hu', 'ttwa-wfm', 'shift_bonus_du', 'Műszakpótlék délután', 1),
  ('en', 'ttwa-wfm', 'shift_bonus_du', 'Shift bonus afternoon', 1),
  ('hu', 'ttwa-wfm', 'shift_bonus_ej', 'Műszakpótlék éjszaka', 1),
  ('en', 'ttwa-wfm', 'shift_bonus_ej', 'Shift bonus night', 1),
  ('hu', 'ttwa-wfm', 'annual_leave_allowance', 'Éves szabadság keret', 1),
  ('en', 'ttwa-wfm', 'annual_leave_allowance', 'Annual leave allowance', 1),
  ('hu', 'ttwa-wfm', 'public_holiday_daily_allowance', 'Munkaszüneti napi pótlék', 1),
  ('en', 'ttwa-wfm', 'public_holiday_daily_allowance', 'Holiday allowance', 1);

UPDATE `_sql_version` SET `revision`=42, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION -42-2023-01-12-08:00------------------------------------------------

UPDATE `app_settings` SET `setting_value` = 'MEGASTAR', `note` = 'Default value, SN-DEV-14536' WHERE `setting_id` = 'ptr_default_mode';
UPDATE `app_settings` SET `setting_value` = 'MCHALE', `note` = 'Default value, SN-DEV-14536' WHERE `setting_id` = 'ptr_customer';
UPDATE `app_settings` SET `setting_value` = '0', `note` = 'Default value, SN-DEV-14536' WHERE `setting_id` = 'ptrCorrectLastMonth';
UPDATE `app_settings` SET `setting_value` = 'employee.emp_id', `note` = 'Default value, SN-DEV-14536' WHERE `setting_id` = 'ptr_payrollEmpId';

UPDATE `payroll_transfer_config` SET `status` = 7;
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
  ('MEGASTAR', 'NAPAPASZ', 'state_type_id', NULL, 'ebf4ac30e7dc238fd2f4bc86332e0675', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Apasági szabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPAPASZ2', 'state_type_id', NULL, '639b1b48715d5', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Apasági szabadság második fele', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPATHSZAB', 'state_type_id', NULL, '8cb3945a0a8b1afa9d064d064584e5d4', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Áthozott szabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPEÉSZ', 'state_type_id', NULL, 'b859c8b809b6b30ae5d7208dff841dec', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Előző évi szabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPFELMENT', 'state_type_id', NULL, '90b9dd9dcbdc86ec8acaad81fd6add2b', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Felmentés mv.alól', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPFELM', 'state_type_id', NULL, 'ad0e81399d06033ad7be1aae70100bcc', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Felmentési idő', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPJUSZAB', 'state_type_id', NULL, '59c3e3db07f06c43cf5bbf440be6b553', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Fiatal munkavállaló pótszabadsága', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPFIGTA', 'state_type_id', NULL, 'f4f63ae4dd65cd97ee1f409d8b620806', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Fizetett igazolt', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPFEIER', 'state_type_id', NULL, 'eb49097c829f3f5140e41e92eae91202', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Fizetett ünnep', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPGONDCT', 'state_type_id', NULL, '639b1d89381f8', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Gondozási célú távollét', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPGYSZAB', 'state_type_id', NULL, 'c9c203ce94830523b07616850d43172d', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Gyermekek után járó pótszabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPKIKULD', 'state_type_id', NULL, 'aad4833e34da5d4a6eb00a13088aeab5', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Hivatalos kiküldetés', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPIGLAN', 'state_type_id', NULL, 'def32968390fb987c823da0cbf7d3bd8', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Igazolatlan távollét', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPNIG', 'state_type_id', NULL, '269b59b4efbbb4ef1d527492dc06cb60', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Igazolt de nem fizetett', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPIG.T', 'state_type_id', NULL, '64af4774b72418f8e11d2eb770a2a7e4', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Igazolt távollét', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPOKTB', 'state_type_id', NULL, 'absence_type_training_in', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Oktatás belső helyszínen', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPI', 'state_type_id', NULL, 'd8fe7db7b99500f7abd159cdfb4a47e6', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Iskolanapon', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'ORAKOMP_NAP', 'state_type_id', NULL, '6384c4e362754', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Kompenzációs nap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPKIKÜLDK', 'state_type_id', NULL, 'absence_type_delegacy_ext', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Külföldi kiküldetés', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPKU', 'state_type_id', NULL, '7829307df38f9d1795a9bfe7e2fb7e25', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Külső munkán', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPOKTK', 'state_type_id', NULL, 'absence_type_training_ext', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Oktatás külső helyszínen', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPSZAB', 'state_type_id', NULL, 'a272f564576d443e7832587126b070aa', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Szabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPSZÜSZAB', 'state_type_id', NULL, '639b1bc6a94f9', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Szülői szabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
  ('MEGASTAR', 'NAPTSZ', 'state_type_id', NULL, 'cffaabff749860f85840377da9f315cf', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Temetési szabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_value`, `null_value`, `select_value`, `select_as`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
	('MEGASTAR', 'DE', 'inside_type_id', NULL, 0, 'IF(data.`wage_type` = \"MoLo\", 1, data.`value_sum_payroll`)', 'new_value_sum_payroll', NULL, 0, 0, NULL, 'data.`inside_type_id` = \"wtde\"', 'Délelőtt', 2),
	('MEGASTAR', 'DUP', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"wtdu2\"', 'Délután pótlékkal', 2),
	('MEGASTAR', 'DUPM', 'inside_type_id', NULL, 0, 'IF(data.`wage_type` = \"MoLo\", 1, data.`value_sum_payroll`)', 'new_value_sum_payroll', NULL, 0, 0, NULL, 'data.`inside_type_id` = \"wtdu1\"', 'Délután pótlékmentes', 2),
	('MEGASTAR', 'ÉJ', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"wtej\"', 'Éjszaka', 2),
	('MEGASTAR', 'TUODE', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"otde\"', 'Fizetett túlóra délelőtt', 2),
	('MEGASTAR', 'TUODUP', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"otdu2\"', 'Fizetett túlóra délután pótlékkal', 2),
	('MEGASTAR', 'TUODUPM', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"otdu1\"', 'Fizetett túlóra délután pótlékmentes', 2),
	('MEGASTAR', 'TUOÉJ', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"otej\"', 'Fizetett túlóra éjszaka', 2),
	('MEGASTAR', 'PIHTUODE', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"otwde\"', 'Pihenőnapi fizetett túlóra délelőtt', 2),
	('MEGASTAR', 'PIHTUOÉJ', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"otwej\"', 'Pihenőnapi fizetett túlóra éjszaka', 2),
	('MEGASTAR', 'PIHTUODUP', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"otwdu2\"', 'Pihenőnapi fizetett túlóra délután pótlékkal', 2),
	('MEGASTAR', 'PIHTUODUPM', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"otwdu1\"', 'Pihenőnapi fizetett túlóra délután pótlékmentes', 2);

UPDATE `_sql_version` SET `revision`=43, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION -43-2023-01-16-13:00------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_value`, `null_value`, `select_value`, `select_as`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
	('MEGASTAR', 'SZABTULH', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_absredempt_workday\"', 'Szabadidővel megváltott túlóra hétköznap', 2),
	('MEGASTAR', 'SZABTULSZAB', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_absredempt_restday\"', 'Szabadidővel megváltott túlóra szabadnap', 2),
	('MEGASTAR', 'SZABTULÜ', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"absence_type_absredempt_holiday\"', 'Szabadidővel megváltott túlóra ünnepnap', 2);

UPDATE `_sql_version` SET `revision`=44, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION -44-2023-02-03-11:00------------------------------------------------

UPDATE
    `employee_contract`
LEFT JOIN `employee`
    ON `employee_contract`.`employee_id` = `employee`.`employee_id`
    AND `employee`.`status` = 2
SET
    `employee_contract`.`wage_type` = (
        CASE
            WHEN `employee`.`payroll_id` = '2' THEN 'MoLo'
            WHEN `employee`.`payroll_id` = '3' THEN 'OB'
        END
    ),
    `employee_contract`.`modified_by` = 'DEV-14962',
    `employee_contract`.`modified_on` = NOW()
WHERE
    `employee`.`payroll_id` IN('2', '3')
    AND `employee`.`valid_from` = `employee_contract`.`valid_from`
    AND `employee`.`valid_to` = `employee_contract`.`valid_to`
    AND `employee_contract`.`status` = 2;

UPDATE `_sql_version` SET `revision`=45, `updated_on`=NOW() WHERE `module`='c_mchale';

-- VERSION -45-2023-02-03-15:00----------------------------------------------DEV-14962

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('DE', 'DUPM');
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_value`, `null_value`, `select_value`, `select_as`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
	('MEGASTAR', 'DE', 'inside_type_id', NULL, 0, 'IF(data.`wage_type` = \"MoLo\", 1, data.`value_sum_payroll`)', 'new_value_sum_payroll', NULL, 0, 0, NULL, 'data.`inside_type_id` = \"wtde\" OR data.`inside_type_id` = \"wtdu1\" OR (data.`wage_type` = \"MoLo\" AND data.`state_type_id` = \"29337d9204baca9588942e162d229087\")', 'Délelőtt - délután pótlékmentes - csúsztat', 2);

UPDATE `_sql_version` SET `revision`=46, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION -46-2023-02-10-15:15------------------------------------------------

UPDATE `app_settings` SET `setting_value` = 1, `note` = 'prev val: 0, Összesítőlap kimutatás export érték 2 decimálisba', `modified_by` = 'DEV-15097', `modified_on` = NOW() WHERE `setting_id` = 'wfmEmployeeCalcRep2Decimal';

UPDATE `_sql_version` SET `revision`=47, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION -47-2023-02-22-11:00------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_value`, `null_value`, `select_value`, `select_as`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
	('MEGASTAR', 'OKTBO', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"64269819be691\"', 'Oktatás belső helyszínen órában', 2),
	('MEGASTAR', 'OKTKO', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"64269846561f9\"', 'Oktatás külső helyszínen órában', 2),
	('MEGASTAR', 'SZABTULVAS', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"64269349b5da3\"', 'Szabadidővel megváltott túlóra vasárnap', 2),
	('MEGASTAR', 'ENFO', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"642693d0ecb3f\"', 'Egyéb nem fizetett órák', 2),
	('MEGASTAR', 'IGLANO', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"6426965cf3c14\"', 'Igazolatlan távollét órában', 2),
	('MEGASTAR', 'NIGO', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"6426977a91e6a\"', 'Igazolt de nem fizetett órában', 2),
	('MEGASTAR', 'IG.TO', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` = \"642697b6beb29\"', 'Igazolt távollét órában', 2);

UPDATE `_sql_version` SET `revision`=48, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION -48-2023-04-04-10:00------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '0', `note` = 'DEV-15472, prev val: 1' WHERE `setting_id` = 'sumsheet_use_prevday_inside_types';

UPDATE `_sql_version` SET `revision`=49, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION -49-2023-04-12-16:00------------------------------------------------

UPDATE `app_settings` SET `setting_value` = 'a272f564576d443e7832587126b070aa;b859c8b809b6b30ae5d7208dff841dec', `note` = 'prev val: a272f564576d443e7832587126b070aa', `modified_by` = 'DEV-15646', `modified_on` = NOW()  WHERE `setting_id` = 'absenceCondition';

UPDATE `_sql_version` SET `revision`=50, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION -50-2023-04-12-16:00------------------------------------------------

UPDATE `state_type` SET `worktime` = 1, `modified_by` = 'SZOF-2530', `modified_on` = NOW() WHERE `state_type_id` = '29337d9204baca9588942e162d229087' AND `status` = 2;

UPDATE `_sql_version` SET `revision`=51, `updated_on`=NOW() WHERE `module` = 'c_mchale';

-- VERSION -51-2024-01-29-08:40------------------------------------------------
