<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Handler;

use LoginAutonom\CoreBundle\Interfaces\CommandBusInterface;
use LoginAutonom\DatabaseBundle\DTO\EntityPersistRequest;
use LoginAutonom\DatabaseBundle\Event\Command\PersistEntitiesCommand;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class PersistEntitiesWithContextHandler implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    private const DEFAULT_CHUNK_SIZE = 100;

    public function __construct(
        private readonly EntityPersistRequestHandler $entityPersistRequestHandler,
        private readonly CommandBusInterface $commandBus,
        private readonly TransactionHandler $transactionHandler,
        private readonly int $chunkSize = self::DEFAULT_CHUNK_SIZE,
    ) {
    }

    public function handle(array $entities, array $entityContextMap, array $persistReasons): void
    {
        if (empty($entities)) {
            return;
        }

        $this->logger->info('Starting entity persistence with context', [
            'entity_count' => count($entities),
            'chunk_size' => $this->chunkSize,
        ]);

        $this->handleEntityPersistRequests($entities, $entityContextMap, $persistReasons);

        $this->persistEntitiesGroupedByClass($entities);

        $this->logger->info('Completed entity persistence with context');
    }

    private function handleEntityPersistRequests(array $entities, array $entityContextMap, array $persistReasons): void
    {
        foreach ($entities as $entityHash => $entity) {
            $reason = $persistReasons[get_class($entity)];
            $entityPersistRequest = new EntityPersistRequest($entity, $reason);
            if (isset($entityContextMap[$entityHash])) {
                $entityPersistRequest->setEntityPersistContext(
                    $entityContextMap[$entityHash]
                );
            }

            $this->entityPersistRequestHandler->handle($entityPersistRequest);
        }
    }

    private function persistEntitiesGroupedByClass(array $entities): void
    {
        $entitiesByClass = [];
        array_walk($entities, function ($entity) use (&$entitiesByClass) {
            $entitiesByClass[get_class($entity)][] = $entity;
        });

        $this->logger->info('Grouped entities by FQCN', [
            'types' => array_keys($entitiesByClass),
            'type_counts' => array_map('count', $entitiesByClass),
        ]);

        foreach ($entitiesByClass as $entitiesOfType) {
            $chunks = array_chunk($entitiesOfType, $this->chunkSize);

            foreach ($chunks as $chunkIndex => $chunk) {
                $this->persistEntitiesChunk($chunk, $chunkIndex + 1);
            }
        }
    }

    private function persistEntitiesChunk(array $entitiesChunk, int $chunkNumber): void
    {
        $logData = [
            'chunk_number' => $chunkNumber,
            'chunk_size' => count($entitiesChunk),
        ];

        $this->logger->info('Persisting entities chunk', $logData);

        $this->commandBus->send(
            new PersistEntitiesCommand($entitiesChunk)
        );

        $this->transactionHandler->commitTransaction();
        $this->transactionHandler->startTransaction();
    }
}
